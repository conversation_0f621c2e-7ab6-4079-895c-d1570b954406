# Server Configuration
PORT=3001
JWT_SECRET=your_jwt_secret_here

# Database Configuration
DATABASE_URL="postgresql://USER:PASSWORD@localhost:5432/myzone-mindset-db?schema=public"

# Replicate API for Kokoro TTS
REPLICATE_API_KEY=your_replicate_api_key_here
REPLICATE_KOKORO_MODEL="jaaari/kokoro-82m:f559560eb822dc509045f3921a1921234918b91739db4bf3daab2169b71c7a13"

# AWS Configuration for S3 Storage
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET=your_s3_bucket_name

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
EMAIL_FROM=<EMAIL>

# Frontend URL for password reset links
FRONTEND_URL=http://localhost:5173