-- CreateE<PERSON>
CREATE TYPE "PeriodizationStatus" AS ENUM ('ACTIVE', 'COMPLETED', 'CANCELLED');

-- CreateTable
CREATE TABLE "periodizations" (
    "id" TEXT NOT NULL,
    "coachee_id" TEXT NOT NULL,
    "coach_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "off_season_start_date" TIMESTAMP(3) NOT NULL,
    "off_season_end_date" TIMESTAMP(3) NOT NULL,
    "prep_start_date" TIMESTAMP(3) NOT NULL,
    "prep_end_date" TIMESTAMP(3) NOT NULL,
    "pre_comp_start_date" TIMESTAMP(3) NOT NULL,
    "pre_comp_end_date" TIMESTAMP(3) NOT NULL,
    "competition_start_date" TIMESTAMP(3) NOT NULL,
    "competition_end_date" TIMESTAMP(3) NOT NULL,
    "schedule_mental_toughness" BOOLEAN NOT NULL DEFAULT true,
    "schedule_mental_wellness" BOOLEAN NOT NULL DEFAULT true,
    "mental_wellness_start_date" TIMESTAMP(3),
    "mental_wellness_end_date" TIMESTAMP(3),
    "status" "PeriodizationStatus" NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "periodizations_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "periodizations" ADD CONSTRAINT "periodizations_coachee_id_fkey" FOREIGN KEY ("coachee_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "periodizations" ADD CONSTRAINT "periodizations_coach_id_fkey" FOREIGN KEY ("coach_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
