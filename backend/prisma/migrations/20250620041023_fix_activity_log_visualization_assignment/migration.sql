/*
  Warnings:

  - You are about to drop the column `visualization_id` on the `activity_logs` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "activity_logs" DROP CONSTRAINT "activity_logs_visualization_id_fkey";

-- AlterTable
ALTER TABLE "activity_logs" DROP COLUMN "visualization_id",
ADD COLUMN     "visualizationId" TEXT,
ADD COLUMN     "visualization_assignment_id" TEXT;

-- AddForeignKey
ALTER TABLE "activity_logs" ADD CONSTRAINT "activity_logs_visualization_assignment_id_fkey" FOREIGN KEY ("visualization_assignment_id") REFERENCES "visualization_assignments"("id") ON DELETE SET NULL ON UPDATE CASCADE;
