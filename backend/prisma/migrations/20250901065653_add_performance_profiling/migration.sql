-- CreateEnum
CREATE TYPE "PerformanceCategory" AS ENUM ('PHYSICAL', 'TECHNICAL', 'TACTICAL', 'MENTAL');

-- CreateTable
CREATE TABLE "performance_profiles" (
    "id" TEXT NOT NULL,
    "coachee_id" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "target_date" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "performance_profiles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "performance_profile_goals" (
    "id" TEXT NOT NULL,
    "performance_profile_id" TEXT NOT NULL,
    "category" "PerformanceCategory" NOT NULL,
    "goal_name" TEXT NOT NULL,
    "current_rating" INTEGER NOT NULL,
    "target_rating" INTEGER NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "performance_profile_goals_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "performance_profiles" ADD CONSTRAINT "performance_profiles_coachee_id_fkey" FOREIGN KEY ("coachee_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "performance_profile_goals" ADD CONSTRAINT "performance_profile_goals_performance_profile_id_fkey" FOREIGN KEY ("performance_profile_id") REFERENCES "performance_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
