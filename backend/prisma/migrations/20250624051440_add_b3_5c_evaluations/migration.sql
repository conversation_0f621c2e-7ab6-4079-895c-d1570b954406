-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "ActivityEventType" ADD VALUE 'EVALUATION_CREATED';
ALTER TYPE "ActivityEventType" ADD VALUE 'EVALUATION_UPDATED';

-- CreateTable
CREATE TABLE "coachee_evaluations" (
    "id" TEXT NOT NULL,
    "coachee_id" TEXT NOT NULL,
    "coach_id" TEXT NOT NULL,
    "title" TEXT,
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "composure" INTEGER NOT NULL,
    "concentration" INTEGER NOT NULL,
    "confidence" INTEGER NOT NULL,
    "copeability" INTEGER NOT NULL,
    "cohesion" INTEGER NOT NULL,

    CONSTRAINT "coachee_evaluations_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "coachee_evaluations" ADD CONSTRAINT "coachee_evaluations_coachee_id_fkey" FOREIGN KEY ("coachee_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coachee_evaluations" ADD CONSTRAINT "coachee_evaluations_coach_id_fkey" FOREIGN KEY ("coach_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
