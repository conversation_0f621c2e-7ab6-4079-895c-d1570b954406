-- CreateTable
CREATE TABLE "ideal_performance_states" (
    "id" TEXT NOT NULL,
    "coachee_id" TEXT NOT NULL,
    "date_time" TIMESTAMP(3) NOT NULL,
    "competition_date_time" TIMESTAMP(3) NOT NULL,
    "competition_name" TEXT NOT NULL,
    "performance_score" INTEGER NOT NULL,
    "arousal_score" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ideal_performance_states_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ideal_performance_states" ADD CONSTRAINT "ideal_performance_states_coachee_id_fkey" FOREIGN KEY ("coachee_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
