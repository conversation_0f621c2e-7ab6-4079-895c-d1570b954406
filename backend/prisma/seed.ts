import { PrismaClient, UserRole } from "@prisma/client";
import * as bcrypt from "bcrypt";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting database seeding...");

  // Clear existing data in the correct order (respecting foreign key constraints)
  console.log("🧹 Clearing existing data...");

  await prisma.activityLog.deleteMany({});
  console.log("  ✅ Cleared activity logs");

  await prisma.assignmentSubmission.deleteMany({});
  console.log("  ✅ Cleared assignment submissions");

  await prisma.assignment.deleteMany({});
  console.log("  ✅ Cleared assignments");

  await prisma.visualizationAssignment.deleteMany({});
  console.log("  ✅ Cleared visualization assignments");

  await prisma.exercise.deleteMany({});
  console.log("  ✅ Cleared exercises");

  await prisma.visualization.deleteMany({});
  console.log("  ✅ Cleared visualizations");

  await prisma.coachCoachee.deleteMany({});
  console.log("  ✅ Cleared coach-coachee relationships");

  await prisma.organizationCoachee.deleteMany({});
  console.log("  ✅ Cleared organization-coachee relationships");

  await prisma.organizationCoach.deleteMany({});
  console.log("  ✅ Cleared organization-coach relationships");

  await prisma.organization.deleteMany({});
  console.log("  ✅ Cleared organizations");

  await prisma.user.deleteMany({});
  console.log("  ✅ Cleared users");

  console.log("🧹 Database cleared successfully!");
  console.log("");
  console.log("🌱 Creating fresh seed data...");

  // Create admin user
  const adminPassword = await bcrypt.hash("admin123!", 10);
  const admin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      passwordHash: adminPassword,
      role: UserRole.ADMIN,
      firstName: "System",
      lastName: "Administrator",
    },
  });

  console.log("✅ Created admin user:", admin.email);

  // Create sample HR admin
  const hrPassword = await bcrypt.hash("hr123!", 10);
  const hrAdmin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      passwordHash: hrPassword,
      role: UserRole.HR_ADMIN,
      firstName: "HR",
      lastName: "Administrator",
    },
  });

  console.log("✅ Created HR admin user:", hrAdmin.email);

  // Create sample organization
  const organization = await prisma.organization.upsert({
    where: { id: "sample-org-id" },
    update: {},
    create: {
      id: "sample-org-id",
      name: "Sample Sports Organization",
      hrAdminId: hrAdmin.id,
    },
  });

  console.log("✅ Created organization:", organization.name);

  // Create sample coach
  const coachPassword = await bcrypt.hash("coach123!", 10);
  const coach = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      passwordHash: coachPassword,
      role: UserRole.COACH,
      firstName: "John",
      lastName: "Coach",
    },
  });

  console.log("✅ Created coach user:", coach.email);

  // Create sample coachee
  const coacheePassword = await bcrypt.hash("coachee123!", 10);
  const coachee = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      passwordHash: coacheePassword,
      role: UserRole.COACHEE,
      firstName: "Jane",
      lastName: "Athlete",
    },
  });

  console.log("✅ Created coachee user:", coachee.email);

  // Create coach-coachee relationship
  await prisma.coachCoachee.upsert({
    where: {
      coachId_coacheeId: {
        coachId: coach.id,
        coacheeId: coachee.id,
      },
    },
    update: {},
    create: {
      coachId: coach.id,
      coacheeId: coachee.id,
      coachNotes: "Initial coaching relationship established.",
    },
  });

  console.log("✅ Created coach-coachee relationship");

  // Add coach and coachee to organization
  await prisma.organizationCoach.upsert({
    where: {
      organizationId_coachId: {
        organizationId: organization.id,
        coachId: coach.id,
      },
    },
    update: {},
    create: {
      organizationId: organization.id,
      coachId: coach.id,
    },
  });

  await prisma.organizationCoachee.upsert({
    where: {
      organizationId_coacheeId: {
        organizationId: organization.id,
        coacheeId: coachee.id,
      },
    },
    update: {},
    create: {
      organizationId: organization.id,
      coacheeId: coachee.id,
    },
  });

  console.log("✅ Added users to organization");

  // Create sample exercise
  const exercise = await prisma.exercise.upsert({
    where: { id: "sample-exercise-id" },
    update: {},
    create: {
      id: "sample-exercise-id",
      name: "Goal Setting Exercise",
      description:
        "A comprehensive exercise to help athletes set and achieve their goals.",
      questions: [
        {
          id: "q1",
          type: "free_text",
          prompt: "What is your primary athletic goal for this season?",
          description: "Be specific about what you want to achieve and when.",
          required: true,
          placeholder:
            "e.g., Improve my 5K time by 30 seconds before the championship",
          maxLength: 500,
        },
        {
          id: "q2",
          type: "likert",
          prompt: "How confident are you in achieving this goal?",
          description: "Rate your current confidence level honestly.",
          required: true,
          responseMode: "single",
          scaleMin: 1,
          scaleMax: 5,
          labels: {
            1: "Not confident at all",
            2: "Slightly confident",
            3: "Moderately confident",
            4: "Very confident",
            5: "Extremely confident",
          },
        },
        {
          id: "q3",
          type: "task",
          prompt: "Create an action plan for achieving your goal",
          description:
            "Check this box when you have written down 3 specific actions you will take.",
          required: true,
          isCompleted: false,
        },
        {
          id: "q4",
          type: "table",
          prompt: "Weekly training schedule",
          description:
            "Plan your training activities for each day of the week.",
          required: false,
          rowMode: "fixed",
          fixedRows: 7,
          columns: [
            {
              id: "day",
              header: "Day",
              columnType: "free_text",
              placeholder: "e.g., Monday",
            },
            {
              id: "activity",
              header: "Training Activity",
              columnType: "free_text",
              placeholder: "e.g., 5K run, strength training",
            },
            {
              id: "intensity",
              header: "Intensity Level",
              columnType: "likert",
              scaleMin: 1,
              scaleMax: 5,
              labels: {
                1: "Very Light",
                3: "Moderate",
                5: "Very Hard",
              },
            },
          ],
        },
        {
          id: "q5",
          type: "graph_2d",
          prompt: "Track your progress over time",
          description:
            "Plot your expected performance improvement over the next 12 weeks.",
          required: false,
          xAxisLabel: "Weeks",
          yAxisLabel: "Performance Level (1-10)",
          xAxisMin: 0,
          xAxisMax: 12,
          yAxisMin: 1,
          yAxisMax: 10,
        },
      ],
      createdBy: admin.id,
      updatedBy: admin.id,
    },
  });

  console.log("✅ Created sample exercise:", exercise.name);

  // Create sample visualization
  const visualization = await prisma.visualization.upsert({
    where: { id: "sample-visualization-id" },
    update: {},
    create: {
      id: "sample-visualization-id",
      title: "Pre-Competition Visualization",
      description:
        "Close your eyes and imagine yourself at the starting line. Feel the energy of the crowd, the anticipation in your muscles. See yourself executing your technique perfectly, moving with confidence and precision. Visualize crossing the finish line with a sense of accomplishment and pride.",
      createdBy: admin.id,
      updatedBy: admin.id,
    },
  });

  console.log("✅ Created sample visualization:", visualization.title);

  console.log("🎉 Database seeding completed successfully!");
  console.log("\n📋 Sample accounts created:");
  console.log("Admin: <EMAIL> / admin123!");
  console.log("HR Admin: <EMAIL> / hr123!");
  console.log("Coach: <EMAIL> / coach123!");
  console.log("Coachee: <EMAIL> / coachee123!");
}

main()
  .catch((e) => {
    console.error("❌ Error during seeding:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
