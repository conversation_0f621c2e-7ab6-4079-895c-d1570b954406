# MyZone Mindset Backend API

This is the backend API for the MyZone Mindset application, built with Node.js, Express, TypeScript, and Prisma ORM connecting to a PostgreSQL database.

## Prerequisites

- Node.js (v18+ recommended)
- npm or yarn
- PostgreSQL database running
- Docker (optional, for alternative database setup)
- SMTP server access for sending emails

## Setup

1.  **Clone the repository** (if not already done).
2.  **Navigate to the `backend` directory:**
    ```bash
    cd backend
    ```
3.  **Install dependencies:**
    ```bash
    npm install
    # or
    # yarn install
    ```
4.  **Set up environment variables:**

    - Create a `.env` file in the `backend` directory by copying the example:
      ```bash
      cp .env.example .env
      ```
    - Update the `.env` file with your:
      - `DATABASE_URL`: Your PostgreSQL connection string (e.g., `postgresql://USER:PASSWORD@HOST:PORT/DATABASE?schema=public`)
      - `JWT_SECRET`: A long, random, secret string for signing authentication tokens
      - `PORT` (optional): The port the server should run on (defaults to 3001)
      - Email settings:
        - `SMTP_HOST`: Your SMTP server host
        - `SMTP_PORT`: SMTP port (usually 587 or 465)
        - `SMTP_SECURE`: Use TLS (true/false)
        - `SMTP_USER`: SMTP username/email
        - `SMTP_PASSWORD`: SMTP password
        - `EMAIL_FROM`: The email address to send from
        - `FRONTEND_URL`: URL of the frontend application

5.  **Run database migrations:**
    - Ensure your PostgreSQL server is running and the database specified in `.env` exists.
    - Apply migrations and generate Prisma Client:
      ```bash
      npx prisma migrate dev
      ```
    - _(Optional)_ If you need to seed the database, create a seed script (e.g., `prisma/seed.ts`) and run:
      ```bash
      npx prisma db seed
      ```

## Running the Application

- **Development Mode (with hot-reloading):**

  ```bash
  npm run dev
  # or
  # yarn dev
  ```

  The server will restart automatically when you make changes to the code.

- **Production Mode:**
  1.  Build the TypeScript code:
      ```bash
      npm run build
      # or
      # yarn build
      ```
  2.  Start the server:
      ```bash
      npm start
      # or
      # yarn start
      ```

## API Endpoints

- `GET /`: Basic health check.
- `POST /api/auth/register`: Register a new user.
  - Body: `{ email, password, firstName?, lastName?, role? }`
- `POST /api/auth/login`: Log in an existing user.
  - Body: `{ email, password }`
  - Returns: `{ user, token }`
- `POST /api/auth/request-reset`: Request a password reset.
  - Body: `{ email }`
  - Sends a reset link via email if the account exists
- `POST /api/auth/reset-password`: Reset password using a token.
  - Body: `{ token, newPassword }`
  - The token is sent via email in the reset link

## Project Structure

- `prisma/`: Contains Prisma schema (`schema.prisma`), migrations, and (optional) seed scripts.
- `src/`: Contains the application source code.
  - `config/`: Environment variable configuration.
  - `controllers/`: Request handling logic.
  - `middleware/`: Express middleware (e.g., authentication).
  - `routes/`: API route definitions.
  - `utils/`: Utility functions (e.g., hashing, email).
  - `prisma.ts`: Prisma client initialization.
  - `server.ts`: Main Express application setup and entry point.
- `dist/`: Compiled JavaScript output (generated by `npm run build`).
- `.env`: Environment variables (ignored by git).
- `.env.example`: Example environment variables.
- `.gitignore`: Specifies intentionally untracked files that Git should ignore.
- `package.json`: Project metadata and dependencies.
- `tsconfig.json`: TypeScript compiler options.
