# RDS Deployment Checklist

Use this checklist to ensure a smooth deployment to AWS RDS.

## Pre-Deployment

- [ ] AWS account set up with appropriate permissions
- [ ] AWS CLI installed and configured
- [ ] Strong password generated for database
- [ ] Backup of local database (if migrating existing data)

## RDS Instance Creation

- [ ] RDS PostgreSQL instance created
- [ ] Instance identifier: `myzone-mindset-db`
- [ ] Database name: `myzone_mindset`
- [ ] Master username: `postgres`
- [ ] Strong password set
- [ ] Appropriate instance class selected (db.t3.micro for free tier)
- [ ] Storage configured (20GB minimum)
- [ ] Backup retention period set (7 days recommended)
- [ ] Encryption enabled

## Security Configuration

- [ ] Security group created/configured
- [ ] Port 5432 opened for necessary sources
- [ ] Public accessibility configured as needed
- [ ] SSL/TLS encryption enforced
- [ ] Network access restricted to application servers

## Environment Configuration

- [ ] `.env.production` file created
- [ ] DATABASE_URL updated with RDS endpoint
- [ ] SSL mode set to `require`
- [ ] All environment variables configured
- [ ] Secrets stored securely (consider A<PERSON> Secrets Manager)

## Database Deployment

- [ ] Connection tested: `npm run db:test`
- [ ] Prisma client generated: `npm run db:generate`
- [ ] Schema deployed: `npm run db:push`
- [ ] Initial data seeded: `npm run db:seed`
- [ ] Tables verified in database

## Application Configuration

- [ ] Backend application updated to use RDS
- [ ] Connection pooling configured
- [ ] Error handling for database connections
- [ ] Health checks implemented
- [ ] Logging configured for database operations

## Monitoring and Alerts

- [ ] CloudWatch monitoring enabled
- [ ] CPU utilization alerts set up
- [ ] Storage space alerts configured
- [ ] Connection count monitoring
- [ ] Backup success/failure notifications

## Testing

- [ ] Database connection successful
- [ ] All CRUD operations working
- [ ] Authentication/authorization working
- [ ] Performance acceptable
- [ ] Error handling tested

## Documentation

- [ ] RDS endpoint documented
- [ ] Access procedures documented
- [ ] Backup/restore procedures documented
- [ ] Troubleshooting guide created
- [ ] Team access configured

## Post-Deployment

- [ ] Remove local database dependencies
- [ ] Update CI/CD pipelines
- [ ] Schedule regular backups
- [ ] Plan for scaling
- [ ] Monitor costs
- [ ] Review security settings

## Emergency Procedures

- [ ] Backup restoration tested
- [ ] Failover procedures documented
- [ ] Emergency contacts identified
- [ ] Rollback plan prepared

---

## Quick Commands Reference

```bash
# Test RDS connection
npm run db:test

# Deploy to RDS
npm run db:deploy

# Manual deployment steps
export $(cat .env.production | grep -v '^#' | xargs)
npx prisma generate
npx prisma db push
npx prisma db seed

# Check RDS status
aws rds describe-db-instances --db-instance-identifier myzone-mindset-db

# Create manual backup
aws rds create-db-snapshot --db-instance-identifier myzone-mindset-db --db-snapshot-identifier backup-$(date +%Y%m%d)
```

## Troubleshooting

**Connection Issues:**
1. Check security groups
2. Verify RDS instance status
3. Test network connectivity
4. Validate credentials

**Performance Issues:**
1. Monitor CloudWatch metrics
2. Check for slow queries
3. Consider instance scaling
4. Optimize database indexes

**Cost Management:**
1. Monitor RDS costs in AWS Billing
2. Use appropriate instance sizes
3. Optimize storage allocation
4. Consider Reserved Instances for production
