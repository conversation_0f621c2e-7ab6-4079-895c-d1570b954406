{
  "compilerOptions": {
    "target": "ES2016",                     /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */
    "module": "CommonJS",                   /* Specify what module code is generated. */
    "outDir": "./dist",                     /* Specify an output folder for all emitted files. */
    "rootDir": "./src",                     /* Specify the root folder within your source files. */
    "strict": true,                         /* Enable all strict type-checking options. */
    "esModuleInterop": true,                /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */
    "skipLibCheck": true,                   /* Skip type checking all .d.ts files. */
    "forceConsistentCasingInFileNames": true, /* Ensure that casing is correct in imports. */
    "resolveJsonModule": true               /* Enable importing .json files */
  },
  "include": ["src/**/*"],                  /* Specifies which files to include in compilation. */
  "exclude": ["node_modules", "**/*.spec.ts"] /* Specifies which files to exclude from compilation. */
}
