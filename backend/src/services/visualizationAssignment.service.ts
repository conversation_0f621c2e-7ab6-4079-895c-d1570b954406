import { PrismaClient, VisualizationAssignmentStatus } from "@prisma/client";
import { ActivityLogService } from "./activityLog.service";

const prisma = new PrismaClient();

export interface CreateVisualizationAssignmentData {
  visualizationId: string;
  coacheeId: string;
  coachId: string;
  dueDate?: Date;
}

export interface UpdateVisualizationAssignmentData {
  status?: VisualizationAssignmentStatus;
  dueDate?: Date;
  viewedAt?: Date;
  completedAt?: Date;
}

export interface VisualizationAssignmentFilters {
  coacheeId?: string;
  coachId?: string;
  status?: VisualizationAssignmentStatus;
  visualizationId?: string;
}

export class VisualizationAssignmentService {
  /**
   * Create a new visualization assignment
   */
  static async createVisualizationAssignment(
    data: CreateVisualizationAssignmentData
  ) {
    try {
      // First, validate that the visualization exists and has a title
      const visualization = await prisma.visualization.findUnique({
        where: { id: data.visualizationId },
        select: { id: true, title: true },
      });

      if (!visualization) {
        throw new Error("Visualization not found");
      }

      if (!visualization.title || visualization.title.trim() === "") {
        throw new Error("Visualization must have a title");
      }

      // Validate that the coachee exists
      const coachee = await prisma.user.findUnique({
        where: { id: data.coacheeId },
        select: { id: true, firstName: true, lastName: true },
      });

      if (!coachee) {
        throw new Error("Coachee not found");
      }

      const assignment = await prisma.visualizationAssignment.create({
        data: {
          visualizationId: data.visualizationId,
          coacheeId: data.coacheeId,
          coachId: data.coachId,
          dueDate: data.dueDate,
          status: VisualizationAssignmentStatus.PENDING,
        },
        include: {
          visualization: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Log the visualization assignment creation activity
      try {
        // Ensure we have all required data for the log message
        const visualizationTitle =
          assignment.visualization?.title || "Untitled Visualization";
        const coacheeFirstName = assignment.coachee?.firstName || "Unknown";
        const coacheeLastName = assignment.coachee?.lastName || "User";

        await ActivityLogService.createActivityLog({
          coachId: data.coachId,
          coacheeId: data.coacheeId,
          visualizationAssignmentId: assignment.id,
          eventType: "VISUALIZATION_ASSIGNED",
          eventMessage: `Visualization "${visualizationTitle}" assigned to ${coacheeFirstName} ${coacheeLastName}`,
        });
      } catch (logError) {
        console.error(
          "Failed to log visualization assignment creation activity:",
          logError
        );
        // Don't fail the assignment creation if logging fails
      }

      return assignment;
    } catch (error) {
      console.error("Error creating visualization assignment:", error);
      throw new Error("Failed to create visualization assignment");
    }
  }

  /**
   * Get visualization assignments with filters
   */
  static async getVisualizationAssignments(
    filters: VisualizationAssignmentFilters = {}
  ) {
    try {
      const where: any = {};

      if (filters.coacheeId) {
        where.coacheeId = filters.coacheeId;
      }

      if (filters.coachId) {
        where.coachId = filters.coachId;
      }

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.visualizationId) {
        where.visualizationId = filters.visualizationId;
      }

      const assignments = await prisma.visualizationAssignment.findMany({
        where,
        include: {
          visualization: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return assignments;
    } catch (error) {
      console.error("Error fetching visualization assignments:", error);
      throw new Error("Failed to fetch visualization assignments");
    }
  }

  /**
   * Get visualization assignment by ID
   */
  static async getVisualizationAssignmentById(id: string) {
    try {
      const assignment = await prisma.visualizationAssignment.findUnique({
        where: { id },
        include: {
          visualization: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      return assignment;
    } catch (error) {
      console.error("Error fetching visualization assignment:", error);
      throw new Error("Failed to fetch visualization assignment");
    }
  }

  /**
   * Update visualization assignment status
   */
  static async updateVisualizationAssignmentStatus(
    id: string,
    status: VisualizationAssignmentStatus,
    userId?: string
  ) {
    try {
      const updateData: UpdateVisualizationAssignmentData = { status };

      if (status === VisualizationAssignmentStatus.VIEWED) {
        updateData.viewedAt = new Date();
      } else if (status === VisualizationAssignmentStatus.COMPLETED) {
        updateData.completedAt = new Date();
      }

      const assignment = await prisma.visualizationAssignment.update({
        where: { id },
        data: updateData,
        include: {
          visualization: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Log the visualization completion activity if status is COMPLETED
      if (status === VisualizationAssignmentStatus.COMPLETED) {
        try {
          // Ensure we have all required data for the log message
          const visualizationTitle =
            assignment.visualization?.title || "Untitled Visualization";
          const coacheeFirstName = assignment.coachee?.firstName || "Unknown";
          const coacheeLastName = assignment.coachee?.lastName || "User";

          await ActivityLogService.createActivityLog({
            coachId: assignment.coachId,
            coacheeId: assignment.coacheeId,
            visualizationAssignmentId: assignment.id,
            eventType: "VISUALIZATION_COMPLETED",
            eventMessage: `Visualization "${visualizationTitle}" completed by ${coacheeFirstName} ${coacheeLastName}`,
          });
        } catch (logError) {
          console.error(
            "Failed to log visualization completion activity:",
            logError
          );
          // Don't fail the status update if logging fails
        }
      }

      return assignment;
    } catch (error) {
      console.error("Error updating visualization assignment status:", error);
      throw new Error("Failed to update visualization assignment status");
    }
  }

  /**
   * Update visualization assignment
   */
  static async updateVisualizationAssignment(
    id: string,
    data: UpdateVisualizationAssignmentData
  ) {
    try {
      const assignment = await prisma.visualizationAssignment.update({
        where: { id },
        data,
        include: {
          visualization: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      return assignment;
    } catch (error) {
      console.error("Error updating visualization assignment:", error);
      throw new Error("Failed to update visualization assignment");
    }
  }

  /**
   * Delete visualization assignment
   */
  static async deleteVisualizationAssignment(id: string) {
    try {
      await prisma.visualizationAssignment.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error deleting visualization assignment:", error);
      throw new Error("Failed to delete visualization assignment");
    }
  }

  /**
   * Get visualization assignments for a specific coachee
   */
  static async getCoacheeVisualizationAssignments(coacheeId: string) {
    return this.getVisualizationAssignments({ coacheeId });
  }

  /**
   * Get visualization assignments for a specific coach
   */
  static async getCoachVisualizationAssignments(coachId: string) {
    return this.getVisualizationAssignments({ coachId });
  }
}
