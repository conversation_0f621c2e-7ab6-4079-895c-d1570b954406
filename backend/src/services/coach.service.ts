import {
  PrismaClient,
  AssignmentStatus,
  VisualizationAssignmentStatus,
} from "@prisma/client";
import { ActivityLogService } from "./activityLog.service";

const prisma = new PrismaClient();

export interface AssignCoacheeData {
  coachId: string;
  coacheeId: string;
  coachNotes?: string;
}

export interface UpdateCoacheeNotesData {
  coachId: string;
  coacheeId: string;
  coachNotes: string;
}

export interface CoacheeFilters {
  coachId: string;
  search?: string;
  organizationId?: string;
}

export interface AssignmentFilters {
  coachId: string;
  status?: AssignmentStatus;
  coacheeId?: string;
  limit?: number;
  offset?: number;
}

export interface CoachPerformanceMetrics {
  totalCoachees: number;
  totalAssignments: number;
  completedAssignments: number;
  pendingAssignments: number;
  totalVisualizationAssignments: number;
  completedVisualizationAssignments: number;
  averageCompletionRate: number;
  recentActivity: any[];
  coacheePerformance: Array<{
    coachee: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    totalAssignments: number;
    completedAssignments: number;
    completionRate: number;
    lastActivity?: Date;
  }>;
}

export class CoachService {
  /**
   * Get all coachees assigned to a coach
   */
  static async getCoachees(filters: CoacheeFilters) {
    try {
      const { coachId, search, organizationId } = filters;

      const where: any = {
        coachId,
      };

      if (search) {
        where.coachee = {
          OR: [
            {
              email: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              firstName: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              lastName: {
                contains: search,
                mode: "insensitive",
              },
            },
          ],
        };
      }

      if (organizationId) {
        where.coachee = {
          ...where.coachee,
          organizationMemberships: {
            some: {
              organizationId,
            },
          },
        };
      }

      const coachees = await prisma.coachCoachee.findMany({
        where,
        include: {
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              organizationMemberships: {
                include: {
                  organization: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
              receivedAssignments: {
                where: {
                  coachId: coachId, // Filter by assignments assigned by this coach
                },
                select: {
                  id: true,
                  status: true,
                  createdAt: true,
                  completedAt: true,
                },
                orderBy: {
                  createdAt: "desc",
                },
                take: 5,
              },
            },
          },
        },
        orderBy: {
          assignedAt: "desc",
        },
      });

      return coachees;
    } catch (error) {
      console.error("Error fetching coachees:", error);
      throw new Error("Failed to fetch coachees");
    }
  }

  /**
   * Assign a coachee to a coach
   */
  static async assignCoachee(data: AssignCoacheeData) {
    try {
      // Check if relationship already exists
      const existingRelationship = await prisma.coachCoachee.findUnique({
        where: {
          coachId_coacheeId: {
            coachId: data.coachId,
            coacheeId: data.coacheeId,
          },
        },
      });

      if (existingRelationship) {
        throw new Error("Coachee is already assigned to this coach");
      }

      const relationship = await prisma.coachCoachee.create({
        data: {
          coachId: data.coachId,
          coacheeId: data.coacheeId,
          coachNotes: data.coachNotes,
        },
        include: {
          coach: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Log the coach assignment activity
      try {
        await ActivityLogService.createActivityLog({
          coachId: data.coachId,
          coacheeId: data.coacheeId,
          eventType: "COACH_ASSIGNED",
          eventMessage: `${relationship.coach.firstName} ${relationship.coach.lastName} assigned as coach to ${relationship.coachee.firstName} ${relationship.coachee.lastName}`,
        });
      } catch (logError) {
        console.error("Failed to log coach assignment activity:", logError);
        // Don't fail the assignment if logging fails
      }

      return relationship;
    } catch (error) {
      console.error("Error assigning coachee:", error);
      throw error;
    }
  }

  /**
   * Remove coachee assignment
   */
  static async removeCoacheeAssignment(coachId: string, coacheeId: string) {
    try {
      await prisma.coachCoachee.delete({
        where: {
          coachId_coacheeId: {
            coachId,
            coacheeId,
          },
        },
      });
    } catch (error) {
      console.error("Error removing coachee assignment:", error);
      throw new Error("Failed to remove coachee assignment");
    }
  }

  /**
   * Update coach notes for a coachee
   */
  static async updateCoacheeNotes(data: UpdateCoacheeNotesData) {
    try {
      const relationship = await prisma.coachCoachee.update({
        where: {
          coachId_coacheeId: {
            coachId: data.coachId,
            coacheeId: data.coacheeId,
          },
        },
        data: {
          coachNotes: data.coachNotes,
        },
        include: {
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return relationship;
    } catch (error) {
      console.error("Error updating coachee notes:", error);
      throw new Error("Failed to update coachee notes");
    }
  }

  /**
   * Get assignments created by a coach
   */
  static async getCoachAssignments(filters: AssignmentFilters) {
    try {
      const { coachId, status, coacheeId, limit = 50, offset = 0 } = filters;

      const where: any = {
        coachId,
      };

      if (status) {
        where.status = status;
      }

      if (coacheeId) {
        where.coacheeId = coacheeId;
      }

      const [assignments, total] = await Promise.all([
        prisma.assignment.findMany({
          where,
          include: {
            exercise: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
            coachee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            submission: {
              select: {
                id: true,
                submittedAt: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: limit,
          skip: offset,
        }),
        prisma.assignment.count({ where }),
      ]);

      return {
        assignments,
        total,
        hasMore: offset + limit < total,
      };
    } catch (error) {
      console.error("Error fetching coach assignments:", error);
      throw new Error("Failed to fetch coach assignments");
    }
  }

  /**
   * Get coach performance metrics
   */
  static async getCoachPerformanceMetrics(
    coachId: string
  ): Promise<CoachPerformanceMetrics> {
    try {
      const [
        totalCoachees,
        totalAssignments,
        completedAssignments,
        pendingAssignments,
        totalVisualizationAssignments,
        completedVisualizationAssignments,
        recentActivity,
        coacheeRelationships,
      ] = await Promise.all([
        prisma.coachCoachee.count({ where: { coachId } }),
        prisma.assignment.count({ where: { coachId } }),
        prisma.assignment.count({
          where: {
            coachId,
            status: AssignmentStatus.COMPLETED,
          },
        }),
        prisma.assignment.count({
          where: {
            coachId,
            status: AssignmentStatus.PENDING,
          },
        }),
        prisma.visualizationAssignment.count({ where: { coachId } }),
        prisma.visualizationAssignment.count({
          where: {
            coachId,
            status: VisualizationAssignmentStatus.COMPLETED,
          },
        }),
        ActivityLogService.getActivityLogs({
          coachId,
          limit: 10,
        }),
        prisma.coachCoachee.findMany({
          where: { coachId },
          include: {
            coachee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        }),
      ]);

      // Calculate coachee performance
      const coacheePerformance = await Promise.all(
        coacheeRelationships.map(async (relationship) => {
          const [
            coacheeAssignments,
            coacheeCompletedAssignments,
            lastActivity,
          ] = await Promise.all([
            prisma.assignment.count({
              where: {
                coachId,
                coacheeId: relationship.coacheeId,
              },
            }),
            prisma.assignment.count({
              where: {
                coachId,
                coacheeId: relationship.coacheeId,
                status: AssignmentStatus.COMPLETED,
              },
            }),
            prisma.activityLog.findFirst({
              where: {
                coacheeId: relationship.coacheeId,
              },
              orderBy: {
                timestamp: "desc",
              },
              select: {
                timestamp: true,
              },
            }),
          ]);

          return {
            coachee: {
              id: relationship.coachee.id,
              firstName: relationship.coachee.firstName || "",
              lastName: relationship.coachee.lastName || "",
              email: relationship.coachee.email,
            },
            totalAssignments: coacheeAssignments,
            completedAssignments: coacheeCompletedAssignments,
            completionRate:
              coacheeAssignments > 0
                ? (coacheeCompletedAssignments / coacheeAssignments) * 100
                : 0,
            lastActivity: lastActivity?.timestamp,
          };
        })
      );

      const averageCompletionRate =
        totalAssignments > 0
          ? (completedAssignments / totalAssignments) * 100
          : 0;

      return {
        totalCoachees,
        totalAssignments,
        completedAssignments,
        pendingAssignments,
        totalVisualizationAssignments,
        completedVisualizationAssignments,
        averageCompletionRate,
        recentActivity: recentActivity.activityLogs,
        coacheePerformance,
      };
    } catch (error) {
      console.error("Error fetching coach performance metrics:", error);
      throw new Error("Failed to fetch coach performance metrics");
    }
  }

  /**
   * Provide feedback on an assignment
   */
  static async provideFeedback(
    assignmentId: string,
    coachId: string,
    feedback: string
  ) {
    try {
      // Verify the assignment belongs to this coach
      const assignment = await prisma.assignment.findFirst({
        where: {
          id: assignmentId,
          coachId,
        },
        include: {
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
          exercise: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!assignment) {
        throw new Error("Assignment not found or unauthorized");
      }

      const updatedAssignment = await prisma.assignment.update({
        where: { id: assignmentId },
        data: {
          coachFeedback: feedback,
          feedbackAt: new Date(),
        },
        include: {
          exercise: {
            select: {
              id: true,
              name: true,
            },
          },
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          submission: true,
        },
      });

      // Log the feedback activity
      try {
        await ActivityLogService.createActivityLog({
          coachId,
          coacheeId: assignment.coacheeId,
          assignmentId,
          eventType: "FEEDBACK_PROVIDED",
          eventMessage: `Feedback provided for exercise "${assignment.exercise.name}" to ${assignment.coachee.firstName} ${assignment.coachee.lastName}`,
        });
      } catch (logError) {
        console.error("Failed to log feedback activity:", logError);
        // Don't fail the feedback if logging fails
      }

      return updatedAssignment;
    } catch (error) {
      console.error("Error providing feedback:", error);
      throw error;
    }
  }
}
