import { PrismaClient, AssignmentStatus } from "@prisma/client";
import { ActivityLogService } from "./activityLog.service";

const prisma = new PrismaClient();

export interface CreateAssignmentData {
  exerciseId: string;
  coacheeId: string;
  coachId: string;
  dueDate?: Date;
}

export interface UpdateAssignmentData {
  status?: AssignmentStatus;
  dueDate?: Date;
  completedAt?: Date;
}

export interface AssignmentFilters {
  coacheeId?: string;
  coachId?: string;
  status?: AssignmentStatus;
  exerciseId?: string;
}

export class AssignmentService {
  /**
   * Create a new assignment
   */
  static async createAssignment(data: CreateAssignmentData) {
    try {
      const assignment = await prisma.assignment.create({
        data: {
          exerciseId: data.exerciseId,
          coacheeId: data.coacheeId,
          coachId: data.coachId,
          dueDate: data.dueDate,
          status: AssignmentStatus.PENDING,
        },
        include: {
          exercise: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Log the assignment creation activity
      try {
        await ActivityLogService.createActivityLog({
          coachId: data.coachId,
          coacheeId: data.coacheeId,
          assignmentId: assignment.id,
          eventType: "EXERCISE_ASSIGNED",
          eventMessage: `Exercise "${assignment.exercise.name}" assigned to ${assignment.coachee.firstName} ${assignment.coachee.lastName}`,
        });
      } catch (logError) {
        console.error("Failed to log assignment creation activity:", logError);
        // Don't fail the assignment creation if logging fails
      }

      return assignment;
    } catch (error) {
      console.error("Error creating assignment:", error);
      throw new Error("Failed to create assignment");
    }
  }

  /**
   * Get assignments with filters
   */
  static async getAssignments(filters: AssignmentFilters = {}) {
    try {
      const where: any = {};

      if (filters.coacheeId) {
        where.coacheeId = filters.coacheeId;
      }

      if (filters.coachId) {
        where.coachId = filters.coachId;
      }

      if (filters.status) {
        where.status = filters.status;
      }

      if (filters.exerciseId) {
        where.exerciseId = filters.exerciseId;
      }

      const assignments = await prisma.assignment.findMany({
        where,
        include: {
          exercise: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          submission: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return assignments;
    } catch (error) {
      console.error("Error fetching assignments:", error);
      throw new Error("Failed to fetch assignments");
    }
  }

  /**
   * Get assignment by ID
   */
  static async getAssignmentById(id: string) {
    try {
      const assignment = await prisma.assignment.findUnique({
        where: { id },
        include: {
          exercise: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          submission: true,
        },
      });

      return assignment;
    } catch (error) {
      console.error("Error fetching assignment:", error);
      throw new Error("Failed to fetch assignment");
    }
  }

  /**
   * Update assignment
   */
  static async updateAssignment(id: string, data: UpdateAssignmentData) {
    try {
      const assignment = await prisma.assignment.update({
        where: { id },
        data,
        include: {
          exercise: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          submission: true,
        },
      });

      return assignment;
    } catch (error) {
      console.error("Error updating assignment:", error);
      throw new Error("Failed to update assignment");
    }
  }

  /**
   * Submit assignment (complete it)
   */
  static async submitAssignment(
    id: string,
    userId: string,
    submissionData: any
  ) {
    try {
      // First get the assignment to verify ownership
      const assignment = await prisma.assignment.findUnique({
        where: { id },
        include: {
          exercise: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!assignment) {
        throw new Error("Assignment not found");
      }

      if (assignment.coacheeId !== userId) {
        throw new Error("Unauthorized to submit this assignment");
      }

      // Create or update submission
      const submission = await prisma.assignmentSubmission.upsert({
        where: { assignmentId: id },
        update: {
          answers: submissionData,
          submittedAt: new Date(),
        },
        create: {
          assignmentId: id,
          coacheeId: userId,
          answers: submissionData,
          submittedAt: new Date(),
        },
      });

      // Update assignment status to completed
      const updatedAssignment = await prisma.assignment.update({
        where: { id },
        data: {
          status: AssignmentStatus.COMPLETED,
          completedAt: new Date(),
        },
        include: {
          exercise: true,
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          submission: true,
        },
      });

      // Log the assignment completion activity
      try {
        await ActivityLogService.createActivityLog({
          coachId: assignment.coachId,
          coacheeId: userId,
          assignmentId: id,
          eventType: "EXERCISE_COMPLETED",
          eventMessage: `Exercise "${assignment.exercise.name}" completed by ${updatedAssignment.coachee.firstName} ${updatedAssignment.coachee.lastName}`,
        });
      } catch (logError) {
        console.error(
          "Failed to log assignment completion activity:",
          logError
        );
        // Don't fail the submission if logging fails
      }

      return updatedAssignment;
    } catch (error) {
      console.error("Error submitting assignment:", error);
      throw error;
    }
  }

  /**
   * Delete assignment
   */
  static async deleteAssignment(id: string) {
    try {
      await prisma.assignment.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error deleting assignment:", error);
      throw new Error("Failed to delete assignment");
    }
  }

  /**
   * Get assignments for a specific coachee
   */
  static async getCoacheeAssignments(coacheeId: string) {
    return this.getAssignments({ coacheeId });
  }

  /**
   * Get assignments for a specific coach
   */
  static async getCoachAssignments(coachId: string) {
    return this.getAssignments({ coachId });
  }
}
