import Replicate from "replicate";
import { spawn } from "child_process";
import { writeFileSync, unlinkSync, readFileSync } from "fs";
import { tmpdir } from "os";
import { join } from "path";

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY as string,
});

export interface TTSOptions {
  voice?: string;
  speed?: number;
  language?: string;
  model_version?: string;
}

interface ReplicateResponse {
  id: string;
  status: string;
  output: string;
  error?: string;
  completed_at?: string;
  created_at?: string;
  started_at?: string;
  metrics?: {
    predict_time: number;
    total_time: number;
  };
  urls?: {
    get: string;
    cancel: string;
    stream?: string;
    web?: string;
  };
}

export class TTSService {
  /**
   * Convert WAV buffer to MP3 using ffmpeg for better browser compatibility
   */
  private static async convertWavToMp3(wavBuffer: Buffer): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const tempWavPath = join(tmpdir(), `temp_${Date.now()}.wav`);
      const tempMp3Path = join(tmpdir(), `temp_${Date.now()}.mp3`);

      try {
        // Write WAV buffer to temporary file
        writeFileSync(tempWavPath, wavBuffer);

        // Spawn ffmpeg process to convert WAV to MP3
        const ffmpeg = spawn("ffmpeg", [
          "-i",
          tempWavPath,
          "-codec:a",
          "libmp3lame",
          "-b:a",
          "128k",
          "-ar",
          "44100",
          "-ac",
          "2",
          "-y", // Overwrite output file
          tempMp3Path,
        ]);

        ffmpeg.on("close", (code) => {
          try {
            if (code === 0) {
              // Read the converted MP3 file
              const mp3Buffer = readFileSync(tempMp3Path);
              resolve(mp3Buffer);
            } else {
              reject(new Error(`ffmpeg process exited with code ${code}`));
            }
          } catch (error) {
            reject(error);
          } finally {
            // Clean up temporary files
            try {
              unlinkSync(tempWavPath);
              unlinkSync(tempMp3Path);
            } catch (cleanupError) {
              console.warn("Failed to clean up temporary files:", cleanupError);
            }
          }
        });

        ffmpeg.on("error", (error) => {
          reject(new Error(`ffmpeg error: ${error.message}`));
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Generate audio using Kokoro TTS on Replicate
   */
  static async generateAudio(
    text: string,
    options: TTSOptions = {}
  ): Promise<Buffer> {
    try {
      // Validate inputs
      if (!text || text.trim().length === 0) {
        throw new Error("Text is required for audio generation");
      }

      if (text.length > 5000) {
        throw new Error("Text is too long. Maximum 5000 characters allowed.");
      }

      if (!process.env.REPLICATE_API_KEY) {
        throw new Error("REPLICATE_API_KEY is not configured");
      }

      if (!process.env.REPLICATE_KOKORO_MODEL) {
        throw new Error("REPLICATE_KOKORO_MODEL is not configured");
      }

      console.log(`Generating audio for text: "${text.substring(0, 100)}..."`);

      const input = {
        text: text.trim(),
        voice: options.voice || "af_nicole", // Default voice
        speed: options.speed || 1.0,
        language: options.language || "en",
      };

      console.log("Replicate input:", input);
      console.log("Using model:", process.env.REPLICATE_KOKORO_MODEL);

      // Create prediction and use urls.get to fetch the result
      console.log("Creating Replicate prediction...");

      const output = await replicate.run(
        process.env.REPLICATE_KOKORO_MODEL as `${string}/${string}`,
        { input }
      );

      console.log("Replicate output:", output);
      console.log("Output type:", typeof output);
      console.log("Output constructor:", output?.constructor?.name);

      // Extract the audio URL from the response
      let audioUrl: string;

      if (typeof output === "string") {
        // Direct URL string
        audioUrl = output;
      } else if (Array.isArray(output) && output.length > 0) {
        // Array with URL as first element
        audioUrl = output[0];
      } else if (output && typeof output === "object") {
        // Check if it's a FileOutput object with url() method
        if ("url" in output && typeof (output as any).url === "function") {
          console.log("Found FileOutput object with url() method");
          const urlResult = (output as any).url();
          console.log("URL result:", urlResult);
          console.log("URL result type:", typeof urlResult);

          // The url() method might return a URL object or string
          if (typeof urlResult === "string") {
            audioUrl = urlResult;
          } else if (
            urlResult &&
            typeof urlResult === "object" &&
            "toString" in urlResult
          ) {
            audioUrl = urlResult.toString();
          } else {
            throw new Error(
              `Unexpected URL format from FileOutput: ${urlResult}`
            );
          }
        } else if ("output" in output) {
          // Object with output field containing the URL (expected format)
          const replicateResponse = output as ReplicateResponse;

          // Check if the prediction was successful
          if (replicateResponse.error) {
            throw new Error(
              `Replicate prediction failed: ${replicateResponse.error}`
            );
          }

          if (replicateResponse.status !== "succeeded") {
            throw new Error(
              `Replicate prediction status: ${replicateResponse.status}`
            );
          }

          audioUrl = replicateResponse.output;
        } else {
          throw new Error(
            `Unexpected Replicate response format: ${JSON.stringify(output)}`
          );
        }
      } else {
        throw new Error(
          `Unexpected Replicate response format: ${JSON.stringify(output)}`
        );
      }

      if (!audioUrl || typeof audioUrl !== "string") {
        throw new Error(
          `Invalid audio URL received from Replicate: ${audioUrl}`
        );
      }

      console.log("Audio URL:", audioUrl);

      // Download the generated audio file
      const response = await fetch(audioUrl);

      if (!response.ok) {
        throw new Error(
          `Failed to download audio: ${response.status} ${response.statusText}`
        );
      }

      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      if (buffer.length === 0) {
        throw new Error("Generated audio file is empty");
      }

      console.log(
        `Audio downloaded successfully. Size: ${buffer.length} bytes`
      );

      // Convert WAV to MP3 for better browser compatibility
      console.log("Converting WAV to MP3 for browser compatibility...");
      try {
        const mp3Buffer = await this.convertWavToMp3(buffer);
        console.log(
          `Audio converted to MP3 successfully. Size: ${mp3Buffer.length} bytes`
        );
        return mp3Buffer;
      } catch (conversionError) {
        console.warn(
          "Failed to convert to MP3, returning original WAV:",
          conversionError
        );
        // Fallback to original WAV if conversion fails
        return buffer;
      }
    } catch (error) {
      console.error("Error generating audio:", error);

      if (error instanceof Error) {
        throw error;
      }

      throw new Error("Unknown error occurred during audio generation");
    }
  }

  /**
   * Get available voices for Kokoro TTS
   */
  static getAvailableVoices(): string[] {
    return [
      "af_bella",
      "af_nicole",
      "af_sarah",
      "am_adam",
      "am_michael",
      "bf_emma",
      "bf_isabella",
      "bm_george",
      "bm_lewis",
    ];
  }

  /**
   * Validate TTS options
   */
  static validateOptions(options: TTSOptions): void {
    if (options.speed && (options.speed < 0.5 || options.speed > 2.0)) {
      throw new Error("Speed must be between 0.5 and 2.0");
    }

    if (options.voice && !this.getAvailableVoices().includes(options.voice)) {
      throw new Error(
        `Invalid voice. Available voices: ${this.getAvailableVoices().join(
          ", "
        )}`
      );
    }

    if (
      options.language &&
      ![
        "en",
        "es",
        "fr",
        "de",
        "it",
        "pt",
        "pl",
        "tr",
        "ru",
        "nl",
        "cs",
        "ar",
        "zh",
        "ja",
        "hu",
        "ko",
      ].includes(options.language)
    ) {
      throw new Error("Unsupported language");
    }
  }
}
