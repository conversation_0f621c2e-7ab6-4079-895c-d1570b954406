import prisma from "../prisma";

export interface CreateIPSRecordData {
  coacheeId: string;
  dateTime: Date;
  competitionDateTime: Date;
  competitionName: string;
  performanceScore: number;
  arousalScore: number;
}

export interface UpdateIPSRecordData {
  dateTime?: Date;
  competitionDateTime?: Date;
  competitionName?: string;
  performanceScore?: number;
  arousalScore?: number;
}

export interface GetIPSRecordsOptions {
  coacheeId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export class IPSService {
  /**
   * Create a new IPS record
   */
  static async createIPSRecord(data: CreateIPSRecordData) {
    try {
      // Validate scores are within range
      if (data.performanceScore < 0 || data.performanceScore > 10) {
        throw new Error("Performance score must be between 0 and 10");
      }
      if (data.arousalScore < 0 || data.arousalScore > 10) {
        throw new Error("Arousal score must be between 0 and 10");
      }

      const ipsRecord = await prisma.idealPerformanceState.create({
        data: {
          coacheeId: data.coacheeId,
          dateTime: data.dateTime,
          competitionDateTime: data.competitionDateTime,
          competitionName: data.competitionName,
          performanceScore: data.performanceScore,
          arousalScore: data.arousalScore,
        },
        include: {
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return ipsRecord;
    } catch (error) {
      console.error("Error creating IPS record:", error);
      throw error;
    }
  }

  /**
   * Get IPS records with optional filtering
   */
  static async getIPSRecords(options: GetIPSRecordsOptions = {}) {
    try {
      const {
        coacheeId,
        startDate,
        endDate,
        limit = 50,
        offset = 0,
      } = options;

      const where: any = {};

      if (coacheeId) {
        where.coacheeId = coacheeId;
      }

      if (startDate || endDate) {
        where.competitionDateTime = {};
        if (startDate) {
          where.competitionDateTime.gte = startDate;
        }
        if (endDate) {
          where.competitionDateTime.lte = endDate;
        }
      }

      const [ipsRecords, total] = await Promise.all([
        prisma.idealPerformanceState.findMany({
          where,
          include: {
            coachee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: {
            competitionDateTime: "desc",
          },
          take: limit,
          skip: offset,
        }),
        prisma.idealPerformanceState.count({ where }),
      ]);

      return {
        ipsRecords,
        total,
        hasMore: offset + limit < total,
      };
    } catch (error) {
      console.error("Error fetching IPS records:", error);
      throw new Error("Failed to fetch IPS records");
    }
  }

  /**
   * Get IPS record by ID
   */
  static async getIPSRecordById(id: string) {
    try {
      const ipsRecord = await prisma.idealPerformanceState.findUnique({
        where: { id },
        include: {
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!ipsRecord) {
        throw new Error("IPS record not found");
      }

      return ipsRecord;
    } catch (error) {
      console.error("Error fetching IPS record:", error);
      throw error;
    }
  }

  /**
   * Update IPS record
   */
  static async updateIPSRecord(id: string, data: UpdateIPSRecordData) {
    try {
      // Validate scores if provided
      if (data.performanceScore !== undefined && (data.performanceScore < 0 || data.performanceScore > 10)) {
        throw new Error("Performance score must be between 0 and 10");
      }
      if (data.arousalScore !== undefined && (data.arousalScore < 0 || data.arousalScore > 10)) {
        throw new Error("Arousal score must be between 0 and 10");
      }

      const ipsRecord = await prisma.idealPerformanceState.update({
        where: { id },
        data,
        include: {
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return ipsRecord;
    } catch (error) {
      console.error("Error updating IPS record:", error);
      throw error;
    }
  }

  /**
   * Delete IPS record
   */
  static async deleteIPSRecord(id: string) {
    try {
      await prisma.idealPerformanceState.delete({
        where: { id },
      });

      return { success: true };
    } catch (error) {
      console.error("Error deleting IPS record:", error);
      throw error;
    }
  }

  /**
   * Get IPS records for a specific coachee
   */
  static async getCoacheeIPSRecords(coacheeId: string, limit = 50, offset = 0) {
    return this.getIPSRecords({ coacheeId, limit, offset });
  }

  /**
   * Get IPS trends for visualization (simplified data for charts)
   */
  static async getIPSTrends(coacheeId: string, startDate?: Date, endDate?: Date) {
    try {
      const where: any = { coacheeId };

      if (startDate || endDate) {
        where.competitionDateTime = {};
        if (startDate) {
          where.competitionDateTime.gte = startDate;
        }
        if (endDate) {
          where.competitionDateTime.lte = endDate;
        }
      }

      const ipsRecords = await prisma.idealPerformanceState.findMany({
        where,
        select: {
          id: true,
          competitionDateTime: true,
          competitionName: true,
          performanceScore: true,
          arousalScore: true,
        },
        orderBy: {
          competitionDateTime: "asc",
        },
      });

      return ipsRecords;
    } catch (error) {
      console.error("Error fetching IPS trends:", error);
      throw new Error("Failed to fetch IPS trends");
    }
  }
}
