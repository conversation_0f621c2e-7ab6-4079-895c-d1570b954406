import { PrismaClient, ActivityEventType } from "@prisma/client";

const prisma = new PrismaClient();

export interface CreateActivityLogData {
  coachId?: string;
  coacheeId?: string;
  assignmentId?: string;
  visualizationAssignmentId?: string;
  eventType: ActivityEventType;
  eventMessage: string;
}

export interface ActivityLogFilters {
  coacheeId?: string;
  coachId?: string;
  eventType?: ActivityEventType;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export class ActivityLogService {
  /**
   * Create a new activity log entry
   */
  static async createActivityLog(data: CreateActivityLogData) {
    try {
      const activityLog = await prisma.activityLog.create({
        data: {
          coachId: data.coachId,
          coacheeId: data.coacheeId,
          assignmentId: data.assignmentId,
          visualizationAssignmentId: data.visualizationAssignmentId,
          eventType: data.eventType,
          eventMessage: data.eventMessage,
        },
        include: {
          coach: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          assignment: {
            select: {
              id: true,
              exercise: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          visualizationAssignment: {
            select: {
              id: true,
              visualization: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
        },
      });

      return activityLog;
    } catch (error) {
      console.error("Error creating activity log:", error);
      throw new Error("Failed to create activity log");
    }
  }

  /**
   * Get activity logs with filters
   */
  static async getActivityLogs(filters: ActivityLogFilters = {}) {
    try {
      const {
        coacheeId,
        coachId,
        eventType,
        startDate,
        endDate,
        limit = 50,
        offset = 0,
      } = filters;

      const where: any = {};

      if (coacheeId) {
        where.coacheeId = coacheeId;
      }

      if (coachId) {
        where.coachId = coachId;
      }

      if (eventType) {
        where.eventType = eventType;
      }

      if (startDate || endDate) {
        where.timestamp = {};
        if (startDate) {
          where.timestamp.gte = startDate;
        }
        if (endDate) {
          where.timestamp.lte = endDate;
        }
      }

      const [activityLogs, total] = await Promise.all([
        prisma.activityLog.findMany({
          where,
          include: {
            coach: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            coachee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            assignment: {
              select: {
                id: true,
                exercise: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
            visualizationAssignment: {
              select: {
                id: true,
                visualization: {
                  select: {
                    id: true,
                    title: true,
                  },
                },
              },
            },
          },
          orderBy: {
            timestamp: "desc",
          },
          take: limit,
          skip: offset,
        }),
        prisma.activityLog.count({ where }),
      ]);

      return {
        activityLogs,
        total,
        hasMore: offset + limit < total,
      };
    } catch (error) {
      console.error("Error fetching activity logs:", error);
      throw new Error("Failed to fetch activity logs");
    }
  }

  /**
   * Get activity logs for a specific coachee
   */
  static async getCoacheeActivityLogs(
    coacheeId: string,
    filters: Omit<ActivityLogFilters, "coacheeId"> = {}
  ) {
    return this.getActivityLogs({ ...filters, coacheeId });
  }

  /**
   * Get activity logs for a specific coach
   */
  static async getCoachActivityLogs(
    coachId: string,
    filters: Omit<ActivityLogFilters, "coachId"> = {}
  ) {
    return this.getActivityLogs({ ...filters, coachId });
  }

  /**
   * Get recent activity for dashboard
   */
  static async getRecentActivity(limit: number = 10) {
    try {
      const activityLogs = await prisma.activityLog.findMany({
        include: {
          coach: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          assignment: {
            select: {
              id: true,
              exercise: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          visualizationAssignment: {
            select: {
              id: true,
              visualization: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
        },
        orderBy: {
          timestamp: "desc",
        },
        take: limit,
      });

      return activityLogs;
    } catch (error) {
      console.error("Error fetching recent activity:", error);
      throw new Error("Failed to fetch recent activity");
    }
  }

  /**
   * Get activity statistics for dashboard
   */
  static async getActivityStats(startDate?: Date, endDate?: Date) {
    try {
      const where: any = {};

      if (startDate || endDate) {
        where.timestamp = {};
        if (startDate) {
          where.timestamp.gte = startDate;
        }
        if (endDate) {
          where.timestamp.lte = endDate;
        }
      }

      const [
        totalActivities,
        exerciseAssignments,
        exerciseCompletions,
        visualizationAssignments,
        visualizationCompletions,
        coachAssignments,
      ] = await Promise.all([
        prisma.activityLog.count({ where }),
        prisma.activityLog.count({
          where: { ...where, eventType: "EXERCISE_ASSIGNED" },
        }),
        prisma.activityLog.count({
          where: { ...where, eventType: "EXERCISE_COMPLETED" },
        }),
        prisma.activityLog.count({
          where: { ...where, eventType: "VISUALIZATION_ASSIGNED" },
        }),
        prisma.activityLog.count({
          where: { ...where, eventType: "VISUALIZATION_COMPLETED" },
        }),
        prisma.activityLog.count({
          where: { ...where, eventType: "COACH_ASSIGNED" },
        }),
      ]);

      return {
        totalActivities,
        exerciseAssignments,
        exerciseCompletions,
        visualizationAssignments,
        visualizationCompletions,
        coachAssignments,
        completionRate:
          exerciseAssignments > 0
            ? Math.round((exerciseCompletions / exerciseAssignments) * 100)
            : 0,
      };
    } catch (error) {
      console.error("Error fetching activity stats:", error);
      throw new Error("Failed to fetch activity stats");
    }
  }
}
