import prisma from "../prisma";
import { PeriodizationStatus } from "@prisma/client";
import {
  generatePeriodization,
  PeriodizationEntry,
} from "../utils/periodization";

export interface CreatePeriodizationData {
  coacheeId: string;
  coachId: string;
  name: string;
  offSeasonStartDate: Date;
  offSeasonEndDate: Date;
  prepStartDate: Date;
  prepEndDate: Date;
  preCompStartDate: Date;
  preCompEndDate: Date;
  competitionStartDate: Date;
  competitionEndDate: Date;
  scheduleMentalToughness: boolean;
  scheduleMentalWellness: boolean;
  mentalWellnessStartDate?: Date;
  mentalWellnessEndDate?: Date;
}

export interface PeriodizationPreview {
  entries: PeriodizationEntry[];
  errors: string[];
  totalAssignments: number;
  phaseBreakdown: {
    offSeason: number;
    prep: number;
    preComp: number;
  };
}

export class PeriodizationService {
  /**
   * Create a new periodization and generate assignments
   */
  static async createPeriodization(data: CreatePeriodizationData) {
    try {
      // First, get all exercises for periodization generation
      const exercises = await prisma.exercise.findMany({
        select: {
          id: true,
          name: true,
          description: true,
          questions: true,
        },
      });

      // Generate periodization entries
      const periodizationResult = generatePeriodization(
        data.offSeasonStartDate,
        data.offSeasonEndDate,
        data.prepStartDate,
        data.prepEndDate,
        data.preCompStartDate,
        data.preCompEndDate,
        data.competitionStartDate,
        data.competitionEndDate,
        exercises,
        data.scheduleMentalToughness,
        data.scheduleMentalWellness,
        data.mentalWellnessStartDate,
        data.mentalWellnessEndDate
      );

      if (periodizationResult.errors.length > 0) {
        throw new Error(
          `Periodization generation failed: ${periodizationResult.errors.join(
            ", "
          )}`
        );
      }

      console.log(
        `Generated ${periodizationResult.entries.length} assignments for periodization`
      );

      // Ensure coach-coachee relationship exists before creating assignments
      const existingRelationship = await prisma.coachCoachee.findUnique({
        where: {
          coachId_coacheeId: {
            coachId: data.coachId,
            coacheeId: data.coacheeId,
          },
        },
      });

      if (!existingRelationship) {
        console.log(
          `Creating coach-coachee relationship for coach ${data.coachId} and coachee ${data.coacheeId}`
        );
        await prisma.coachCoachee.create({
          data: {
            coachId: data.coachId,
            coacheeId: data.coacheeId,
            coachNotes: `Relationship created automatically during periodization: ${data.name}`,
          },
        });
      }

      // If there are too many assignments, create them outside the transaction
      if (periodizationResult.entries.length > 100) {
        // Create periodization first
        const periodization = await prisma.periodization.create({
          data: {
            coacheeId: data.coacheeId,
            coachId: data.coachId,
            name: data.name,
            offSeasonStartDate: data.offSeasonStartDate,
            offSeasonEndDate: data.offSeasonEndDate,
            prepStartDate: data.prepStartDate,
            prepEndDate: data.prepEndDate,
            preCompStartDate: data.preCompStartDate,
            preCompEndDate: data.preCompEndDate,
            competitionStartDate: data.competitionStartDate,
            competitionEndDate: data.competitionEndDate,
            scheduleMentalToughness: data.scheduleMentalToughness,
            scheduleMentalWellness: data.scheduleMentalWellness,
            mentalWellnessStartDate: data.mentalWellnessStartDate,
            mentalWellnessEndDate: data.mentalWellnessEndDate,
          },
          include: {
            coachee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            coach: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });

        // Create assignments in batches
        interface AssignmentCreateData {
          exerciseId: string;
          coacheeId: string;
          coachId: string;
          dueDate: Date;
          status: "PENDING";
        }

        const assignmentData: AssignmentCreateData[] =
          periodizationResult.entries.map(
            (entry: PeriodizationEntry): AssignmentCreateData => ({
              exerciseId: entry.exerciseId,
              coacheeId: data.coacheeId,
              coachId: data.coachId,
              dueDate: new Date(entry.assignmentDate),
              status: "PENDING",
            })
          );

        const batchSize = 50;
        let totalCreated = 0;

        for (let i = 0; i < assignmentData.length; i += batchSize) {
          const batch = assignmentData.slice(i, i + batchSize);
          const result = await prisma.assignment.createMany({
            data: batch,
          });
          totalCreated += result.count;
        }

        return {
          periodization,
          assignments: totalCreated,
          assignmentCount: totalCreated,
        };
      } else {
        // Create periodization record and assignments in a transaction with extended timeout
        const result = await prisma.$transaction(
          async (tx) => {
            // Create the periodization record
            const periodization = await tx.periodization.create({
              data: {
                coacheeId: data.coacheeId,
                coachId: data.coachId,
                name: data.name,
                offSeasonStartDate: data.offSeasonStartDate,
                offSeasonEndDate: data.offSeasonEndDate,
                prepStartDate: data.prepStartDate,
                prepEndDate: data.prepEndDate,
                preCompStartDate: data.preCompStartDate,
                preCompEndDate: data.preCompEndDate,
                competitionStartDate: data.competitionStartDate,
                competitionEndDate: data.competitionEndDate,
                scheduleMentalToughness: data.scheduleMentalToughness,
                scheduleMentalWellness: data.scheduleMentalWellness,
                mentalWellnessStartDate: data.mentalWellnessStartDate,
                mentalWellnessEndDate: data.mentalWellnessEndDate,
              },
              include: {
                coachee: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
                coach: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            });

            // Create assignments from periodization entries using createMany for better performance
            const assignmentData = periodizationResult.entries.map(
              (entry: PeriodizationEntry) => ({
                exerciseId: entry.exerciseId,
                coacheeId: data.coacheeId,
                coachId: data.coachId,
                dueDate: new Date(entry.assignmentDate),
                status: "PENDING" as const,
              })
            );

            const assignments = await tx.assignment.createMany({
              data: assignmentData,
            });

            return {
              periodization,
              assignments: assignments.count,
              assignmentCount: assignments.count,
            };
          },
          {
            maxWait: 10000, // 10 seconds
            timeout: 15000, // 15 seconds
          }
        );

        return result;
      }
    } catch (error) {
      console.error("Error creating periodization:", error);
      throw error;
    }
  }

  /**
   * Generate a preview of periodization without creating records
   */
  static async generatePeriodizationPreview(
    data: Omit<CreatePeriodizationData, "name">
  ): Promise<PeriodizationPreview> {
    try {
      // Get all exercises for periodization generation
      const exercises = await prisma.exercise.findMany({
        select: {
          id: true,
          name: true,
          description: true,
          questions: true,
        },
      });

      // Generate periodization entries
      const periodizationResult = generatePeriodization(
        data.offSeasonStartDate,
        data.offSeasonEndDate,
        data.prepStartDate,
        data.prepEndDate,
        data.preCompStartDate,
        data.preCompEndDate,
        data.competitionStartDate,
        data.competitionEndDate,
        exercises,
        data.scheduleMentalToughness,
        data.scheduleMentalWellness,
        data.mentalWellnessStartDate,
        data.mentalWellnessEndDate
      );

      // Calculate phase breakdown
      const phaseBreakdown = {
        offSeason: 0,
        prep: 0,
        preComp: 0,
      };

      periodizationResult.entries.forEach((entry: PeriodizationEntry) => {
        const assignmentDate = new Date(entry.assignmentDate);
        if (
          assignmentDate >= data.offSeasonStartDate &&
          assignmentDate <= data.offSeasonEndDate
        ) {
          phaseBreakdown.offSeason++;
        } else if (
          assignmentDate >= data.prepStartDate &&
          assignmentDate <= data.prepEndDate
        ) {
          phaseBreakdown.prep++;
        } else if (
          assignmentDate >= data.preCompStartDate &&
          assignmentDate <= data.preCompEndDate
        ) {
          phaseBreakdown.preComp++;
        }
      });

      return {
        entries: periodizationResult.entries,
        errors: periodizationResult.errors,
        totalAssignments: periodizationResult.entries.length,
        phaseBreakdown,
      };
    } catch (error) {
      console.error("Error generating periodization preview:", error);
      throw error;
    }
  }

  /**
   * Get periodizations for a coach
   */
  static async getPeriodizationsForCoach(coachId: string) {
    return prisma.periodization.findMany({
      where: { coachId },
      include: {
        coachee: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  /**
   * Get periodizations for a coachee
   */
  static async getPeriodizationsForCoachee(coacheeId: string) {
    return prisma.periodization.findMany({
      where: { coacheeId },
      include: {
        coach: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  }

  /**
   * Get periodization by ID
   */
  static async getPeriodizationById(id: string) {
    return prisma.periodization.findUnique({
      where: { id },
      include: {
        coachee: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        coach: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  }

  /**
   * Update periodization status
   */
  static async updatePeriodizationStatus(
    id: string,
    status: PeriodizationStatus
  ) {
    return prisma.periodization.update({
      where: { id },
      data: { status },
    });
  }

  /**
   * Delete periodization (and optionally its assignments)
   */
  static async deletePeriodization(
    id: string,
    deleteAssignments: boolean = false
  ) {
    try {
      if (deleteAssignments) {
        // Get the periodization to find related assignments
        const periodization = await prisma.periodization.findUnique({
          where: { id },
        });

        if (periodization) {
          // Delete assignments created for this periodization
          // Note: This is a simplified approach. In a more complex system,
          // you might want to track which assignments belong to which periodization
          await prisma.assignment.deleteMany({
            where: {
              coacheeId: periodization.coacheeId,
              coachId: periodization.coachId,
              createdAt: {
                gte: periodization.createdAt,
              },
            },
          });
        }
      }

      // Delete the periodization
      return prisma.periodization.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error deleting periodization:", error);
      throw error;
    }
  }
}
