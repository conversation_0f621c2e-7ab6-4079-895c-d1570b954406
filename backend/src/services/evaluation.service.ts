import { PrismaClient } from "@prisma/client";
import { ActivityLogService } from "./activityLog.service";

const prisma = new PrismaClient();

export interface CreateEvaluationData {
  coacheeId: string;
  coachId: string;
  title?: string;
  notes?: string;
  composure: number;
  concentration: number;
  confidence: number;
  copeability: number;
  cohesion: number;
}

export interface UpdateEvaluationData {
  id: string;
  coachId: string;
  title?: string;
  notes?: string;
  composure?: number;
  concentration?: number;
  confidence?: number;
  copeability?: number;
  cohesion?: number;
}

export interface EvaluationFilters {
  coacheeId?: string;
  coachId?: string;
  limit?: number;
  offset?: number;
  startDate?: Date;
  endDate?: Date;
}

export interface EvaluationTrend {
  date: Date;
  composure: number;
  concentration: number;
  confidence: number;
  copeability: number;
  cohesion: number;
  overall: number;
}

export class EvaluationService {
  /**
   * Validate B3-5C scores (must be between 1-10)
   */
  private static validateScores(scores: {
    composure: number;
    concentration: number;
    confidence: number;
    copeability: number;
    cohesion: number;
  }) {
    const { composure, concentration, confidence, copeability, cohesion } =
      scores;

    if (
      composure < 1 ||
      composure > 10 ||
      concentration < 1 ||
      concentration > 10 ||
      confidence < 1 ||
      confidence > 10 ||
      copeability < 1 ||
      copeability > 10 ||
      cohesion < 1 ||
      cohesion > 10
    ) {
      throw new Error("All B3-5C scores must be between 1 and 10");
    }
  }

  /**
   * Create a new evaluation
   */
  static async createEvaluation(data: CreateEvaluationData) {
    try {
      // Validate scores
      this.validateScores({
        composure: data.composure,
        concentration: data.concentration,
        confidence: data.confidence,
        copeability: data.copeability,
        cohesion: data.cohesion,
      });

      // Verify coach-coachee relationship exists
      const relationship = await prisma.coachCoachee.findUnique({
        where: {
          coachId_coacheeId: {
            coachId: data.coachId,
            coacheeId: data.coacheeId,
          },
        },
      });

      if (!relationship) {
        throw new Error("Coach-coachee relationship not found");
      }

      const evaluation = await prisma.coacheeEvaluation.create({
        data: {
          coacheeId: data.coacheeId,
          coachId: data.coachId,
          title: data.title,
          notes: data.notes,
          composure: data.composure,
          concentration: data.concentration,
          confidence: data.confidence,
          copeability: data.copeability,
          cohesion: data.cohesion,
        },
        include: {
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          coach: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Log the evaluation creation activity
      try {
        await ActivityLogService.createActivityLog({
          coachId: data.coachId,
          coacheeId: data.coacheeId,
          eventType: "EVALUATION_CREATED",
          eventMessage: `${evaluation.coach.firstName} ${evaluation.coach.lastName} created B3-5C evaluation for ${evaluation.coachee.firstName} ${evaluation.coachee.lastName}`,
        });
      } catch (logError) {
        console.error("Failed to log evaluation creation activity:", logError);
        // Don't fail the evaluation creation if logging fails
      }

      return evaluation;
    } catch (error) {
      console.error("Error creating evaluation:", error);
      throw error;
    }
  }

  /**
   * Update an existing evaluation
   */
  static async updateEvaluation(data: UpdateEvaluationData) {
    try {
      // Validate scores if provided
      const scores: any = {};
      if (data.composure !== undefined) scores.composure = data.composure;
      if (data.concentration !== undefined)
        scores.concentration = data.concentration;
      if (data.confidence !== undefined) scores.confidence = data.confidence;
      if (data.copeability !== undefined) scores.copeability = data.copeability;
      if (data.cohesion !== undefined) scores.cohesion = data.cohesion;

      if (Object.keys(scores).length > 0) {
        // Get current evaluation to fill in missing scores for validation
        const currentEvaluation = await prisma.coacheeEvaluation.findUnique({
          where: { id: data.id },
        });

        if (!currentEvaluation) {
          throw new Error("Evaluation not found");
        }

        const fullScores = {
          composure: scores.composure ?? currentEvaluation.composure,
          concentration:
            scores.concentration ?? currentEvaluation.concentration,
          confidence: scores.confidence ?? currentEvaluation.confidence,
          copeability: scores.copeability ?? currentEvaluation.copeability,
          cohesion: scores.cohesion ?? currentEvaluation.cohesion,
        };

        this.validateScores(fullScores);
      }

      // Verify the evaluation belongs to this coach
      const existingEvaluation = await prisma.coacheeEvaluation.findFirst({
        where: {
          id: data.id,
          coachId: data.coachId,
        },
      });

      if (!existingEvaluation) {
        throw new Error("Evaluation not found or unauthorized");
      }

      const updateData: any = {};
      if (data.title !== undefined) updateData.title = data.title;
      if (data.notes !== undefined) updateData.notes = data.notes;
      if (data.composure !== undefined) updateData.composure = data.composure;
      if (data.concentration !== undefined)
        updateData.concentration = data.concentration;
      if (data.confidence !== undefined)
        updateData.confidence = data.confidence;
      if (data.copeability !== undefined)
        updateData.copeability = data.copeability;
      if (data.cohesion !== undefined) updateData.cohesion = data.cohesion;

      const evaluation = await prisma.coacheeEvaluation.update({
        where: { id: data.id },
        data: updateData,
        include: {
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          coach: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Log the evaluation update activity
      try {
        await ActivityLogService.createActivityLog({
          coachId: data.coachId,
          coacheeId: evaluation.coacheeId,
          eventType: "EVALUATION_UPDATED",
          eventMessage: `${evaluation.coach.firstName} ${evaluation.coach.lastName} updated B3-5C evaluation for ${evaluation.coachee.firstName} ${evaluation.coachee.lastName}`,
        });
      } catch (logError) {
        console.error("Failed to log evaluation update activity:", logError);
        // Don't fail the evaluation update if logging fails
      }

      return evaluation;
    } catch (error) {
      console.error("Error updating evaluation:", error);
      throw error;
    }
  }

  /**
   * Get evaluations with filters
   */
  static async getEvaluations(filters: EvaluationFilters) {
    try {
      const {
        coacheeId,
        coachId,
        limit = 50,
        offset = 0,
        startDate,
        endDate,
      } = filters;

      const where: any = {};

      if (coacheeId) {
        where.coacheeId = coacheeId;
      }

      if (coachId) {
        where.coachId = coachId;
      }

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) {
          where.createdAt.gte = startDate;
        }
        if (endDate) {
          where.createdAt.lte = endDate;
        }
      }

      const [evaluations, total] = await Promise.all([
        prisma.coacheeEvaluation.findMany({
          where,
          include: {
            coachee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            coach: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: limit,
          skip: offset,
        }),
        prisma.coacheeEvaluation.count({ where }),
      ]);

      return {
        evaluations,
        total,
        hasMore: offset + limit < total,
      };
    } catch (error) {
      console.error("Error fetching evaluations:", error);
      throw new Error("Failed to fetch evaluations");
    }
  }

  /**
   * Get a single evaluation by ID
   */
  static async getEvaluationById(id: string, coachId?: string) {
    try {
      const where: any = { id };

      // If coachId is provided, ensure the coach owns this evaluation
      if (coachId) {
        where.coachId = coachId;
      }

      const evaluation = await prisma.coacheeEvaluation.findFirst({
        where,
        include: {
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          coach: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!evaluation) {
        throw new Error("Evaluation not found");
      }

      return evaluation;
    } catch (error) {
      console.error("Error fetching evaluation:", error);
      throw error;
    }
  }

  /**
   * Delete an evaluation
   */
  static async deleteEvaluation(id: string, coachId: string) {
    try {
      // Verify the evaluation belongs to this coach
      const evaluation = await prisma.coacheeEvaluation.findFirst({
        where: {
          id,
          coachId,
        },
        include: {
          coachee: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!evaluation) {
        throw new Error("Evaluation not found or unauthorized");
      }

      await prisma.coacheeEvaluation.delete({
        where: { id },
      });

      return { message: "Evaluation deleted successfully" };
    } catch (error) {
      console.error("Error deleting evaluation:", error);
      throw error;
    }
  }

  /**
   * Get evaluation trends for a coachee over time
   */
  static async getEvaluationTrends(
    coacheeId: string,
    coachId?: string
  ): Promise<EvaluationTrend[]> {
    try {
      const where: any = { coacheeId };

      // If coachId is provided, only get evaluations from that coach
      if (coachId) {
        where.coachId = coachId;
      }

      const evaluations = await prisma.coacheeEvaluation.findMany({
        where,
        select: {
          createdAt: true,
          composure: true,
          concentration: true,
          confidence: true,
          copeability: true,
          cohesion: true,
        },
        orderBy: {
          createdAt: "asc",
        },
      });

      return evaluations.map((evaluation) => ({
        date: evaluation.createdAt,
        composure: evaluation.composure,
        concentration: evaluation.concentration,
        confidence: evaluation.confidence,
        copeability: evaluation.copeability,
        cohesion: evaluation.cohesion,
        overall:
          Math.round(
            ((evaluation.composure +
              evaluation.concentration +
              evaluation.confidence +
              evaluation.copeability +
              evaluation.cohesion) /
              5) *
              10
          ) / 10, // Round to 1 decimal place
      }));
    } catch (error) {
      console.error("Error fetching evaluation trends:", error);
      throw new Error("Failed to fetch evaluation trends");
    }
  }

  /**
   * Get latest evaluation for a coachee
   */
  static async getLatestEvaluation(coacheeId: string, coachId?: string) {
    try {
      const where: any = { coacheeId };

      if (coachId) {
        where.coachId = coachId;
      }

      const evaluation = await prisma.coacheeEvaluation.findFirst({
        where,
        include: {
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          coach: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return evaluation;
    } catch (error) {
      console.error("Error fetching latest evaluation:", error);
      throw new Error("Failed to fetch latest evaluation");
    }
  }
}
