import { PrismaClient, PerformanceCategory } from "@prisma/client";
import { ActivityLogService } from "./activityLog.service";

const prisma = new PrismaClient();

export interface CreatePerformanceProfileData {
  coacheeId: string;
  startDate: Date;
  targetDate: Date;
  goals: CreatePerformanceGoalData[];
}

export interface CreatePerformanceGoalData {
  category: PerformanceCategory;
  goalName: string;
  currentRating: number;
  targetRating: number;
}

export interface UpdatePerformanceGoalData {
  id?: string; // Optional for new goals
  category?: PerformanceCategory; // Required for new goals
  goalName?: string;
  currentRating?: number;
  targetRating?: number;
  isActive?: boolean;
}

export interface PerformanceProfileFilters {
  coacheeId: string;
  limit?: number;
  offset?: number;
}

export class PerformanceProfileService {
  static async createPerformanceProfile(data: CreatePerformanceProfileData) {
    try {
      // Validate that each category has max 12 active goals
      const goalsByCategory = data.goals.reduce((acc, goal) => {
        acc[goal.category] = (acc[goal.category] || 0) + 1;
        return acc;
      }, {} as Record<PerformanceCategory, number>);

      for (const [category, count] of Object.entries(goalsByCategory)) {
        if (count > 12) {
          throw new Error(
            `Maximum 12 goals allowed per category. ${category} has ${count} goals.`
          );
        }
      }

      // Validate rating ranges
      for (const goal of data.goals) {
        if (goal.currentRating < 0 || goal.currentRating > 10) {
          throw new Error("Current rating must be between 0 and 10");
        }
        if (goal.targetRating < 0 || goal.targetRating > 10) {
          throw new Error("Target rating must be between 0 and 10");
        }
      }

      const performanceProfile = await prisma.performanceProfile.create({
        data: {
          coacheeId: data.coacheeId,
          startDate: data.startDate,
          targetDate: data.targetDate,
          goals: {
            create: data.goals.map((goal) => ({
              category: goal.category,
              goalName: goal.goalName,
              currentRating: goal.currentRating,
              targetRating: goal.targetRating,
            })),
          },
        },
        include: {
          goals: {
            where: { isActive: true },
            orderBy: [{ category: "asc" }, { createdAt: "asc" }],
          },
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Log activity
      await ActivityLogService.createActivityLog({
        coacheeId: data.coacheeId,
        eventType: "PERFORMANCE_PROFILE_CREATED",
        eventMessage: `Performance profile created with ${data.goals.length} goals`,
      });

      return performanceProfile;
    } catch (error) {
      console.error("Error creating performance profile:", error);
      throw error;
    }
  }

  static async getPerformanceProfiles(filters: PerformanceProfileFilters) {
    try {
      const { coacheeId, limit = 10, offset = 0 } = filters;

      const profiles = await prisma.performanceProfile.findMany({
        where: {
          coacheeId,
        },
        include: {
          goals: {
            where: { isActive: true },
            orderBy: [{ category: "asc" }, { createdAt: "asc" }],
          },
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        take: limit,
        skip: offset,
      });

      return profiles;
    } catch (error) {
      console.error("Error fetching performance profiles:", error);
      throw error;
    }
  }

  static async getPerformanceProfileById(id: string) {
    try {
      const profile = await prisma.performanceProfile.findUnique({
        where: { id },
        include: {
          goals: {
            orderBy: [{ category: "asc" }, { createdAt: "asc" }],
          },
          coachee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!profile) {
        throw new Error("Performance profile not found");
      }

      return profile;
    } catch (error) {
      console.error("Error fetching performance profile:", error);
      throw error;
    }
  }

  static async updatePerformanceGoals(
    profileId: string,
    goals: UpdatePerformanceGoalData[]
  ) {
    try {
      const operations = [];

      // Separate new goals from existing goals
      const newGoals = goals.filter((goal) => !goal.id);
      const existingGoals = goals.filter((goal) => goal.id);

      // Create new goals
      if (newGoals.length > 0) {
        const createOperations = newGoals.map((goal) =>
          prisma.performanceProfileGoal.create({
            data: {
              performanceProfileId: profileId,
              category: goal.category!,
              goalName: goal.goalName!,
              currentRating: goal.currentRating!,
              targetRating: goal.targetRating!,
              isActive: goal.isActive ?? true,
            },
          })
        );
        operations.push(...createOperations);
      }

      // Update existing goals
      if (existingGoals.length > 0) {
        const updateOperations = existingGoals.map((goal) =>
          prisma.performanceProfileGoal.update({
            where: { id: goal.id! },
            data: {
              ...(goal.goalName !== undefined && { goalName: goal.goalName }),
              ...(goal.currentRating !== undefined && {
                currentRating: goal.currentRating,
              }),
              ...(goal.targetRating !== undefined && {
                targetRating: goal.targetRating,
              }),
              ...(goal.isActive !== undefined && { isActive: goal.isActive }),
            },
          })
        );
        operations.push(...updateOperations);
      }

      if (operations.length > 0) {
        await prisma.$transaction(operations);
      }

      // Get updated profile
      const updatedProfile = await this.getPerformanceProfileById(profileId);

      // Log activity
      await ActivityLogService.createActivityLog({
        coacheeId: updatedProfile.coacheeId,
        eventType: "PERFORMANCE_PROFILE_UPDATED",
        eventMessage: `Performance profile goals updated (${newGoals.length} new, ${existingGoals.length} modified)`,
      });

      return updatedProfile;
    } catch (error) {
      console.error("Error updating performance goals:", error);
      throw error;
    }
  }

  static async getLatestActiveGoalsByCategory(coacheeId: string) {
    try {
      // Get the most recent performance profile for the coachee
      const latestProfile = await prisma.performanceProfile.findFirst({
        where: { coacheeId },
        orderBy: { createdAt: "desc" },
        include: {
          goals: {
            where: { isActive: true },
            orderBy: [{ category: "asc" }, { createdAt: "asc" }],
          },
        },
      });

      if (!latestProfile) {
        return null;
      }

      // Group goals by category
      const goalsByCategory = latestProfile.goals.reduce((acc, goal) => {
        if (!acc[goal.category]) {
          acc[goal.category] = [];
        }
        acc[goal.category].push(goal);
        return acc;
      }, {} as Record<PerformanceCategory, typeof latestProfile.goals>);

      return {
        profileId: latestProfile.id,
        startDate: latestProfile.startDate,
        targetDate: latestProfile.targetDate,
        goalsByCategory,
      };
    } catch (error) {
      console.error("Error fetching latest active goals:", error);
      throw error;
    }
  }

  static async deletePerformanceProfile(id: string) {
    try {
      const profile = await prisma.performanceProfile.findUnique({
        where: { id },
        select: { coacheeId: true },
      });

      if (!profile) {
        throw new Error("Performance profile not found");
      }

      await prisma.performanceProfile.delete({
        where: { id },
      });

      // Log activity
      await ActivityLogService.createActivityLog({
        coacheeId: profile.coacheeId,
        eventType: "PERFORMANCE_PROFILE_DELETED",
        eventMessage: "Performance profile deleted",
      });

      return { success: true };
    } catch (error) {
      console.error("Error deleting performance profile:", error);
      throw error;
    }
  }
}
