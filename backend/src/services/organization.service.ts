import {
  PrismaClient,
  AssignmentStatus,
  VisualizationAssignmentStatus,
} from "@prisma/client";
import { ActivityLogService } from "./activityLog.service";

const prisma = new PrismaClient();

export interface CreateOrganizationData {
  name: string;
  hrAdminId?: string;
}

export interface UpdateOrganizationData {
  name?: string;
  hrAdminId?: string;
}

export interface AddMemberData {
  organizationId: string;
  userId: string;
  role: "COACH" | "COACHEE";
}

export interface OrganizationFilters {
  search?: string;
  hrAdminId?: string;
  limit?: number;
  offset?: number;
}

export interface MemberFilters {
  organizationId: string;
  role?: "COACH" | "COACHEE";
  search?: string;
  limit?: number;
  offset?: number;
}

export interface OrganizationAnalytics {
  totalMembers: number;
  totalCoaches: number;
  totalCoachees: number;
  totalAssignments: number;
  completedAssignments: number;
  pendingAssignments: number;
  totalVisualizationAssignments: number;
  completedVisualizationAssignments: number;
  averageCompletionRate: number;
  recentActivity: any[];
  memberPerformance: Array<{
    member: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      role: string;
    };
    totalAssignments: number;
    completedAssignments: number;
    completionRate: number;
    lastActivity?: Date;
  }>;
}

export class OrganizationService {
  /**
   * Create a new organization
   */
  static async createOrganization(data: CreateOrganizationData) {
    try {
      const organization = await prisma.organization.create({
        data: {
          name: data.name,
          hrAdminId: data.hrAdminId,
        },
        include: {
          hrAdmin: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return organization;
    } catch (error) {
      console.error("Error creating organization:", error);
      throw new Error("Failed to create organization");
    }
  }

  /**
   * Get all organizations with filters
   */
  static async getOrganizations(filters: OrganizationFilters = {}) {
    try {
      const { search, hrAdminId, limit = 50, offset = 0 } = filters;

      const where: any = {};

      if (search) {
        where.name = {
          contains: search,
          mode: "insensitive",
        };
      }

      if (hrAdminId) {
        where.hrAdminId = hrAdminId;
      }

      const [organizations, total] = await Promise.all([
        prisma.organization.findMany({
          where,
          include: {
            hrAdmin: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            _count: {
              select: {
                coacheeMemberships: true,
                coachMemberships: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: limit,
          skip: offset,
        }),
        prisma.organization.count({ where }),
      ]);

      return {
        organizations,
        total,
        hasMore: offset + limit < total,
      };
    } catch (error) {
      console.error("Error fetching organizations:", error);
      throw new Error("Failed to fetch organizations");
    }
  }

  /**
   * Get organization by ID with detailed information
   */
  static async getOrganizationById(id: string) {
    try {
      const organization = await prisma.organization.findUnique({
        where: { id },
        include: {
          hrAdmin: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          coacheeMemberships: {
            include: {
              coachee: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
            orderBy: {
              joinedAt: "desc",
            },
          },
          coachMemberships: {
            include: {
              coach: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
            orderBy: {
              joinedAt: "desc",
            },
          },
        },
      });

      if (!organization) {
        throw new Error("Organization not found");
      }

      return organization;
    } catch (error) {
      console.error("Error fetching organization:", error);
      throw new Error("Failed to fetch organization");
    }
  }

  /**
   * Update organization
   */
  static async updateOrganization(id: string, data: UpdateOrganizationData) {
    try {
      const organization = await prisma.organization.update({
        where: { id },
        data,
        include: {
          hrAdmin: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return organization;
    } catch (error) {
      console.error("Error updating organization:", error);
      throw new Error("Failed to update organization");
    }
  }

  /**
   * Delete organization
   */
  static async deleteOrganization(id: string) {
    try {
      await prisma.organization.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error deleting organization:", error);
      throw new Error("Failed to delete organization");
    }
  }

  /**
   * Add member to organization
   */
  static async addMember(data: AddMemberData) {
    try {
      if (data.role === "COACH") {
        // Check if already a member
        const existingMembership = await prisma.organizationCoach.findUnique({
          where: {
            organizationId_coachId: {
              organizationId: data.organizationId,
              coachId: data.userId,
            },
          },
        });

        if (existingMembership) {
          throw new Error(
            "User is already a coach member of this organization"
          );
        }

        const membership = await prisma.organizationCoach.create({
          data: {
            organizationId: data.organizationId,
            coachId: data.userId,
          },
          include: {
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
            coach: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });

        return membership;
      } else {
        // Check if already a member
        const existingMembership = await prisma.organizationCoachee.findUnique({
          where: {
            organizationId_coacheeId: {
              organizationId: data.organizationId,
              coacheeId: data.userId,
            },
          },
        });

        if (existingMembership) {
          throw new Error(
            "User is already a coachee member of this organization"
          );
        }

        const membership = await prisma.organizationCoachee.create({
          data: {
            organizationId: data.organizationId,
            coacheeId: data.userId,
          },
          include: {
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
            coachee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });

        return membership;
      }
    } catch (error) {
      console.error("Error adding member to organization:", error);
      throw error;
    }
  }

  /**
   * Remove member from organization
   */
  static async removeMember(
    organizationId: string,
    userId: string,
    role: "COACH" | "COACHEE"
  ) {
    try {
      if (role === "COACH") {
        await prisma.organizationCoach.delete({
          where: {
            organizationId_coachId: {
              organizationId,
              coachId: userId,
            },
          },
        });
      } else {
        await prisma.organizationCoachee.delete({
          where: {
            organizationId_coacheeId: {
              organizationId,
              coacheeId: userId,
            },
          },
        });
      }
    } catch (error) {
      console.error("Error removing member from organization:", error);
      throw new Error("Failed to remove member from organization");
    }
  }

  /**
   * Get organization members with filters
   */
  static async getOrganizationMembers(filters: MemberFilters) {
    try {
      const { organizationId, role, search, limit = 50, offset = 0 } = filters;

      if (role === "COACH" || !role) {
        const coachWhere: any = {
          organizationId,
        };

        if (search) {
          coachWhere.coach = {
            OR: [
              {
                email: {
                  contains: search,
                  mode: "insensitive",
                },
              },
              {
                firstName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            ],
          };
        }

        const coaches = await prisma.organizationCoach.findMany({
          where: coachWhere,
          include: {
            coach: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            joinedAt: "desc",
          },
          take: role === "COACH" ? limit : undefined,
          skip: role === "COACH" ? offset : 0,
        });

        if (role === "COACH") {
          const total = await prisma.organizationCoach.count({
            where: coachWhere,
          });
          return {
            members: coaches.map((c) => ({ ...c.coach, joinedAt: c.joinedAt })),
            total,
            hasMore: offset + limit < total,
          };
        }
      }

      if (role === "COACHEE" || !role) {
        const coacheeWhere: any = {
          organizationId,
        };

        if (search) {
          coacheeWhere.coachee = {
            OR: [
              {
                email: {
                  contains: search,
                  mode: "insensitive",
                },
              },
              {
                firstName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            ],
          };
        }

        const coachees = await prisma.organizationCoachee.findMany({
          where: coacheeWhere,
          include: {
            coachee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            joinedAt: "desc",
          },
          take: role === "COACHEE" ? limit : undefined,
          skip: role === "COACHEE" ? offset : 0,
        });

        if (role === "COACHEE") {
          const total = await prisma.organizationCoachee.count({
            where: coacheeWhere,
          });
          return {
            members: coachees.map((c) => ({
              ...c.coachee,
              joinedAt: c.joinedAt,
            })),
            total,
            hasMore: offset + limit < total,
          };
        }
      }

      // If no role specified, return both
      const [coaches, coachees] = await Promise.all([
        prisma.organizationCoach.findMany({
          where: { organizationId },
          include: {
            coach: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
              },
            },
          },
        }),
        prisma.organizationCoachee.findMany({
          where: { organizationId },
          include: {
            coachee: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
              },
            },
          },
        }),
      ]);

      const allMembers = [
        ...coaches.map((c) => ({ ...c.coach, joinedAt: c.joinedAt })),
        ...coachees.map((c) => ({ ...c.coachee, joinedAt: c.joinedAt })),
      ].sort(
        (a, b) =>
          new Date(b.joinedAt).getTime() - new Date(a.joinedAt).getTime()
      );

      return {
        members: allMembers.slice(offset, offset + limit),
        total: allMembers.length,
        hasMore: offset + limit < allMembers.length,
      };
    } catch (error) {
      console.error("Error fetching organization members:", error);
      throw new Error("Failed to fetch organization members");
    }
  }

  /**
   * Get organization analytics
   */
  static async getOrganizationAnalytics(
    organizationId: string
  ): Promise<OrganizationAnalytics> {
    try {
      // Get all member IDs for this organization
      const [coachMemberships, coacheeMemberships] = await Promise.all([
        prisma.organizationCoach.findMany({
          where: { organizationId },
          select: { coachId: true },
        }),
        prisma.organizationCoachee.findMany({
          where: { organizationId },
          select: { coacheeId: true },
        }),
      ]);

      const coachIds = coachMemberships.map((m) => m.coachId);
      const coacheeIds = coacheeMemberships.map((m) => m.coacheeId);
      const allMemberIds = [...coachIds, ...coacheeIds];

      const [
        totalAssignments,
        completedAssignments,
        pendingAssignments,
        totalVisualizationAssignments,
        completedVisualizationAssignments,
        recentActivity,
        members,
      ] = await Promise.all([
        prisma.assignment.count({
          where: {
            OR: [
              { coachId: { in: coachIds } },
              { coacheeId: { in: coacheeIds } },
            ],
          },
        }),
        prisma.assignment.count({
          where: {
            OR: [
              { coachId: { in: coachIds } },
              { coacheeId: { in: coacheeIds } },
            ],
            status: AssignmentStatus.COMPLETED,
          },
        }),
        prisma.assignment.count({
          where: {
            OR: [
              { coachId: { in: coachIds } },
              { coacheeId: { in: coacheeIds } },
            ],
            status: AssignmentStatus.PENDING,
          },
        }),
        prisma.visualizationAssignment.count({
          where: {
            OR: [
              { coachId: { in: coachIds } },
              { coacheeId: { in: coacheeIds } },
            ],
          },
        }),
        prisma.visualizationAssignment.count({
          where: {
            OR: [
              { coachId: { in: coachIds } },
              { coacheeId: { in: coacheeIds } },
            ],
            status: VisualizationAssignmentStatus.COMPLETED,
          },
        }),
        ActivityLogService.getActivityLogs({
          limit: 20,
        }),
        prisma.user.findMany({
          where: {
            id: { in: allMemberIds },
          },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            role: true,
          },
        }),
      ]);

      // Calculate member performance
      const memberPerformance = await Promise.all(
        members.map(async (member) => {
          const [memberAssignments, memberCompletedAssignments, lastActivity] =
            await Promise.all([
              prisma.assignment.count({
                where: {
                  OR: [{ coachId: member.id }, { coacheeId: member.id }],
                },
              }),
              prisma.assignment.count({
                where: {
                  OR: [{ coachId: member.id }, { coacheeId: member.id }],
                  status: AssignmentStatus.COMPLETED,
                },
              }),
              prisma.activityLog.findFirst({
                where: {
                  OR: [{ coachId: member.id }, { coacheeId: member.id }],
                },
                orderBy: {
                  timestamp: "desc",
                },
                select: {
                  timestamp: true,
                },
              }),
            ]);

          return {
            member: {
              id: member.id,
              firstName: member.firstName || "",
              lastName: member.lastName || "",
              email: member.email,
              role: member.role,
            },
            totalAssignments: memberAssignments,
            completedAssignments: memberCompletedAssignments,
            completionRate:
              memberAssignments > 0
                ? (memberCompletedAssignments / memberAssignments) * 100
                : 0,
            lastActivity: lastActivity?.timestamp,
          };
        })
      );

      const averageCompletionRate =
        totalAssignments > 0
          ? (completedAssignments / totalAssignments) * 100
          : 0;

      return {
        totalMembers: allMemberIds.length,
        totalCoaches: coachIds.length,
        totalCoachees: coacheeIds.length,
        totalAssignments,
        completedAssignments,
        pendingAssignments,
        totalVisualizationAssignments,
        completedVisualizationAssignments,
        averageCompletionRate,
        recentActivity: recentActivity.activityLogs,
        memberPerformance,
      };
    } catch (error) {
      console.error("Error fetching organization analytics:", error);
      throw new Error("Failed to fetch organization analytics");
    }
  }
}
