import {
  S3Client,
  PutObjectCommand,
  DeleteO<PERSON><PERSON>ommand,
  GetO<PERSON>Command,
  HeadObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { randomUUID } from "crypto";
import crypto from "crypto";

const s3Client = new S3Client({
  region: process.env.AWS_REGION as string,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string,
  },
});

const bucketName = process.env.AWS_S3_BUCKET as string;

export interface UploadResult {
  url: string;
  key: string;
  size: number;
}

export class StorageService {
  /**
   * Upload image file to S3 for exercise descriptions
   */
  static async uploadImage(
    imageBuffer: Buffer,
    filename: string,
    contentType: string
  ): Promise<UploadResult> {
    try {
      if (!bucketName) {
        throw new Error("AWS_S3_BUCKET is not configured");
      }

      if (!imageBuffer || imageBuffer.length === 0) {
        throw new Error("Image buffer is empty or invalid");
      }

      const key = `exercise-images/${filename}`;

      console.log(
        `Uploading image to S3: ${key} (${imageBuffer.length} bytes)`
      );

      await s3Client.send(
        new PutObjectCommand({
          Bucket: bucketName,
          Key: key,
          Body: imageBuffer,
          ContentType: contentType,
          CacheControl: "max-age=31536000", // 1 year cache
          Metadata: {
            uploadedAt: new Date().toISOString(),
            size: imageBuffer.length.toString(),
          },
        })
      );

      // Generate public URL (assuming bucket is configured for public read)
      const url = `https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;

      console.log(`Image uploaded successfully: ${url}`);

      return {
        url,
        key,
        size: imageBuffer.length,
      };
    } catch (error) {
      console.error("Error uploading image:", error);
      throw new Error(
        `Failed to upload image: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Upload audio file to S3 with enhanced metadata and error handling
   */
  static async uploadAudio(
    audioBuffer: Buffer,
    filename: string
  ): Promise<UploadResult> {
    try {
      if (!bucketName) {
        throw new Error("AWS_S3_BUCKET is not configured");
      }

      if (!audioBuffer || audioBuffer.length === 0) {
        throw new Error("Audio buffer is empty or invalid");
      }

      const key = `audio/${filename}`;

      console.log(
        `Uploading audio to S3: ${key} (${audioBuffer.length} bytes)`
      );

      await s3Client.send(
        new PutObjectCommand({
          Bucket: bucketName,
          Key: key,
          Body: audioBuffer,
          ContentType: "audio/mpeg", // MP3 format for better browser compatibility
          CacheControl: "max-age=31536000", // 1 year cache
          Metadata: {
            uploadedAt: new Date().toISOString(),
            size: audioBuffer.length.toString(),
          },
        })
      );

      // Generate public URL (assuming bucket is configured for public read)
      const url = `https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;

      console.log(`Audio uploaded successfully: ${url}`);

      return {
        url,
        key,
        size: audioBuffer.length,
      };
    } catch (error) {
      console.error("Error uploading audio:", error);
      throw new Error(
        `Failed to upload audio: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  static async deleteImage(filename: string): Promise<void> {
    const key = `exercise-images/${filename}`;

    await s3Client.send(
      new DeleteObjectCommand({
        Bucket: bucketName,
        Key: key,
      })
    );
  }

  static async deleteAudio(filename: string): Promise<void> {
    const key = `audio/${filename}`;

    await s3Client.send(
      new DeleteObjectCommand({
        Bucket: bucketName,
        Key: key,
      })
    );
  }

  /**
   * Check if an audio file exists in S3
   */
  static async audioExists(filename: string): Promise<boolean> {
    try {
      const key = `audio/${filename}`;
      const command = new HeadObjectCommand({
        Bucket: bucketName,
        Key: key,
      });

      await s3Client.send(command);
      return true;
    } catch (error: any) {
      if (
        error.name === "NotFound" ||
        error.$metadata?.httpStatusCode === 404
      ) {
        return false;
      }
      console.error("Error checking audio file existence:", error);
      throw error;
    }
  }

  /**
   * Get a signed URL for accessing an audio file
   */
  static async getAudioSignedUrl(
    filename: string,
    expiresIn: number = 3600
  ): Promise<string> {
    try {
      const key = `audio/${filename}`;
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: key,
      });

      return await getSignedUrl(s3Client, command, { expiresIn });
    } catch (error) {
      console.error("Error generating signed URL for audio:", error);
      throw new Error(
        `Failed to generate signed URL: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Generate a cache key for audio based on text and options
   */
  static generateAudioCacheKey(
    text: string,
    options: { voice?: string; language?: string; speed?: number } = {}
  ): string {
    const normalizedText = text.trim().toLowerCase();
    const voice = options.voice || "af_bella";
    const language = options.language || "en";
    const speed = options.speed || 1.0;

    const cacheString = `${normalizedText}-${voice}-${language}-${speed}`;
    const hash = crypto.createHash("md5").update(cacheString).digest("hex");

    return `${hash}.mp3`;
  }

  static generateAudioFilename(): string {
    return `${randomUUID()}.mp3`; // MP3 format for better browser compatibility
  }

  /**
   * Generate a unique filename for image uploads
   */
  static generateImageFilename(originalName: string): string {
    const extension = originalName.split(".").pop()?.toLowerCase() || "jpg";
    return `${randomUUID()}.${extension}`;
  }

  /**
   * Check if an image file exists in S3
   */
  static async imageExists(filename: string): Promise<boolean> {
    try {
      const key = `exercise-images/${filename}`;
      const command = new HeadObjectCommand({
        Bucket: bucketName,
        Key: key,
      });

      await s3Client.send(command);
      return true;
    } catch (error: any) {
      if (
        error.name === "NotFound" ||
        error.$metadata?.httpStatusCode === 404
      ) {
        return false;
      }
      console.error("Error checking image file existence:", error);
      throw error;
    }
  }

  /**
   * Get a signed URL for accessing an image file
   */
  static async getImageSignedUrl(
    filename: string,
    expiresIn: number = 3600
  ): Promise<string> {
    try {
      const key = `exercise-images/${filename}`;
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: key,
      });

      return await getSignedUrl(s3Client, command, { expiresIn });
    } catch (error) {
      console.error("Error generating signed URL for image:", error);
      throw new Error(
        `Failed to generate signed URL: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }
}
