import { PrismaClient, VisualizationAssignmentStatus } from "@prisma/client";
import { ActivityLogService } from "./activityLog.service";

const prisma = new PrismaClient();

export interface CreateVisualizationData {
  title: string;
  description?: string;
  audioUrl?: string;
  createdBy: string;
}

export interface UpdateVisualizationData {
  title?: string;
  description?: string;
  audioUrl?: string;
  updatedBy: string;
}

export interface VisualizationFilters {
  createdBy?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface CreateVisualizationAssignmentData {
  visualizationId: string;
  coacheeId: string;
  coachId: string;
  dueDate?: Date;
}

export interface UpdateVisualizationAssignmentData {
  status?: VisualizationAssignmentStatus;
  dueDate?: Date;
}

export interface VisualizationAssignmentFilters {
  coacheeId?: string;
  coachId?: string;
  status?: VisualizationAssignmentStatus;
  visualizationId?: string;
  limit?: number;
  offset?: number;
}

export class VisualizationService {
  /**
   * Create a new visualization
   */
  static async createVisualization(data: CreateVisualizationData) {
    try {
      const visualization = await prisma.visualization.create({
        data: {
          title: data.title,
          description: data.description,
          audioUrl: data.audioUrl,
          createdBy: data.createdBy,
          updatedBy: data.createdBy,
        },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return visualization;
    } catch (error) {
      console.error("Error creating visualization:", error);
      throw new Error("Failed to create visualization");
    }
  }

  /**
   * Get all visualizations with optional filters
   */
  static async getVisualizations(filters: VisualizationFilters = {}) {
    try {
      const { createdBy, search, limit = 50, offset = 0 } = filters;

      const where: any = {};

      if (createdBy) {
        where.createdBy = createdBy;
      }

      if (search) {
        where.OR = [
          {
            title: {
              contains: search,
              mode: "insensitive",
            },
          },
          {
            description: {
              contains: search,
              mode: "insensitive",
            },
          },
        ];
      }

      const [visualizations, total] = await Promise.all([
        prisma.visualization.findMany({
          where,
          include: {
            creator: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            updater: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            assignments: {
              select: {
                id: true,
                status: true,
                createdAt: true,
              },
              orderBy: {
                createdAt: "desc",
              },
              take: 5,
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: limit,
          skip: offset,
        }),
        prisma.visualization.count({ where }),
      ]);

      return {
        visualizations,
        total,
        hasMore: offset + limit < total,
      };
    } catch (error) {
      console.error("Error fetching visualizations:", error);
      throw new Error("Failed to fetch visualizations");
    }
  }

  /**
   * Get visualization by ID
   */
  static async getVisualizationById(id: string) {
    try {
      const visualization = await prisma.visualization.findUnique({
        where: { id },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          assignments: {
            include: {
              coachee: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              coach: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
            orderBy: {
              createdAt: "desc",
            },
          },
        },
      });

      if (!visualization) {
        throw new Error("Visualization not found");
      }

      return visualization;
    } catch (error) {
      console.error("Error fetching visualization:", error);
      throw error;
    }
  }

  /**
   * Update visualization
   */
  static async updateVisualization(id: string, data: UpdateVisualizationData) {
    try {
      const visualization = await prisma.visualization.update({
        where: { id },
        data: {
          title: data.title,
          description: data.description,
          audioUrl: data.audioUrl,
          updatedBy: data.updatedBy,
        },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return visualization;
    } catch (error) {
      console.error("Error updating visualization:", error);
      throw new Error("Failed to update visualization");
    }
  }

  /**
   * Delete visualization
   */
  static async deleteVisualization(id: string) {
    try {
      await prisma.visualization.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error deleting visualization:", error);
      throw new Error("Failed to delete visualization");
    }
  }

  /**
   * Create a new visualization assignment
   */
  static async createVisualizationAssignment(
    data: CreateVisualizationAssignmentData
  ) {
    try {
      const assignment = await prisma.visualizationAssignment.create({
        data: {
          visualizationId: data.visualizationId,
          coacheeId: data.coacheeId,
          coachId: data.coachId,
          dueDate: data.dueDate,
          status: VisualizationAssignmentStatus.PENDING,
        },
        include: {
          visualization: {
            select: {
              id: true,
              title: true,
              description: true,
              audioUrl: true,
            },
          },
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Log the visualization assignment creation activity
      try {
        await ActivityLogService.createActivityLog({
          coachId: data.coachId,
          coacheeId: data.coacheeId,
          visualizationAssignmentId: assignment.id,
          eventType: "VISUALIZATION_ASSIGNED",
          eventMessage: `Visualization "${assignment.visualization.title}" assigned to ${assignment.coachee.firstName} ${assignment.coachee.lastName}`,
        });
      } catch (logError) {
        console.error(
          "Failed to log visualization assignment creation activity:",
          logError
        );
        // Don't fail the assignment creation if logging fails
      }

      return assignment;
    } catch (error) {
      console.error("Error creating visualization assignment:", error);
      throw new Error("Failed to create visualization assignment");
    }
  }

  /**
   * Get visualization assignments with filters
   */
  static async getVisualizationAssignments(
    filters: VisualizationAssignmentFilters = {}
  ) {
    try {
      const {
        coacheeId,
        coachId,
        status,
        visualizationId,
        limit = 50,
        offset = 0,
      } = filters;

      const where: any = {};

      if (coacheeId) {
        where.coacheeId = coacheeId;
      }

      if (coachId) {
        where.coachId = coachId;
      }

      if (status) {
        where.status = status;
      }

      if (visualizationId) {
        where.visualizationId = visualizationId;
      }

      const [assignments, total] = await Promise.all([
        prisma.visualizationAssignment.findMany({
          where,
          include: {
            visualization: {
              select: {
                id: true,
                title: true,
                description: true,
                audioUrl: true,
              },
            },
            coachee: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
            coach: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: limit,
          skip: offset,
        }),
        prisma.visualizationAssignment.count({ where }),
      ]);

      return {
        assignments,
        total,
        hasMore: offset + limit < total,
      };
    } catch (error) {
      console.error("Error fetching visualization assignments:", error);
      throw new Error("Failed to fetch visualization assignments");
    }
  }

  /**
   * Get visualization assignment by ID
   */
  static async getVisualizationAssignmentById(id: string) {
    try {
      const assignment = await prisma.visualizationAssignment.findUnique({
        where: { id },
        include: {
          visualization: {
            select: {
              id: true,
              title: true,
              description: true,
              audioUrl: true,
            },
          },
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!assignment) {
        throw new Error("Visualization assignment not found");
      }

      return assignment;
    } catch (error) {
      console.error("Error fetching visualization assignment:", error);
      throw error;
    }
  }

  /**
   * Update visualization assignment
   */
  static async updateVisualizationAssignment(
    id: string,
    data: UpdateVisualizationAssignmentData
  ) {
    try {
      const assignment = await prisma.visualizationAssignment.update({
        where: { id },
        data,
        include: {
          visualization: {
            select: {
              id: true,
              title: true,
              description: true,
              audioUrl: true,
            },
          },
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      return assignment;
    } catch (error) {
      console.error("Error updating visualization assignment:", error);
      throw new Error("Failed to update visualization assignment");
    }
  }

  /**
   * Mark visualization assignment as viewed/completed
   */
  static async markVisualizationAsViewed(id: string, userId: string) {
    try {
      // First get the assignment to verify ownership
      const assignment = await prisma.visualizationAssignment.findUnique({
        where: { id },
        include: {
          visualization: {
            select: {
              title: true,
            },
          },
          coachee: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!assignment) {
        throw new Error("Visualization assignment not found");
      }

      if (assignment.coacheeId !== userId) {
        throw new Error("Unauthorized to update this assignment");
      }

      const updatedAssignment = await prisma.visualizationAssignment.update({
        where: { id },
        data: {
          status: VisualizationAssignmentStatus.COMPLETED,
        },
        include: {
          visualization: {
            select: {
              id: true,
              title: true,
              description: true,
              audioUrl: true,
            },
          },
          coachee: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
          coach: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Log the visualization completion activity
      try {
        await ActivityLogService.createActivityLog({
          coachId: assignment.coachId,
          coacheeId: userId,
          visualizationAssignmentId: id,
          eventType: "VISUALIZATION_COMPLETED",
          eventMessage: `Visualization "${assignment.visualization.title}" completed by ${assignment.coachee.firstName} ${assignment.coachee.lastName}`,
        });
      } catch (logError) {
        console.error(
          "Failed to log visualization completion activity:",
          logError
        );
        // Don't fail the completion if logging fails
      }

      return updatedAssignment;
    } catch (error) {
      console.error("Error marking visualization as viewed:", error);
      throw error;
    }
  }

  /**
   * Delete visualization assignment
   */
  static async deleteVisualizationAssignment(id: string) {
    try {
      await prisma.visualizationAssignment.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error deleting visualization assignment:", error);
      throw new Error("Failed to delete visualization assignment");
    }
  }

  /**
   * Get visualization assignments for a specific coachee
   */
  static async getCoacheeVisualizationAssignments(coacheeId: string) {
    return this.getVisualizationAssignments({ coacheeId });
  }

  /**
   * Get visualization assignments for a specific coach
   */
  static async getCoachVisualizationAssignments(coachId: string) {
    return this.getVisualizationAssignments({ coachId });
  }
}
