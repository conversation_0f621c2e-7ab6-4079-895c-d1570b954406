import { PrismaClient, UserRole, AssignmentStatus, VisualizationAssignmentStatus } from "@prisma/client";
import bcrypt from "bcryptjs";
import { ActivityLogService } from "./activityLog.service";

const prisma = new PrismaClient();

export interface CreateUserData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
}

export interface UpdateUserData {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: UserRole;
}

export interface UserFilters {
  role?: UserRole;
  search?: string;
  organizationId?: string;
  limit?: number;
  offset?: number;
}

export interface SystemAnalytics {
  totalUsers: number;
  totalCoaches: number;
  totalCoachees: number;
  totalHrAdmins: number;
  totalOrganizations: number;
  totalExercises: number;
  totalVisualizations: number;
  totalAssignments: number;
  completedAssignments: number;
  pendingAssignments: number;
  totalVisualizationAssignments: number;
  completedVisualizationAssignments: number;
  recentActivity: any[];
}

export class AdminService {
  /**
   * Create a new user (admin only)
   */
  static async createUser(data: CreateUserData) {
    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email },
      });

      if (existingUser) {
        throw new Error("User with this email already exists");
      }

      // Hash password
      const passwordHash = await bcrypt.hash(data.password, 10);

      const user = await prisma.user.create({
        data: {
          email: data.email,
          passwordHash,
          firstName: data.firstName,
          lastName: data.lastName,
          role: data.role,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return user;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  /**
   * Get all users with filters and pagination
   */
  static async getUsers(filters: UserFilters = {}) {
    try {
      const {
        role,
        search,
        organizationId,
        limit = 50,
        offset = 0,
      } = filters;

      const where: any = {};

      if (role) {
        where.role = role;
      }

      if (search) {
        where.OR = [
          {
            email: {
              contains: search,
              mode: "insensitive",
            },
          },
          {
            firstName: {
              contains: search,
              mode: "insensitive",
            },
          },
          {
            lastName: {
              contains: search,
              mode: "insensitive",
            },
          },
        ];
      }

      if (organizationId) {
        where.OR = [
          {
            organizationMemberships: {
              some: {
                organizationId,
              },
            },
          },
          {
            coachOrganizationMemberships: {
              some: {
                organizationId,
              },
            },
          },
        ];
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            createdAt: true,
            updatedAt: true,
            organizationMemberships: {
              include: {
                organization: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
            coachOrganizationMemberships: {
              include: {
                organization: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
          take: limit,
          skip: offset,
        }),
        prisma.user.count({ where }),
      ]);

      return {
        users,
        total,
        hasMore: offset + limit < total,
      };
    } catch (error) {
      console.error("Error fetching users:", error);
      throw new Error("Failed to fetch users");
    }
  }

  /**
   * Get user by ID with detailed information
   */
  static async getUserById(id: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
          updatedAt: true,
          organizationMemberships: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          coachOrganizationMemberships: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          assignedAssignments: {
            select: {
              id: true,
              status: true,
              createdAt: true,
              exercise: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            orderBy: {
              createdAt: "desc",
            },
            take: 10,
          },
          receivedAssignments: {
            select: {
              id: true,
              status: true,
              createdAt: true,
              exercise: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            orderBy: {
              createdAt: "desc",
            },
            take: 10,
          },
        },
      });

      if (!user) {
        throw new Error("User not found");
      }

      return user;
    } catch (error) {
      console.error("Error fetching user:", error);
      throw new Error("Failed to fetch user");
    }
  }

  /**
   * Update user
   */
  static async updateUser(id: string, data: UpdateUserData) {
    try {
      const user = await prisma.user.update({
        where: { id },
        data,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      return user;
    } catch (error) {
      console.error("Error updating user:", error);
      throw new Error("Failed to update user");
    }
  }

  /**
   * Delete user
   */
  static async deleteUser(id: string) {
    try {
      await prisma.user.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error deleting user:", error);
      throw new Error("Failed to delete user");
    }
  }

  /**
   * Get system analytics and statistics
   */
  static async getSystemAnalytics(): Promise<SystemAnalytics> {
    try {
      const [
        totalUsers,
        totalCoaches,
        totalCoachees,
        totalHrAdmins,
        totalOrganizations,
        totalExercises,
        totalVisualizations,
        totalAssignments,
        completedAssignments,
        pendingAssignments,
        totalVisualizationAssignments,
        completedVisualizationAssignments,
        recentActivity,
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { role: UserRole.COACH } }),
        prisma.user.count({ where: { role: UserRole.COACHEE } }),
        prisma.user.count({ where: { role: UserRole.HR_ADMIN } }),
        prisma.organization.count(),
        prisma.exercise.count(),
        prisma.visualization.count(),
        prisma.assignment.count(),
        prisma.assignment.count({ where: { status: AssignmentStatus.COMPLETED } }),
        prisma.assignment.count({ where: { status: AssignmentStatus.PENDING } }),
        prisma.visualizationAssignment.count(),
        prisma.visualizationAssignment.count({ 
          where: { status: VisualizationAssignmentStatus.COMPLETED } 
        }),
        ActivityLogService.getRecentActivity(20),
      ]);

      return {
        totalUsers,
        totalCoaches,
        totalCoachees,
        totalHrAdmins,
        totalOrganizations,
        totalExercises,
        totalVisualizations,
        totalAssignments,
        completedAssignments,
        pendingAssignments,
        totalVisualizationAssignments,
        completedVisualizationAssignments,
        recentActivity,
      };
    } catch (error) {
      console.error("Error fetching system analytics:", error);
      throw new Error("Failed to fetch system analytics");
    }
  }

  /**
   * Get all organizations with basic info
   */
  static async getAllOrganizations() {
    try {
      const organizations = await prisma.organization.findMany({
        select: {
          id: true,
          name: true,
          createdAt: true,
          hrAdmin: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              coacheeMemberships: true,
              coachMemberships: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return organizations;
    } catch (error) {
      console.error("Error fetching organizations:", error);
      throw new Error("Failed to fetch organizations");
    }
  }

  /**
   * Reset user password (admin only)
   */
  static async resetUserPassword(userId: string, newPassword: string) {
    try {
      const passwordHash = await bcrypt.hash(newPassword, 10);

      await prisma.user.update({
        where: { id: userId },
        data: {
          passwordHash,
          resetToken: null,
          resetTokenExpiry: null,
        },
      });
    } catch (error) {
      console.error("Error resetting user password:", error);
      throw new Error("Failed to reset user password");
    }
  }
}
