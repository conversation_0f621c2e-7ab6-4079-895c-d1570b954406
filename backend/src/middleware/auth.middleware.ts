import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { env } from "../config/env";
import prisma from "../prisma";
import { UserRole } from "@prisma/client";

// Extend Express Request interface to include user payload
declare global {
  namespace Express {
    interface Request {
      user?: {
        // Make user optional as it's only added after this middleware runs
        userId: string;
        email: string;
        role: UserRole;
      };
    }
  }
}

export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1]; // Bearer TOKEN

  if (token == null) {
    res.status(401).json({ message: "No token provided" });
    return;
  }

  try {
    const payload = jwt.verify(token, env.jwtSecret) as jwt.JwtPayload & {
      userId: string;
      email: string;
      role: UserRole;
    };

    // Optional: Check if user still exists in DB (adds overhead but more secure)
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: { id: true }, // Only select necessary field
    });

    if (!user) {
      res.status(403).json({ message: "User not found" });
      return;
    }

    // Add user payload to request object
    req.user = {
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
    };

    next(); // Proceed to the next middleware or route handler
  } catch (err) {
    if (err instanceof jwt.JsonWebTokenError) {
      res.status(403).json({ message: "Invalid or expired token" });
      return;
    }
    console.error("Authentication error:", err);
    res
      .status(500)
      .json({ message: "Internal server error during authentication" });
    return;
  }
};
