import { Router } from "express";
import { authenticateToken } from "../middleware/auth.middleware";
import {
  addCoachee,
  addCoachNotes,
  getCoacheeDetails,
  getCoacheesForCoach,
} from "../controllers/coach.controller";

const router = Router();

router.use(authenticateToken);

router.get("/coachees", getCoacheesForCoach);
router.post("/coachee", addCoachee);
router.put("/coachee/notes", addCoachNotes);
router.get("/coachee/:coacheeId/details", getCoacheeDetails);

export default router;
