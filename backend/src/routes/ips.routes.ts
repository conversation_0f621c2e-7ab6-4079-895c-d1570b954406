import { Router } from "express";
import { ipsController } from "../controllers/ips.controller";
import { authenticateToken } from "../middleware/auth.middleware";

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// IPS CRUD routes
router.post("/", ipsController.create);
router.get("/", ipsController.getAll);
router.get("/trends", ipsController.getTrends);
router.get("/:id", ipsController.getById);
router.put("/:id", ipsController.update);
router.delete("/:id", ipsController.delete);

export default router;
