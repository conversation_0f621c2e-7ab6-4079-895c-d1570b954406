import { Router } from "express";
import { authenticateToken } from "../middleware/auth.middleware";
import { evaluationController } from "../controllers/evaluation.controller";

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Evaluation CRUD routes
router.post("/", evaluationController.create);
router.get("/", evaluationController.getAll);
router.get("/:id", evaluationController.getById);
router.put("/:id", evaluationController.update);
router.delete("/:id", evaluationController.delete);

// Evaluation analytics routes
router.get("/coachee/:coacheeId/trends", evaluationController.getTrends);
router.get("/coachee/:coacheeId/latest", evaluationController.getLatest);

export default router;
