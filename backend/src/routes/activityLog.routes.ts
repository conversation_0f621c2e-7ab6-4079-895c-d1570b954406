import { Router } from "express";
import { ActivityLogController } from "../controllers/activityLog.controller";
import { authenticateToken } from "../middleware/auth.middleware";

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Activity log routes
router.get("/", ActivityLogController.getActivityLogs);
router.get("/recent", ActivityLogController.getRecentActivity);
router.get("/stats", ActivityLogController.getActivityStats);
router.get("/coachee/:coacheeId", ActivityLogController.getCoacheeActivityLogs);
router.get("/coach/:coachId", ActivityLogController.getCoachActivityLogs);

export default router;
