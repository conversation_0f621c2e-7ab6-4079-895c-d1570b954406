import { Router } from 'express';
import {
  getAllVisualizations,
  getVisualizationById,
  createVisualization,
  updateVisualization,
  deleteVisualization,
} from '../controllers/visualization.controller';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

router.use(authenticateToken);

router.get('/', getAllVisualizations);
router.post('/', createVisualization); // TODO: Add role-based authorization
router.get('/:id', getVisualizationById);
router.put('/:id', updateVisualization); // TODO: Add role-based authorization
router.delete('/:id', deleteVisualization); // TODO: Add role-based authorization

export default router;
