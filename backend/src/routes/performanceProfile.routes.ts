import { Router } from "express";
import { authenticateToken } from "../middleware/auth.middleware";
import { performanceProfileController } from "../controllers/performanceProfile.controller";

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Performance Profile CRUD routes
router.post("/", performanceProfileController.createPerformanceProfile);
router.get("/", performanceProfileController.getPerformanceProfiles);
router.get("/latest-goals", performanceProfileController.getLatestActiveGoals);
router.get("/:id", performanceProfileController.getPerformanceProfileById);
router.put("/:id/goals", performanceProfileController.updatePerformanceGoals);
router.delete("/:id", performanceProfileController.deletePerformanceProfile);

export default router;
