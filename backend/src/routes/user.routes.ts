import { Router } from "express";
import {
  getAllUsers,
  getUserDetailsByEmail,
  getUserDetailsById,
  getUserDetailsByToken,
} from "../controllers/user.controller";
import { authenticateToken } from "../middleware/auth.middleware";

const router = Router();

router.use(authenticateToken);

router.get("/", getUserDetailsByToken);
router.get("/all", getAllUsers);
router.get("/email/:email", getUserDetailsByEmail);
router.get("/id/:id", getUserDetailsById);

export default router;
