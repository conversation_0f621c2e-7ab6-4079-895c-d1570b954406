import { Router } from "express";
import { uploadImage, deleteImage, uploadMiddleware } from "../controllers/image.controller";
import { authenticateToken } from "../middleware/auth.middleware";

const router = Router();

// Apply authentication middleware to all image routes
router.use(authenticateToken);

// Image upload route
router.post("/upload", uploadMiddleware, uploadImage);

// Image deletion route
router.delete("/:filename", deleteImage);

export default router;
