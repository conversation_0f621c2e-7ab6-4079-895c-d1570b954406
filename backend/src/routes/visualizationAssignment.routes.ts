import express from "express";
import { visualizationAssignmentController } from "../controllers/visualizationAssignment.controller";
import { authenticateToken } from "../middleware/auth.middleware";

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Routes for visualization assignments
router.post("/", visualizationAssignmentController.create);
router.get("/", visualizationAssignmentController.getAll);
router.patch("/:id/status", visualizationAssignmentController.updateStatus);

export default router;
