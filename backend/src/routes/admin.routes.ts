import { Router } from "express";
import { authenticateToken } from "../middleware/auth.middleware";
import {
  getCoach,
  getCoachees,
  getUserDetailsById,
} from "../controllers/admin.controller";

const router = Router();

router.use(authenticateToken);

router.get("/user/:userId", getUserDetailsById);
router.get("/coach/:coachId/coachees", getCoachees);
router.get("/coachee/:coacheeId/coach", getCoach);

export default router;
