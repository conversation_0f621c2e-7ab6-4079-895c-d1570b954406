import { Router } from "express";
import { authenticateToken } from "../middleware/auth.middleware";
import { periodizationController } from "../controllers/periodization.controller";

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Periodization CRUD routes
router.post("/", periodizationController.create);
router.post("/preview", periodizationController.preview);
router.get("/", periodizationController.getAll);
router.get("/:id", periodizationController.getById);
router.patch("/:id/status", periodizationController.updateStatus);
router.delete("/:id", periodizationController.delete);

export default router;
