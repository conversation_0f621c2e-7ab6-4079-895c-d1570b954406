import { Router } from 'express';
import {
  getAllOrganizations,
  getOrganizationById,
  createOrganization,
  updateOrganization,
  deleteOrganization,
  addCoachToOrganization, // Placeholder
  addCoacheeToOrganization, // Placeholder
} from '../controllers/organization.controller';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

router.use(authenticateToken);

// Basic Organization CRUD
router.get('/', getAllOrganizations);
router.post('/', createOrganization); // TODO: Role checks needed
router.get('/:id', getOrganizationById); // TODO: Check user membership
router.put('/:id', updateOrganization); // TODO: Role checks needed (e.g., Org Admin)
router.delete('/:id', deleteOrganization); // TODO: Role checks needed (e.g., Org Admin)

// --- Member Management --- 
// TODO: These probably need more specific role checks (e.g., Org Admin or Coach)

// Example: Add Coach to Org
router.post('/:organizationId/coaches', addCoachToOrganization);

// Example: Add Coachee to Org
router.post('/:organizationId/coachees', addCoacheeToOrganization);

// TODO: Add routes for removing members, listing members etc.
// router.delete('/:organizationId/coaches/:userId', removeCoachFromOrganization);
// router.delete('/:organizationId/coachees/:userId', removeCoacheeFromOrganization);
// router.get('/:organizationId/coaches', getCoachesInOrganization);
// router.get('/:organizationId/coachees', getCoacheesInOrganization);


export default router;
