import { Router } from "express";
import {
  getAllExercises,
  getExerciseById,
  createExercise,
  updateExercise,
  deleteExercise,
} from "../controllers/exercise.controller";
import { authenticateToken } from "../middleware/auth.middleware";

const router = Router();

// Apply auth middleware to all exercise routes
router.use(authenticateToken);

// Exercise CRUD routes
router.get("/", getAllExercises); // Get all exercises (filtered by role)
router.post("/", createExercise); // Create new exercise (COACH/ADMIN only)
router.get("/:id", getExerciseById); // Get exercise by ID (with permissions)
router.put("/:id", updateExercise); // Update exercise (creator/ADMIN only)
router.delete("/:id", deleteExercise); // Delete exercise (creator/ADMIN only)

export default router;
