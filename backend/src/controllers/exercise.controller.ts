import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import prisma from "../prisma";
import { randomUUID } from "crypto";

// Get all exercises
export const getAllExercises: RequestHandler = async (req, res) => {
  try {
    const exercises = await prisma.exercise.findMany();
    res.status(200).json(exercises);
    return; // Explicit return
  } catch (error) {
    console.error("Error fetching exercises:", error);
    res.status(500).json({ message: "Failed to fetch exercises" });
    return; // Explicit return
  }
};

// Get exercise by ID
export const getExerciseById: RequestHandler = async (req, res) => {
  const { id } = req.params;
  try {
    const exercise = await prisma.exercise.findUnique({
      where: { id },
    });
    if (!exercise) {
      res.status(404).json({ message: "Exercise not found" });
      return; // Already has explicit return
    }
    // hr_admin is forbidden to access exercise details
    if (req.user?.role === "HR_ADMIN") {
      res.status(403).json({ message: "Forbidden" });
      return; // Explicit return
    }

    res.status(200).json(exercise);
    return; // Add explicit return
  } catch (error) {
    console.error("Error fetching exercise:", error);
    res.status(500).json({ message: "Failed to fetch exercise" });
    return; // Add explicit return
  }
};

// Create new exercise
export const createExercise: RequestHandler = async (req, res) => {
  // TODO: Add validation for request body (e.g., using Zod or express-validator)
  const { id, name, description, questions } = req.body;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  // Basic validation for required fields
  if (!name || !questions) {
    res
      .status(400)
      .json({ message: "Name and questions are required fields." });
    return;
  }

  try {
    const newExercise = await prisma.exercise.create({
      data: {
        id: id ?? randomUUID(),
        name,
        description,
        questions, // Expecting a JSON object/array here
        createdBy: userId,
        updatedBy: userId, // Set initial updater as creator
      },
    });
    res.status(201).json(newExercise);
    return;
  } catch (error) {
    console.error("Error creating exercise:", error);
    res.status(500).json({ message: "Failed to create exercise" });
    return;
  }
};

// Update exercise
export const updateExercise: RequestHandler = async (req, res) => {
  const { id } = req.params;
  // TODO: Add validation
  const { name, description, questions } = req.body;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }
  // if note admin or coach, they are forbidden to update
  if (req.user?.role !== "ADMIN" && req.user?.role !== "COACH") {
    res.status(403).json({ message: "Forbidden" });
    return;
  }

  // Ensure at least one field is being updated (optional, based on requirements)
  if (
    name === undefined &&
    description === undefined &&
    questions === undefined
  ) {
    res.status(400).json({ message: "No fields provided for update." });
    return;
  }

  try {
    // Check if exercise exists first (optional but good practice)
    const existingExercise = await prisma.exercise.findUnique({
      where: { id },
    });
    if (!existingExercise) {
      res.status(404).json({ message: "Exercise not found" });
      return;
    }

    const updatedExercise = await prisma.exercise.update({
      where: { id },
      data: {
        name,
        description,
        questions,
        updatedBy: userId,
      },
    });
    res.status(200).json(updatedExercise);
    return;
  } catch (error) {
    console.error("Error updating exercise:", error);
    res.status(500).json({ message: "Failed to update exercise" });
    return;
    // Note: Prisma update throws if record not found, so explicit check isn't strictly needed
    // but the catch block above handles it less gracefully than the explicit check.
  }
};

// Delete exercise
export const deleteExercise: RequestHandler = async (req, res) => {
  const { id } = req.params;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  try {
    // Check existence and authorization before deleting
    const existingExercise = await prisma.exercise.findUnique({
      where: { id },
      include: {
        assignments: {
          select: { id: true },
        },
      },
    });

    if (!existingExercise) {
      res.status(404).json({ message: "Exercise not found" });
      return;
    }

    // Authorization check - only creator or ADMIN can delete
    if (existingExercise.createdBy !== userId && req.user?.role !== "ADMIN") {
      res.status(403).json({
        message:
          "Forbidden: Only the creator or admin can delete this exercise",
      });
      return;
    }

    // Log the deletion for audit purposes
    const assignmentCount = existingExercise.assignments.length;
    console.log(
      `Deleting exercise "${existingExercise.name}" (ID: ${id}) with ${assignmentCount} assignments`
    );

    // Delete the exercise (cascading will handle assignments and submissions)
    await prisma.exercise.delete({
      where: { id },
    });

    res.status(200).json({
      message: "Exercise deleted successfully",
      deletedAssignments: assignmentCount,
    });
    return;
  } catch (error) {
    console.error("Error deleting exercise:", error);

    // Prisma delete throws if record doesn't exist
    if (error instanceof Error && error.message.includes("does not exist")) {
      res.status(404).json({ message: "Exercise not found" });
      return;
    }

    res.status(500).json({ message: "Failed to delete exercise" });
    return;
  }
};
