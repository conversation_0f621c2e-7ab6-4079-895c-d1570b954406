import { Request, Response } from "express";
import { EvaluationService } from "../services/evaluation.service";

export const evaluationController = {
  // Create a new evaluation
  create: async (req: Request, res: Response) => {
    try {
      const {
        coacheeId,
        title,
        notes,
        composure,
        concentration,
        confidence,
        copeability,
        cohesion,
      } = req.body;

      if (!coacheeId) {
        res.status(400).json({ error: "coacheeId is required" });
        return;
      }

      if (!req.user) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      // Validate if the user is a coach
      if (req.user.role !== "COACH") {
        res.status(403).json({ error: "Forbidden" });
        return;
      }

      // Validate B3-5C scores
      if (
        composure === undefined ||
        concentration === undefined ||
        confidence === undefined ||
        copeability === undefined ||
        cohesion === undefined
      ) {
        res.status(400).json({
          error: "All B3-5C scores (composure, concentration, confidence, copeability, cohesion) are required",
        });
        return;
      }

      const coachId = req.user.userId;

      const evaluation = await EvaluationService.createEvaluation({
        coacheeId,
        coachId,
        title,
        notes,
        composure,
        concentration,
        confidence,
        copeability,
        cohesion,
      });

      res.status(201).json(evaluation);
    } catch (error: any) {
      console.error("Error creating evaluation:", error);
      res.status(400).json({ error: error.message || "Failed to create evaluation" });
    }
  },

  // Get evaluations with filters
  getAll: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const role = req.user?.role;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      const { coacheeId, limit, offset, startDate, endDate } = req.query;

      let filters: any = {
        limit: limit ? parseInt(limit as string) : undefined,
        offset: offset ? parseInt(offset as string) : undefined,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
      };

      // Set filters based on user role
      if (role === "COACH") {
        filters.coachId = userId;
        if (coacheeId) {
          filters.coacheeId = coacheeId as string;
        }
      } else if (role === "COACHEE") {
        filters.coacheeId = userId;
      } else if (role === "ADMIN") {
        // Admin can see all evaluations
        if (coacheeId) {
          filters.coacheeId = coacheeId as string;
        }
      } else {
        res.status(403).json({ error: "Forbidden" });
        return;
      }

      const result = await EvaluationService.getEvaluations(filters);
      res.json(result);
    } catch (error: any) {
      console.error("Error fetching evaluations:", error);
      res.status(400).json({ error: error.message || "Failed to fetch evaluations" });
    }
  },

  // Get a single evaluation by ID
  getById: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;
      const role = req.user?.role;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      let evaluation;
      if (role === "COACH") {
        evaluation = await EvaluationService.getEvaluationById(id, userId);
      } else if (role === "ADMIN") {
        evaluation = await EvaluationService.getEvaluationById(id);
      } else if (role === "COACHEE") {
        evaluation = await EvaluationService.getEvaluationById(id);
        // Verify the coachee can only see their own evaluations
        if (evaluation && evaluation.coacheeId !== userId) {
          res.status(403).json({ error: "Not authorized to access this evaluation" });
          return;
        }
      } else {
        res.status(403).json({ error: "Forbidden" });
        return;
      }

      if (!evaluation) {
        res.status(404).json({ error: "Evaluation not found" });
        return;
      }

      res.json(evaluation);
    } catch (error: any) {
      console.error("Error fetching evaluation:", error);
      res.status(400).json({ error: error.message || "Failed to fetch evaluation" });
    }
  },

  // Update an evaluation
  update: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const {
        title,
        notes,
        composure,
        concentration,
        confidence,
        copeability,
        cohesion,
      } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      // Only coaches can update evaluations
      if (req.user?.role !== "COACH") {
        res.status(403).json({ error: "Forbidden" });
        return;
      }

      const evaluation = await EvaluationService.updateEvaluation({
        id,
        coachId: userId,
        title,
        notes,
        composure,
        concentration,
        confidence,
        copeability,
        cohesion,
      });

      res.json(evaluation);
    } catch (error: any) {
      console.error("Error updating evaluation:", error);
      res.status(400).json({ error: error.message || "Failed to update evaluation" });
    }
  },

  // Delete an evaluation
  delete: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      // Only coaches can delete evaluations
      if (req.user?.role !== "COACH") {
        res.status(403).json({ error: "Forbidden" });
        return;
      }

      const result = await EvaluationService.deleteEvaluation(id, userId);
      res.json(result);
    } catch (error: any) {
      console.error("Error deleting evaluation:", error);
      res.status(400).json({ error: error.message || "Failed to delete evaluation" });
    }
  },

  // Get evaluation trends for a coachee
  getTrends: async (req: Request, res: Response) => {
    try {
      const { coacheeId } = req.params;
      const userId = req.user?.userId;
      const role = req.user?.role;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      let trends;
      if (role === "COACH") {
        trends = await EvaluationService.getEvaluationTrends(coacheeId, userId);
      } else if (role === "ADMIN") {
        trends = await EvaluationService.getEvaluationTrends(coacheeId);
      } else if (role === "COACHEE") {
        // Coachees can only see their own trends
        if (coacheeId !== userId) {
          res.status(403).json({ error: "Not authorized to access these trends" });
          return;
        }
        trends = await EvaluationService.getEvaluationTrends(coacheeId);
      } else {
        res.status(403).json({ error: "Forbidden" });
        return;
      }

      res.json(trends);
    } catch (error: any) {
      console.error("Error fetching evaluation trends:", error);
      res.status(400).json({ error: error.message || "Failed to fetch evaluation trends" });
    }
  },

  // Get latest evaluation for a coachee
  getLatest: async (req: Request, res: Response) => {
    try {
      const { coacheeId } = req.params;
      const userId = req.user?.userId;
      const role = req.user?.role;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      let evaluation;
      if (role === "COACH") {
        evaluation = await EvaluationService.getLatestEvaluation(coacheeId, userId);
      } else if (role === "ADMIN") {
        evaluation = await EvaluationService.getLatestEvaluation(coacheeId);
      } else if (role === "COACHEE") {
        // Coachees can only see their own latest evaluation
        if (coacheeId !== userId) {
          res.status(403).json({ error: "Not authorized to access this evaluation" });
          return;
        }
        evaluation = await EvaluationService.getLatestEvaluation(coacheeId);
      } else {
        res.status(403).json({ error: "Forbidden" });
        return;
      }

      if (!evaluation) {
        res.status(404).json({ error: "No evaluations found" });
        return;
      }

      res.json(evaluation);
    } catch (error: any) {
      console.error("Error fetching latest evaluation:", error);
      res.status(400).json({ error: error.message || "Failed to fetch latest evaluation" });
    }
  },
};
