import { Request, Response } from "express";
import { ActivityLogService, ActivityLogFilters } from "../services/activityLog.service";
import { ActivityEventType } from "@prisma/client";

export class ActivityLogController {
  /**
   * Get activity logs with optional filters
   * GET /api/activity-logs
   */
  static async getActivityLogs(req: Request, res: Response) {
    try {
      const {
        coacheeId,
        coachId,
        eventType,
        startDate,
        endDate,
        limit,
        offset,
      } = req.query;

      const filters: ActivityLogFilters = {};

      if (coacheeId) {
        filters.coacheeId = coacheeId as string;
      }

      if (coachId) {
        filters.coachId = coachId as string;
      }

      if (eventType && Object.values(ActivityEventType).includes(eventType as ActivityEventType)) {
        filters.eventType = eventType as ActivityEventType;
      }

      if (startDate) {
        filters.startDate = new Date(startDate as string);
      }

      if (endDate) {
        filters.endDate = new Date(endDate as string);
      }

      if (limit) {
        filters.limit = parseInt(limit as string, 10);
      }

      if (offset) {
        filters.offset = parseInt(offset as string, 10);
      }

      const result = await ActivityLogService.getActivityLogs(filters);

      res.json({
        success: true,
        data: result.activityLogs,
        pagination: {
          total: result.total,
          limit: filters.limit || 50,
          offset: filters.offset || 0,
          hasMore: result.hasMore,
        },
      });
    } catch (error) {
      console.error("Error in getActivityLogs:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch activity logs",
      });
    }
  }

  /**
   * Get activity logs for a specific coachee
   * GET /api/activity-logs/coachee/:coacheeId
   */
  static async getCoacheeActivityLogs(req: Request, res: Response) {
    try {
      const { coacheeId } = req.params;
      const { eventType, startDate, endDate, limit, offset } = req.query;

      const filters: Omit<ActivityLogFilters, "coacheeId"> = {};

      if (eventType && Object.values(ActivityEventType).includes(eventType as ActivityEventType)) {
        filters.eventType = eventType as ActivityEventType;
      }

      if (startDate) {
        filters.startDate = new Date(startDate as string);
      }

      if (endDate) {
        filters.endDate = new Date(endDate as string);
      }

      if (limit) {
        filters.limit = parseInt(limit as string, 10);
      }

      if (offset) {
        filters.offset = parseInt(offset as string, 10);
      }

      const result = await ActivityLogService.getCoacheeActivityLogs(coacheeId, filters);

      res.json({
        success: true,
        data: result.activityLogs,
        pagination: {
          total: result.total,
          limit: filters.limit || 50,
          offset: filters.offset || 0,
          hasMore: result.hasMore,
        },
      });
    } catch (error) {
      console.error("Error in getCoacheeActivityLogs:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch coachee activity logs",
      });
    }
  }

  /**
   * Get activity logs for a specific coach
   * GET /api/activity-logs/coach/:coachId
   */
  static async getCoachActivityLogs(req: Request, res: Response) {
    try {
      const { coachId } = req.params;
      const { eventType, startDate, endDate, limit, offset } = req.query;

      const filters: Omit<ActivityLogFilters, "coachId"> = {};

      if (eventType && Object.values(ActivityEventType).includes(eventType as ActivityEventType)) {
        filters.eventType = eventType as ActivityEventType;
      }

      if (startDate) {
        filters.startDate = new Date(startDate as string);
      }

      if (endDate) {
        filters.endDate = new Date(endDate as string);
      }

      if (limit) {
        filters.limit = parseInt(limit as string, 10);
      }

      if (offset) {
        filters.offset = parseInt(offset as string, 10);
      }

      const result = await ActivityLogService.getCoachActivityLogs(coachId, filters);

      res.json({
        success: true,
        data: result.activityLogs,
        pagination: {
          total: result.total,
          limit: filters.limit || 50,
          offset: filters.offset || 0,
          hasMore: result.hasMore,
        },
      });
    } catch (error) {
      console.error("Error in getCoachActivityLogs:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch coach activity logs",
      });
    }
  }

  /**
   * Get recent activity for dashboard
   * GET /api/activity-logs/recent
   */
  static async getRecentActivity(req: Request, res: Response) {
    try {
      const { limit } = req.query;
      const limitNum = limit ? parseInt(limit as string, 10) : 10;

      const activityLogs = await ActivityLogService.getRecentActivity(limitNum);

      res.json({
        success: true,
        data: activityLogs,
      });
    } catch (error) {
      console.error("Error in getRecentActivity:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch recent activity",
      });
    }
  }

  /**
   * Get activity statistics for dashboard
   * GET /api/activity-logs/stats
   */
  static async getActivityStats(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query;

      let start: Date | undefined;
      let end: Date | undefined;

      if (startDate) {
        start = new Date(startDate as string);
      }

      if (endDate) {
        end = new Date(endDate as string);
      }

      const stats = await ActivityLogService.getActivityStats(start, end);

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error("Error in getActivityStats:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch activity statistics",
      });
    }
  }
}
