import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import prisma from "../prisma";

export const getUserDetailsByToken: RequestHandler = async (req, res) => {
  if (!req.user) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }
  // get user details from id in token
  const userId = req.user.userId;
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
    },
  });
  if (!user) {
    res.status(404).json({ message: "User not found" });
    return;
  }
  // remove password hash from user object
  res.status(200).json(user);
};

export const getUserDetailsById: RequestHandler = async (req, res) => {
  const { id } = req.params;
  if (!id) {
    res.status(400).json({ message: "User ID is required" });
    return;
  }

  // check database for user with id
  const user = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
    },
  });
  if (!user) {
    res.status(404).json({ message: "User not found" });
    return;
  }
  res.status(200).json(user);
};

export const getUserDetailsByEmail: RequestHandler = async (req, res) => {
  const { email } = req.params;
  if (!email) {
    res.status(400).json({ message: "Email is required" });
    return;
  }

  // check database for user with email
  const user = await prisma.user.findUnique({
    where: { email },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
    },
  });
  if (!user) {
    res.status(404).json({ message: "User not found" });
    return;
  }
  res.status(200).json(user);
};

export const getAllUsers: RequestHandler = async (req, res) => {
  // check if user is admin
  if (req.user?.role !== "ADMIN") {
    res.status(403).json({ message: "Forbidden" });
    return;
  }
  const users = await prisma.user.findMany({
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
    },
  });
  res.status(200).json(users);
};
