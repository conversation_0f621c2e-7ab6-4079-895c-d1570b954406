import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import prisma from "../prisma";
import { TTSService, TTSOptions } from "../services/tts.service";
import { StorageService } from "../services/storage.service";

export const generateAudio: RequestHandler = async (req, res) => {
  const { id } = req.params;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  try {
    // Find the visualization
    const visualization = await prisma.visualization.findUnique({
      where: { id },
    });

    if (!visualization) {
      res.status(404).json({ message: "Visualization not found" });
      return;
    }

    // Check if description exists
    if (
      !visualization.description ||
      visualization.description.trim().length === 0
    ) {
      res.status(400).json({ message: "No description to convert to audio" });
      return;
    }

    // Extract TTS options from request body
    const { voice, speed, language } = req.body;
    const ttsOptions: TTSOptions = {
      voice,
      speed,
      language,
    };

    // Validate TTS options
    TTSService.validateOptions(ttsOptions);

    // Check if audio already exists for this text and options (caching)
    const cacheKey = StorageService.generateAudioCacheKey(
      visualization.description,
      ttsOptions
    );
    const audioExists = await StorageService.audioExists(cacheKey);

    let audioUrl: string;

    if (audioExists) {
      // Use cached audio - generate signed URL for secure access
      console.log(`Using cached audio: ${cacheKey}`);
      try {
        audioUrl = await StorageService.getAudioSignedUrl(cacheKey, 3600); // 1 hour expiry
      } catch (error) {
        console.error(
          "Failed to generate signed URL, falling back to public URL:",
          error
        );
        audioUrl = `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/audio/${cacheKey}`;
      }
    } else {
      // Generate new audio
      console.log(`Generating new audio for visualization: ${id}`);
      const audioBuffer = await TTSService.generateAudio(
        visualization.description,
        ttsOptions
      );

      // Upload to S3 with cache key as filename
      const uploadResult = await StorageService.uploadAudio(
        audioBuffer,
        cacheKey
      );

      // Generate signed URL for secure access
      try {
        audioUrl = await StorageService.getAudioSignedUrl(cacheKey, 3600); // 1 hour expiry
      } catch (error) {
        console.error(
          "Failed to generate signed URL, using upload URL:",
          error
        );
        audioUrl = uploadResult.url;
      }
    }

    // Update the visualization with the audio URL
    const updatedVisualization = await prisma.visualization.update({
      where: { id },
      data: {
        audioUrl,
        updatedBy: userId,
      },
    });

    res.status(200).json({
      message: "Audio generated successfully",
      audioUrl,
      cached: audioExists,
      visualization: updatedVisualization,
      ttsOptions,
    });
  } catch (error) {
    console.error("Error generating audio:", error);

    if (error instanceof Error) {
      // Provide more specific error messages based on the error type
      if (error.message.includes("REPLICATE_API_KEY")) {
        res.status(500).json({
          message: "TTS service configuration error. Please contact support.",
        });
      } else if (error.message.includes("Invalid response")) {
        res.status(500).json({
          message:
            "TTS service returned an unexpected response. Please try again.",
        });
      } else if (error.message.includes("Failed to download")) {
        res.status(500).json({
          message: "Generated audio could not be downloaded. Please try again.",
        });
      } else if (error.message.includes("Text is too long")) {
        res.status(400).json({
          message:
            "Text is too long. Please use shorter text (max 5000 characters).",
        });
      } else if (error.message.includes("Invalid voice")) {
        res.status(400).json({
          message: "Invalid voice selected. Please choose a valid voice.",
        });
      } else {
        res.status(400).json({ message: error.message });
      }
    } else {
      res.status(500).json({
        message: "An unexpected error occurred while generating audio",
      });
    }
  }
};

export const deleteAudio: RequestHandler = async (req, res) => {
  const { id } = req.params;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  try {
    // Find the visualization
    const visualization = await prisma.visualization.findUnique({
      where: { id },
    });

    if (!visualization) {
      res.status(404).json({ message: "Visualization not found" });
      return;
    }

    if (!visualization.audioUrl) {
      res.status(400).json({ message: "No audio file to delete" });
      return;
    }

    // Extract filename from URL
    const urlParts = visualization.audioUrl.split("/");
    const filename = urlParts[urlParts.length - 1];

    // Delete from S3
    await StorageService.deleteAudio(filename);

    // Update the visualization to remove the audio URL
    const updatedVisualization = await prisma.visualization.update({
      where: { id },
      data: {
        audioUrl: null,
        updatedBy: userId,
      },
    });

    res.status(200).json(updatedVisualization);
  } catch (error) {
    console.error("Error deleting audio:", error);
    res.status(500).json({ message: "Failed to delete audio" });
  }
};

// Generate audio from custom text (not tied to a visualization)
export const generateCustomAudio: RequestHandler = async (req, res) => {
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  try {
    const { text, voice, speed, language } = req.body;

    if (!text || text.trim().length === 0) {
      res.status(400).json({ message: "Text is required" });
      return;
    }

    const ttsOptions: TTSOptions = {
      voice,
      speed,
      language,
    };

    // Validate TTS options
    TTSService.validateOptions(ttsOptions);

    // Check if audio already exists for this text and options (caching)
    const cacheKey = StorageService.generateAudioCacheKey(text, ttsOptions);
    const audioExists = await StorageService.audioExists(cacheKey);

    let audioUrl: string;

    if (audioExists) {
      // Use cached audio - generate signed URL for secure access
      console.log(`Using cached audio: ${cacheKey}`);
      try {
        audioUrl = await StorageService.getAudioSignedUrl(cacheKey, 3600); // 1 hour expiry
      } catch (error) {
        console.error(
          "Failed to generate signed URL, falling back to public URL:",
          error
        );
        audioUrl = `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/audio/${cacheKey}`;
      }
    } else {
      // Generate new audio
      console.log(`Generating new audio for custom text`);
      const audioBuffer = await TTSService.generateAudio(text, ttsOptions);

      // Upload to S3 with cache key as filename
      const uploadResult = await StorageService.uploadAudio(
        audioBuffer,
        cacheKey
      );

      // Generate signed URL for secure access
      try {
        audioUrl = await StorageService.getAudioSignedUrl(cacheKey, 3600); // 1 hour expiry
      } catch (error) {
        console.error(
          "Failed to generate signed URL, using upload URL:",
          error
        );
        audioUrl = uploadResult.url;
      }
    }

    res.status(200).json({
      message: "Audio generated successfully",
      audioUrl,
      cached: audioExists,
      text: text.substring(0, 100) + (text.length > 100 ? "..." : ""),
      ttsOptions,
    });
  } catch (error) {
    console.error("Error generating custom audio:", error);

    if (error instanceof Error) {
      // Provide more specific error messages based on the error type
      if (error.message.includes("REPLICATE_API_KEY")) {
        res.status(500).json({
          message: "TTS service configuration error. Please contact support.",
        });
      } else if (error.message.includes("Invalid response")) {
        res.status(500).json({
          message:
            "TTS service returned an unexpected response. Please try again.",
        });
      } else if (error.message.includes("Failed to download")) {
        res.status(500).json({
          message: "Generated audio could not be downloaded. Please try again.",
        });
      } else if (error.message.includes("Text is too long")) {
        res.status(400).json({
          message:
            "Text is too long. Please use shorter text (max 5000 characters).",
        });
      } else if (error.message.includes("Invalid voice")) {
        res.status(400).json({
          message: "Invalid voice selected. Please choose a valid voice.",
        });
      } else {
        res.status(400).json({ message: error.message });
      }
    } else {
      res.status(500).json({
        message: "An unexpected error occurred while generating audio",
      });
    }
  }
};

// Get available TTS voices
export const getAvailableVoices: RequestHandler = async (_req, res) => {
  try {
    const voices = TTSService.getAvailableVoices();
    res.status(200).json({
      voices,
      defaultVoice: "af_nicole",
      supportedLanguages: ["en"],
      speedRange: { min: 0.5, max: 2.0, default: 1.0 },
    });
  } catch (error) {
    console.error("Error getting available voices:", error);
    res.status(500).json({ message: "Failed to get available voices" });
  }
};

// Get a fresh signed URL for an existing audio file
export const getAudioUrl: RequestHandler = async (req, res) => {
  const userId = req.user?.userId;
  const { id } = req.params;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  try {
    // Get the visualization to check if it has audio
    const visualization = await prisma.visualization.findUnique({
      where: { id },
    });

    if (!visualization) {
      res.status(404).json({ message: "Visualization not found" });
      return;
    }

    if (!visualization.audioUrl) {
      res
        .status(404)
        .json({ message: "No audio available for this visualization" });
      return;
    }

    // Extract the filename from the stored audioUrl
    // The audioUrl could be either a signed URL or a public URL
    let filename: string;

    if (visualization.audioUrl.includes("amazonaws.com")) {
      // Extract filename from S3 URL
      const urlParts = visualization.audioUrl.split("/");
      const audioPath = urlParts[urlParts.length - 1];
      // Remove query parameters if it's a signed URL
      filename = audioPath.split("?")[0];
    } else {
      res.status(400).json({ message: "Invalid audio URL format" });
      return;
    }

    // Generate a fresh signed URL
    try {
      const freshAudioUrl = await StorageService.getAudioSignedUrl(
        filename,
        3600
      ); // 1 hour expiry

      res.status(200).json({
        audioUrl: freshAudioUrl,
        expiresIn: 3600,
        message: "Fresh audio URL generated successfully",
      });
    } catch (error) {
      console.error("Failed to generate fresh signed URL:", error);
      res.status(500).json({ message: "Failed to generate fresh audio URL" });
    }
  } catch (error) {
    console.error("Error getting audio URL:", error);
    res.status(500).json({ message: "Failed to get audio URL" });
  }
};
