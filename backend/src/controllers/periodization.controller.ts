import { Request, Response } from "express";
import {
  PeriodizationService,
  CreatePeriodizationData,
} from "../services/periodization.service";
import { PeriodizationStatus } from "@prisma/client";

export const periodizationController = {
  /**
   * Create a new periodization
   */
  create: async (req: Request, res: Response) => {
    try {
      const {
        coacheeId,
        name,
        offSeasonStartDate,
        offSeasonEndDate,
        prepStartDate,
        prepEndDate,
        preCompStartDate,
        preCompEndDate,
        competitionStartDate,
        competitionEndDate,
        scheduleMentalToughness,
        scheduleMentalWellness,
        mentalWellnessStartDate,
        mentalWellnessEndDate,
      } = req.body;

      // Validate required fields
      if (
        !coacheeId ||
        !name ||
        !offSeasonStartDate ||
        !prepStartDate ||
        !preCompStartDate ||
        !competitionStartDate ||
        !competitionEndDate
      ) {
        res.status(400).json({
          error:
            "Missing required fields: coacheeId, name, and all phase dates are required",
        });
        return;
      }

      // Validate authentication
      if (!req.user) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      // Validate user role
      if (req.user.role !== "COACH") {
        res
          .status(403)
          .json({ error: "Only coaches can create periodizations" });
        return;
      }

      const coachId = req.user.userId;

      // Validate UUID format for coacheeId
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(coacheeId)) {
        res.status(400).json({ error: "Invalid coachee ID format" });
        return;
      }

      // Parse dates
      const periodizationData: CreatePeriodizationData = {
        coacheeId,
        coachId,
        name,
        offSeasonStartDate: new Date(offSeasonStartDate),
        offSeasonEndDate: new Date(offSeasonEndDate),
        prepStartDate: new Date(prepStartDate),
        prepEndDate: new Date(prepEndDate),
        preCompStartDate: new Date(preCompStartDate),
        preCompEndDate: new Date(preCompEndDate),
        competitionStartDate: new Date(competitionStartDate),
        competitionEndDate: new Date(competitionEndDate),
        scheduleMentalToughness: scheduleMentalToughness ?? true,
        scheduleMentalWellness: scheduleMentalWellness ?? true,
        mentalWellnessStartDate: mentalWellnessStartDate
          ? new Date(mentalWellnessStartDate)
          : undefined,
        mentalWellnessEndDate: mentalWellnessEndDate
          ? new Date(mentalWellnessEndDate)
          : undefined,
      };

      const result = await PeriodizationService.createPeriodization(
        periodizationData
      );

      res.status(201).json({
        message: "Periodization created successfully",
        periodization: result.periodization,
        assignmentCount: result.assignmentCount,
      });
    } catch (error) {
      console.error("Error creating periodization:", error);
      res.status(500).json({
        error: "Failed to create periodization",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },

  /**
   * Generate periodization preview
   */
  preview: async (req: Request, res: Response) => {
    try {
      const {
        coacheeId,
        offSeasonStartDate,
        offSeasonEndDate,
        prepStartDate,
        prepEndDate,
        preCompStartDate,
        preCompEndDate,
        competitionStartDate,
        competitionEndDate,
        scheduleMentalToughness,
        scheduleMentalWellness,
        mentalWellnessStartDate,
        mentalWellnessEndDate,
      } = req.body;

      // Validate required fields
      if (
        !coacheeId ||
        !offSeasonStartDate ||
        !prepStartDate ||
        !preCompStartDate ||
        !competitionStartDate ||
        !competitionEndDate
      ) {
        res.status(400).json({
          error:
            "Missing required fields: coacheeId and all phase dates are required",
        });
        return;
      }

      // Validate authentication
      if (!req.user) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      // Validate user role
      if (req.user.role !== "COACH") {
        res
          .status(403)
          .json({ error: "Only coaches can preview periodizations" });
        return;
      }

      const coachId = req.user.userId;

      // Parse dates
      const previewData = {
        coacheeId,
        coachId,
        offSeasonStartDate: new Date(offSeasonStartDate),
        offSeasonEndDate: new Date(offSeasonEndDate),
        prepStartDate: new Date(prepStartDate),
        prepEndDate: new Date(prepEndDate),
        preCompStartDate: new Date(preCompStartDate),
        preCompEndDate: new Date(preCompEndDate),
        competitionStartDate: new Date(competitionStartDate),
        competitionEndDate: new Date(competitionEndDate),
        scheduleMentalToughness: scheduleMentalToughness ?? true,
        scheduleMentalWellness: scheduleMentalWellness ?? true,
        mentalWellnessStartDate: mentalWellnessStartDate
          ? new Date(mentalWellnessStartDate)
          : undefined,
        mentalWellnessEndDate: mentalWellnessEndDate
          ? new Date(mentalWellnessEndDate)
          : undefined,
      };

      const preview = await PeriodizationService.generatePeriodizationPreview(
        previewData
      );

      res.json(preview);
    } catch (error) {
      console.error("Error generating periodization preview:", error);
      res.status(500).json({
        error: "Failed to generate periodization preview",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },

  /**
   * Get all periodizations for the authenticated coach
   */
  getAll: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const role = req.user?.role;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      let periodizations: any[] = [];
      if (role === "COACH") {
        periodizations = await PeriodizationService.getPeriodizationsForCoach(
          userId
        );
      } else if (role === "COACHEE") {
        periodizations = await PeriodizationService.getPeriodizationsForCoachee(
          userId
        );
      } else {
        // For admin users, we could get all periodizations, but for now just return empty
        periodizations = [];
      }

      res.json(periodizations);
    } catch (error) {
      console.error("Error fetching periodizations:", error);
      res.status(500).json({ error: "Failed to fetch periodizations" });
    }
  },

  /**
   * Get periodization by ID
   */
  getById: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      const periodization = await PeriodizationService.getPeriodizationById(id);

      if (!periodization) {
        res.status(404).json({ error: "Periodization not found" });
        return;
      }

      // Check authorization - only coach who created it or the coachee can view
      if (
        periodization.coachId !== userId &&
        periodization.coacheeId !== userId
      ) {
        res.status(403).json({ error: "Access denied" });
        return;
      }

      res.json(periodization);
    } catch (error) {
      console.error("Error fetching periodization:", error);
      res.status(500).json({ error: "Failed to fetch periodization" });
    }
  },

  /**
   * Update periodization status
   */
  updateStatus: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      if (!Object.values(PeriodizationStatus).includes(status)) {
        res.status(400).json({ error: "Invalid status value" });
        return;
      }

      // Check if periodization exists and user has permission
      const periodization = await PeriodizationService.getPeriodizationById(id);
      if (!periodization) {
        res.status(404).json({ error: "Periodization not found" });
        return;
      }

      // Only the coach who created it can update status
      if (periodization.coachId !== userId) {
        res
          .status(403)
          .json({
            error: "Only the creating coach can update periodization status",
          });
        return;
      }

      const updatedPeriodization =
        await PeriodizationService.updatePeriodizationStatus(id, status);

      res.json({
        message: "Periodization status updated successfully",
        periodization: updatedPeriodization,
      });
    } catch (error) {
      console.error("Error updating periodization status:", error);
      res.status(500).json({ error: "Failed to update periodization status" });
    }
  },

  /**
   * Delete periodization
   */
  delete: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { deleteAssignments } = req.query;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      // Check if periodization exists and user has permission
      const periodization = await PeriodizationService.getPeriodizationById(id);
      if (!periodization) {
        res.status(404).json({ error: "Periodization not found" });
        return;
      }

      // Only the coach who created it can delete
      if (periodization.coachId !== userId) {
        res
          .status(403)
          .json({ error: "Only the creating coach can delete periodization" });
        return;
      }

      await PeriodizationService.deletePeriodization(
        id,
        deleteAssignments === "true"
      );

      res.json({ message: "Periodization deleted successfully" });
    } catch (error) {
      console.error("Error deleting periodization:", error);
      res.status(500).json({ error: "Failed to delete periodization" });
    }
  },
};
