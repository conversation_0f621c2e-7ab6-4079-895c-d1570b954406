import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import prisma from "../prisma";
import { VisualizationService } from "../services/visualization.service";

// Get all visualizations
export const getAllVisualizations: RequestHandler = async (req, res) => {
  try {
    const result = await VisualizationService.getVisualizations();
    res.status(200).json(result.visualizations);
  } catch (error) {
    console.error("Error fetching visualizations:", error);
    res.status(500).json({ message: "Failed to fetch visualizations" });
  }
};

// Get visualization by ID
export const getVisualizationById: RequestHandler = async (req, res) => {
  const { id } = req.params;
  try {
    const visualization = await VisualizationService.getVisualizationById(id);
    res.status(200).json(visualization);
    return;
  } catch (error) {
    console.error("Error fetching visualization:", error);
    if (error instanceof Error && error.message === "Visualization not found") {
      res.status(404).json({ message: "Visualization not found" });
      return;
    }
    res.status(500).json({ message: "Failed to fetch visualization" });
    return;
  }
};

// Create new visualization
export const createVisualization: RequestHandler = async (req, res) => {
  // TODO: Add validation (e.g., Zod)
  const { id, title, description, audioUrl } = req.body;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  if (!title) {
    res.status(400).json({ message: "Title is a required field." });
    return;
  }

  try {
    const newVisualization = await prisma.visualization.create({
      data: {
        id,
        title,
        description,
        audioUrl,
        createdBy: userId,
        updatedBy: userId, // Set initial updater as creator
      },
    });
    res.status(201).json(newVisualization);
    return;
  } catch (error) {
    console.error("Error creating visualization:", error);
    res.status(500).json({ message: "Failed to create visualization" });
    return;
  }
};

// Update visualization
export const updateVisualization: RequestHandler = async (req, res) => {
  const { id } = req.params;
  // TODO: Add validation
  const { title, description, audioUrl } = req.body;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  if (
    title === undefined &&
    description === undefined &&
    audioUrl === undefined
  ) {
    res.status(400).json({ message: "No fields provided for update." });
    return;
  }

  try {
    // Optional: Check existence first
    const existingVis = await prisma.visualization.findUnique({
      where: { id },
    });
    if (!existingVis) {
      res.status(404).json({ message: "Visualization not found" });
      return;
    }

    // TODO: Add authorization check

    const updatedVisualization = await prisma.visualization.update({
      where: { id },
      data: {
        title,
        description,
        audioUrl,
        updatedBy: userId,
      },
    });
    res.status(200).json(updatedVisualization);
    return;
  } catch (error) {
    if (
      error instanceof Error &&
      error.message.includes("Record to update not found")
    ) {
      // This case is handled by the explicit check above now
      res.status(404).json({ message: "Visualization not found" });
      return;
    }
    console.error("Error updating visualization:", error);
    res.status(500).json({ message: "Failed to update visualization" });
    return;
  }
};

// Delete visualization
export const deleteVisualization: RequestHandler = async (req, res) => {
  const { id } = req.params;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  try {
    // Check existence and authorization before deleting
    const existingVis = await prisma.visualization.findUnique({
      where: { id },
      include: {
        assignments: {
          select: { id: true },
        },
      },
    });

    if (!existingVis) {
      res.status(404).json({ message: "Visualization not found" });
      return;
    }

    // Authorization check - only creator or ADMIN can delete
    if (existingVis.createdBy !== userId && req.user?.role !== "ADMIN") {
      res.status(403).json({
        message:
          "Forbidden: Only the creator or admin can delete this visualization",
      });
      return;
    }

    // Log the deletion for audit purposes
    const assignmentCount = existingVis.assignments.length;
    console.log(
      `Deleting visualization "${existingVis.title}" (ID: ${id}) with ${assignmentCount} assignments`
    );

    // Delete the visualization (cascading will handle assignments)
    await prisma.visualization.delete({
      where: { id },
    });

    res.status(200).json({
      message: "Visualization deleted successfully",
      deletedAssignments: assignmentCount,
    });
    return;
  } catch (error) {
    console.error("Error deleting visualization:", error);

    // Prisma delete throws if record doesn't exist
    if (error instanceof Error && error.message.includes("does not exist")) {
      res.status(404).json({ message: "Visualization not found" });
      return;
    }

    res.status(500).json({ message: "Failed to delete visualization" });
    return;
  }
};
