import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import prisma from "../prisma";

export const getUserDetailsById: RequestHandler = async (req, res) => {
  try {
    const { userId } = req.params;
    // check if requesting user is admin
    if (req.user?.role !== "ADMIN") {
      res.status(403).json({ error: "Forbidden" });
      return;
    }
    if (!userId) {
      res.status(400).json({ error: "User ID is required" });
      return;
    }
    // Fetch user details by ID
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        createdAt: true,
        organizationMemberships: {
          select: {
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        coachRelationships: {
          select: {
            coachee: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
            coachNotes: true,
          },
        },
        coacheeRelationships: {
          select: {
            coach: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });
    if (!user) {
      res.status(404).json({ error: "User not found" });
      return;
    }
    res.status(200).json(user);
  } catch (error) {
    console.error("Error fetching user details:", error);
    res.status(400).json({ error: "Failed to fetch user details" });
    return;
  }
};

export const getCoachees: RequestHandler = async (req, res) => {
  try {
    const { coachId } = req.params;
    // check if requesting user is admin
    if (req.user?.role !== "ADMIN") {
      res.status(403).json({ error: "Forbidden" });
      return;
    }
    if (!coachId) {
      res.status(400).json({ error: "User ID is required" });
      return;
    }
    // Fetch coachees for the specified user
    const coachees = await prisma.user.findMany({
      where: {
        coacheeRelationships: {
          some: {
            coachId: coachId,
          },
        },
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        organizationMemberships: {
          select: {
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
    res.status(200).json(coachees);
  } catch (error) {
    res.status(400).json({ error: "Failed to fetch coachees" });
    return;
  }
};

export const getCoach: RequestHandler = async (req, res) => {
  try {
    const { coacheeId } = req.params;
    // check if requesting user is admin
    if (req.user?.role !== "ADMIN") {
      res.status(403).json({ error: "Forbidden" });
      return;
    }
    if (!coacheeId) {
      res.status(400).json({ error: "User ID is required" });
      return;
    }
    // Fetch coach details
    const coach = await prisma.user.findFirst({
      where: {
        coachRelationships: {
          some: {
            coacheeId: coacheeId,
          },
        },
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        organizationMemberships: {
          select: {
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
    if (!coach) {
      res.status(404).json({ error: "Coach not found" });
      return;
    }
    res.status(200).json(coach);
  } catch (error) {
    res.status(400).json({ error: "Failed to fetch coach details" });
  }
};
