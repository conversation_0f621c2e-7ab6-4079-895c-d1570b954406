import { RequestH<PERSON><PERSON>, Request } from "express";
import { StorageService } from "../services/storage.service";
import multer, { FileFilterCallback } from "multer";
import sharp from "sharp";

// Extend Request interface to include file property
interface MulterRequest extends Request {
  file?: Express.Multer.File;
}

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (
    req: Request,
    file: Express.Multer.File,
    cb: FileFilterCallback
  ) => {
    // Check file type
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(
        new Error(
          "Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed."
        )
      );
    }
  },
});

export const uploadMiddleware = upload.single("image");

export const uploadImage: RequestHandler = async (req, res) => {
  const multerReq = req as MulterRequest;
  const userId = multerReq.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  try {
    if (!multerReq.file) {
      res.status(400).json({ message: "No image file provided" });
      return;
    }

    const { buffer, originalname, mimetype } = multerReq.file;

    // Process image with Sharp for optimization
    let processedBuffer: Buffer;
    let finalMimetype = mimetype;

    try {
      // Resize and optimize the image
      const sharpInstance = sharp(buffer);
      const metadata = await sharpInstance.metadata();

      // Resize if too large (max 1200px width)
      if (metadata.width && metadata.width > 1200) {
        sharpInstance.resize(1200, null, {
          withoutEnlargement: true,
          fit: "inside",
        });
      }

      // Convert to JPEG for better compression if it's not PNG/GIF (to preserve transparency)
      if (mimetype === "image/jpeg" || mimetype === "image/webp") {
        processedBuffer = await sharpInstance
          .jpeg({ quality: 85, progressive: true })
          .toBuffer();
        finalMimetype = "image/jpeg";
      } else if (mimetype === "image/png") {
        processedBuffer = await sharpInstance
          .png({ quality: 85, progressive: true })
          .toBuffer();
      } else {
        // Keep original format for GIF
        processedBuffer = buffer;
      }
    } catch (sharpError) {
      console.error("Error processing image with Sharp:", sharpError);
      // Fallback to original buffer if Sharp fails
      processedBuffer = buffer;
    }

    // Generate unique filename
    const filename = StorageService.generateImageFilename(originalname);

    // Upload to S3
    const uploadResult = await StorageService.uploadImage(
      processedBuffer,
      filename,
      finalMimetype
    );

    console.log(
      `Image uploaded successfully by user ${userId}: ${uploadResult.url}`
    );

    res.status(200).json({
      message: "Image uploaded successfully",
      imageUrl: uploadResult.url,
      filename: filename,
      size: uploadResult.size,
      originalName: originalname,
    });
  } catch (error) {
    console.error("Error uploading image:", error);

    if (error instanceof Error) {
      if (error.message.includes("Invalid file type")) {
        res.status(400).json({ message: error.message });
      } else if (error.message.includes("File too large")) {
        res.status(400).json({
          message: "File size exceeds 10MB limit",
        });
      } else {
        res.status(500).json({
          message: "Failed to upload image",
          error: error.message,
        });
      }
    } else {
      res.status(500).json({
        message: "An unexpected error occurred while uploading image",
      });
    }
  }
};

export const deleteImage: RequestHandler = async (req, res) => {
  const userId = req.user?.userId;
  const { filename } = req.params;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  try {
    if (!filename) {
      res.status(400).json({ message: "Filename is required" });
      return;
    }

    // Check if image exists
    const exists = await StorageService.imageExists(filename);
    if (!exists) {
      res.status(404).json({ message: "Image not found" });
      return;
    }

    // Delete from S3
    await StorageService.deleteImage(filename);

    console.log(`Image deleted successfully by user ${userId}: ${filename}`);

    res.status(200).json({
      message: "Image deleted successfully",
      filename,
    });
  } catch (error) {
    console.error("Error deleting image:", error);
    res.status(500).json({
      message: "Failed to delete image",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
