import dotenv from "dotenv";
dotenv.config();

const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    throw new Error(`Missing required environment variable: ${key}`);
  }
  return value || defaultValue!;
};

export const env = {
  port: parseInt(getEnvVar("PORT", "3001"), 10),
  jwtSecret: getEnvVar("JWT_SECRET"),

  // Email configuration
  smtpHost: getEnvVar("SMTP_HOST"),
  smtpPort: parseInt(getEnvVar("SMTP_PORT", "587"), 10),
  smtpSecure: getEnvVar("SMTP_SECURE", "false") === "true",
  smtpUser: getEnvVar("SMTP_USER"),
  smtpPassword: getEnvVar("SMTP_PASSWORD"),
  emailFrom: getEnvVar("EMAIL_FROM"),
  frontendUrl: getEnvVar("FRONTEND_URL", "http://localhost:5173"),
};
