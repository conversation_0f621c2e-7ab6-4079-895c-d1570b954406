import { PerformanceCategory } from "@prisma/client";

export interface PerformanceProfileGoal {
  id: string;
  category: PerformanceCategory;
  goalName: string;
  currentRating: number;
  targetRating: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface PerformanceProfile {
  id: string;
  coacheeId: string;
  startDate: Date;
  targetDate: Date;
  createdAt: Date;
  updatedAt: Date;
  goals: PerformanceProfileGoal[];
  coachee: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreatePerformanceProfileRequest {
  startDate: string;
  targetDate: string;
  goals: {
    category: PerformanceCategory;
    goalName: string;
    currentRating: number;
    targetRating: number;
  }[];
}

export interface UpdatePerformanceGoalsRequest {
  goals: {
    id: string;
    goalName?: string;
    currentRating?: number;
    targetRating?: number;
    isActive?: boolean;
  }[];
}

export interface LatestActiveGoalsResponse {
  profileId: string;
  startDate: Date;
  targetDate: Date;
  goalsByCategory: Record<PerformanceCategory, PerformanceProfileGoal[]>;
}

export { PerformanceCategory };
