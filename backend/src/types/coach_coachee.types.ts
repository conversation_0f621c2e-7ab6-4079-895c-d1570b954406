/**
 * Represents the association between a coach and a coachee.
 *
 * This interface is used to link coaches to their coachees.
 * It is a one-to-many relationship where a coach can have multiple coachees.
 */
export interface CoachCoachee {
  /** ID of the coach. */
  coachId: string;
  /** ID of the coachee. */
  coacheeId: string;
  /** Timestamp when the relationship was assigned */
  assignedAt: string;
  /** Notes specific to this coach-coachee relationship. */
  coachNotes: string | null;
}
