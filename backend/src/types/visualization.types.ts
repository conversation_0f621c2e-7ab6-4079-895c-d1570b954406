/**
 * Represents a visualization exercise. A coach may create a visualization exercise
 * for a coachee to read through/listen to.
 *
 * The visualization contains a text transcript of the visualization, and a URL to
 * the audio file, stored in the storage bucket.
 */
export interface Visualization {
  /** Unique identifier for the visualization. */
  id: string;
  /** The title of the visualization. */
  title: string;
  /** A description of the visualization. */
  description: string;
  /** The URL to the audio file, stored in the storage bucket. */
  audioUrl: string;
  /** Timestamp when the visualization was created. */
  createdAt: string;
  /** Created by user ID. */
  createdBy: string;
  /** Timestamp when the visualization was last updated. */
  updatedAt: string;
  /** Updated by user ID. */
  updatedBy: string;
}

export interface CreateVisualizationDto {
  title: string;
  description: string;
  audioUrl?: string;
}

export interface UpdateVisualizationDto {
  title?: string;
  description?: string;
  audioUrl?: string;
}

export interface VisualizationResponse {
  id: string;
  title: string;
  description: string;
  audioUrl: string | null;
  createdBy: string;
  updatedBy: string;
  createdAt: Date;
  updatedAt: Date;
}
