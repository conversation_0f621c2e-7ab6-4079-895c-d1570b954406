import { AssignmentStatus } from "@prisma/client";

// --- Assignment Definition ---

export interface CreateAssignmentDto {
  exerciseId: string;
  coacheeId: string;
  dueDate?: Date;
}

export interface UpdateAssignmentStatusDto {
  status: AssignmentStatus;
}

/**
 * Represents a specific instance of an exercise (Form) assigned
 * by a Coach to a Coachee.
 */
export interface Assignment {
  /** Unique identifier for this specific assignment instance. */
  id: string;
  /** ID of the exercise/form definition being assigned. Links to Form['id']. */
  exerciseId: string;
  /** ID of the Coach who assigned the exercise. Links to User['id']. */
  coachId: string;
  /** ID of the Coachee to whom the exercise is assigned. Links to User['id']. */
  coacheeId: string;
  /** Date and time when the assignment was created (ISO 8601 format recommended). */
  createdAt: string; // e.g., "2025-04-07T15:15:29.000Z"
  /** Date and time when the assignment was last updated (ISO 8601 format recommended). */
  updatedAt: string; // e.g., "2025-04-07T15:15:29.000Z"
  /** Optional due date for the assignment (ISO 8601 format recommended). */
  dueDate?: string;
  /** Current status of the assignment. */
  status: AssignmentStatus;
}

// --- Response and Answer Definitions ---

/**
 * Represents a point on the 2D graph input.
 */
export interface GraphPoint {
  x: number;
  y: number;
}

/**
 * Represents the structure of an answer for a single row in a table question.
 * Keys are the column IDs (TableColumn['id']).
 * Values are the cell inputs (string for free_text, number for likert).
 */
export type TableRowAnswer = Record<string, string | number>;

// --- Specific Answer Types (matching Question types) ---

/** Answer type for 'free_text' questions. */
export type FreeTextAnswer = string;

/**
 * Answer type for 'likert' questions.
 * - number: For single response mode.
 * - number[]: For multi-response mode (order corresponds to multi_response_labels).
 */
export type LikertAnswer = number | number[];

/** Answer type for 'task' questions (checkbox state). */
export type TaskAnswer = boolean;

/** Answer type for 'table' questions (an array of row answers). */
export type TableAnswer = TableRowAnswer[];

/** Answer type for 'graph_2d' questions (an array of plotted points). */
export type Graph2DAnswer = GraphPoint[];

/**
 * Union type representing the possible data structure for any single answer.
 */
export type AnswerValue =
  | FreeTextAnswer
  | LikertAnswer
  | TaskAnswer
  | TableAnswer
  | Graph2DAnswer;

/**
 * Represents the Coachee's submitted answers for a specific Assignment.
 */
export interface Response {
  /** Unique identifier for this response submission. */
  id: string;
  /** ID of the assignment this response belongs to. Links to Assignment['id']. */
  assignmentId: string;
  /** Date and time when the response was submitted (ISO 8601 format recommended). */
  submittedDate: string;
  /**
   * A collection of answers provided by the coachee.
   * The keys are the question IDs (Question['id']) from the corresponding Form/Exercise.
   * The values are the actual answer data, conforming to the AnswerValue type.
   * Note: Runtime checks might be needed to ensure the AnswerValue structure matches the specific question type.
   */
  answers: Record<string, AnswerValue>; // Map<QuestionId, AnswerValue>
}

// --- Example Usage (Conceptual) ---
/*
  // Assume we have user objects coachUser and coacheeUser
  // Assume we have an existing Form object exerciseForm with id "form-weekly-1"
  
  const assignmentInstance: Assignment = {
    id: "asgn-xyz-123",
    exerciseId: "form-weekly-1", // Links to the Form definition
    coachId: coachUser.id,
    coacheeId: coacheeUser.id,
    assignedDate: new Date().toISOString(), // Current time: 2025-04-07T15:15:29.000Z
    status: 'assigned',
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // Due in 7 days
  };
  
  // Later, when the coachee submits:
  const coacheeResponse: Response = {
    id: "resp-abc-789",
    assignmentId: assignmentInstance.id,
    submittedDate: new Date().toISOString(),
    answers: {
      "q1": [ // Assuming q1 was the 'table' question from the previous example
        { "c1": "Monday", "c2": "Team Meeting", "c3": 4 },
        { "c1": "Tuesday", "c2": "Project Work", "c3": 5 },
        // ... other rows
      ],
      "q2": [8, 9], // Assuming q2 was the multi-response 'likert' (Current: 8, Goal: 9)
      "q3": true     // Assuming q3 was the 'task' question
    }
  };
  
  // Update assignment status
  assignmentInstance.status = 'completed';
  assignmentInstance.completedDate = coacheeResponse.submittedDate;
  */
