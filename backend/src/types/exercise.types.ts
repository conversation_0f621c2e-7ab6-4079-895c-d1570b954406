/**
 * Represents the structure for the entire form.
 */
export interface Exercise {
  /** Unique identifier for the form. */
  id: string; // Corresponds to format: uuid in schema, but string in TS
  /** The title or name of the form. */
  name: string;
  /** A description of the form, potentially including rich text (e.g., HTML). */
  description: string;
  /** An ordered list of questions and text blocks included in the form. */
  questions: (Question | TextBlock)[];
  /** Created by user ID. */
  createdBy: string;
  /** Timestamp when the exercise was created. */
  createdAt: string;
  /** Updated by user ID. */
  updatedBy: string;
  /** Timestamp when the exercise was last updated. */
  updatedAt: string;
}

/**
 * Represents a rich text block that can be inserted between questions.
 */
export interface TextBlock {
  /** Unique identifier for the text block. */
  id: string;
  /** The type identifier for text blocks. */
  type: "text_block";
  /** Rich text content (HTML). */
  content: string;
  /** Optional title for the text block. */
  title?: string;
}

// --- Base and Union Type for Questions ---

/**
 * Base interface containing common properties for all question types.
 */
interface QuestionBase {
  /** Unique identifier for the question within the form. */
  id: string; // Corresponds to format: uuid in schema, but string in TS
  /** The text of the question being asked. */
  prompt: string;
  /** Optional additional instructions or context for the question. */
  description?: string;
  /** Whether an answer to this question is mandatory. Defaults to false if omitted. */
  required?: boolean;
  /** The type of question, determining its specific structure and properties. */
  type: QuestionType;
}

/**
 * Union type representing any possible question type.
 * Uses the 'type' property as the discriminant.
 */
export type Question =
  | FreeTextQuestion
  | LikertQuestion
  | TaskQuestion
  | TableQuestion
  | Graph2DQuestion
  | TextBlock;

/**
 * Enum-like type for the different kinds of questions and content blocks.
 */
export type QuestionType =
  | "free_text"
  | "likert"
  | "task"
  | "table"
  | "graph_2d"
  | "text_block";

// --- Specific Question Type Definitions ---

/**
 * Interface for a free text input question.
 */
export interface FreeTextQuestion extends QuestionBase {
  type: "free_text";
  /** Optional placeholder text for the input field. */
  placeholder?: string;
  /** Optional maximum length for the text response. */
  maxLength?: number;
}

/**
 * Type definition for the labels object in a Likert scale.
 * Keys are string representations of scale values (e.g., "0", "5", "10").
 */
export type LikertLabelMap = Record<string, string>; // or Record<number, string> if keys are always numeric strings

/**
 * Base interface for common Likert scale properties.
 */
interface LikertQuestionBase extends QuestionBase {
  type: "likert";
  /** The minimum value on the Likert scale. */
  scaleMin: number;
  /** The maximum value on the Likert scale. */
  scaleMax: number;
  /** Captions explaining specific points on the scale. */
  labels: LikertLabelMap;
  /** The mode of response for the Likert question, either single or multi. */
  responseMode: "single" | "multi";
  /** Optional labels for each response input field (e.g., ["Current", "Goal"]). Required when response_mode is 'multi'. */
  multiResponseLabels?: string[];
}

/**
 * Interface for a Likert scale question allowing a single response.
 */
export interface SingleResponseLikertQuestion extends LikertQuestionBase {
  responseMode: "single";
}

/**
 * Interface for a Likert scale question allowing multiple responses (e.g., current vs. goal).
 */
export interface MultiResponseLikertQuestion extends LikertQuestionBase {
  responseMode: "multi";
  /** Labels for each response input field (e.g., ["Current", "Goal"]). Required when response_mode is 'multi'. */
  multiResponseLabels: string[];
}

/**
 * Union type representing either a single or multi-response Likert question.
 */
export type LikertQuestion =
  | SingleResponseLikertQuestion
  | MultiResponseLikertQuestion;

/**
 * Interface for a simple task/checkbox question.
 */
export interface TaskQuestion extends QuestionBase {
  type: "task";
  /** Whether the task is completed or not */
  isCompleted: boolean;
}

// --- Table Question Related Definitions ---

/**
 * Enum-like type for table column input types.
 */
export type TableColumnType = "free_text" | "likert";

/**
 * Base interface for table column properties.
 */
interface TableColumnBase {
  /** Unique identifier for the column within the table question. */
  id: string;
  /** The text displayed in the column header. */
  header: string;
  /** The type of input allowed in this column's cells. */
  columnType: TableColumnType;
}

/**
 * Interface for a table column allowing free text input.
 */
export interface FreeTextTableColumn extends TableColumnBase {
  columnType: "free_text";
  /** Optional placeholder text for the cells in this column. */
  placeholder?: string;
}

/**
 * Interface for a table column allowing Likert scale input.
 */
export interface LikertTableColumn extends TableColumnBase {
  columnType: "likert";
  /** The minimum value on the Likert scale for this column. */
  scaleMin: number;
  /** The maximum value on the Likert scale for this column. */
  scaleMax: number;
  /** Captions explaining specific points on the scale for this column. */
  labels: LikertLabelMap;
}

/**
 * Union type representing any possible table column type.
 * Uses 'columnType' as the discriminant.
 */
export type TableColumn = FreeTextTableColumn | LikertTableColumn;

/**
 * Base interface for common Table question properties.
 */
interface TableQuestionBase extends QuestionBase {
  type: "table";
  /** Definitions for the columns in the table. */
  columns: TableColumn[];
}

/**
 * Interface for a Table question where the user can dynamically add rows.
 */
export interface DynamicRowTableQuestion extends TableQuestionBase {
  rowMode: "dynamic";
}

/**
 * Interface for a Table question with a fixed number of rows defined by the form creator.
 */
export interface FixedRowTableQuestion extends TableQuestionBase {
  rowMode: "fixed";
  /** The predefined number of rows for the table. Required when row_mode is 'fixed'. */
  fixedRows: number;
}

/**
 * Union type representing either a dynamic or fixed row table question.
 */
export type TableQuestion = DynamicRowTableQuestion | FixedRowTableQuestion;

// --- Graph 2D Question Definition ---

/**
 * Interface for a 2D line graph input question.
 */
export interface Graph2DQuestion extends QuestionBase {
  type: "graph_2d";
  /** Label for the X-axis. */
  xAxisLabel: string;
  /** Label for the Y-axis. */
  yAxisLabel: string;
  /** Optional minimum value for the X-axis. */
  xAxisMin?: number;
  /** Optional maximum value for the X-axis. */
  xAxisMax?: number;
  /** Optional minimum value for the Y-axis. */
  yAxisMin?: number;
  /** Optional maximum value for the Y-axis. */
  yAxisMax?: number;
}

// --- Example Usage (Conceptual) ---
/*
const myExercise: Exercise = {
  id: "f81d4fae-7dec-11d0-a765-00a0c91e6bf6",
  name: "Weekly Check-in",
  description: "<p>Please fill out your activities and mood for the week.</p>",
  questions: [
    {
      id: "q1",
      type: "table",
      prompt: "Log your week:",
      required: true,
      rowMode: "fixed",
      fixedRows: 7,
      columns: [
        { id: "c1", header: "Day", columnType: "free_text", placeholder: "e.g., Monday" },
        { id: "c2", header: "Main Activity", columnType: "free_text" },
        {
          id: "c3", header: "Mood", columnType: "likert",
          scaleMin: 1, scaleMax: 5,
          labels: { "1": "Awful", "3": "Okay", "5": "Great" }
        }
      ]
    },
    {
      id: "q2",
      type: "likert",
      prompt: "Rate your overall week satisfaction (Current vs Goal).",
      scaleMin: 0,
      scaleMax: 10,
      labels: { "0": "Very Dissatisfied", "5": "Neutral", "10": "Very Satisfied" },
      responseMode: "multi",
      multiResponseLabels: ["Current", "Goal"]
    },
    {
      id: "q3",
      type: "task",
      prompt: "Did you complete your main goal this week?"
    }
  ]
};
*/
