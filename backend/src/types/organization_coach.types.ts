/**
 * Represents the association between an organization and a coach.
 *
 * This interface is used to link coaches to their respective organizations.
 * It is a many-to-many relationship where a coach can be associated with multiple organizations,
 * and an organization can have multiple coaches.
 */
export interface OrganizationCoach {
  /** ID of the organization. */
  organizationId: string;
  /** ID of the coach. */
  coachId: string;
  /** Timestamp when the coach was added to the organization. */
  joinedAt: string;
}
