import { VisualizationAssignmentStatus } from "@prisma/client";

/**
 * Represents a visualization assignment. A visualization assignment is a
 * unique assignment of a visualization exercise from a coach to a coachee.
 */
export interface VisualizationAssignment {
  /** Unique identifier for the visualization assignment. */
  id: string;
  /** The visualization ID associated with the assignment. */
  visualizationId: string;
  /** The coachee ID associated with the assignment. */
  coacheeId: string;
  /** The coach ID associated with the assignment. */
  coachId: string;
  /** Timestamp when the assignment was created. */
  createdAt: Date;
  /** Timestamp when the assignment was last updated. */
  updatedAt: Date;
  /** The status of the assignment. */
  status: VisualizationAssignmentStatus;
  /** The due date of the assignment. */
  dueDate: Date | null;
}

export interface CreateVisualizationAssignmentDto {
  visualizationId: string;
  coacheeId: string;
  dueDate?: Date;
}

export interface UpdateVisualizationAssignmentStatusDto {
  status: VisualizationAssignmentStatus;
}

export interface VisualizationAssignmentResponse {
  id: string;
  visualizationId: string;
  coachId: string;
  coacheeId: string;
  status: VisualizationAssignmentStatus;
  dueDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
}
