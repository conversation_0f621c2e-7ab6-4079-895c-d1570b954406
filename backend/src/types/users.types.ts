import { UserRole } from "@prisma/client";

export interface User {
  /** Unique identifier for the user. */
  id: string;
  /** Email address of the user. */
  email: string;
  /** First name of the user. */
  firstName: string;
  /** Last name of the user. */
  lastName: string;
  /** Role of the user. */
  role: UserRole;
  /** Timestamp when the user was created. */
  createdAt: string;
  /** Timestamp when the user was last updated. */
  updatedAt: string;
}
