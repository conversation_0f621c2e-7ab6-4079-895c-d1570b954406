/**
 * Represents the association between an organization and a coachee.
 *
 * This interface is used to link coachees to their respective organizations.
 * It is a one-to-many relationship where a coachee can only be associated with one organization, and an organization can have multiple coachees.
 */

export interface OrganizationCoachee {
  /** ID of the organization. */
  organizationId: string;
  /** ID of the coachee. */
  coacheeId: string;
  /** Timestamp when the coachee was added to the organization. */
  createdAt: string;
  /** Timestamp when the coachee was last updated in the organization. */
  updatedAt: string;
}
