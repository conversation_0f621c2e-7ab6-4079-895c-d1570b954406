import nodemailer from "nodemailer";
import { env } from "../config/env";

export const transporter = nodemailer.createTransport({
  host: env.smtpHost,
  port: env.smtpPort,
  secure: env.smtpSecure,
  auth: {
    user: env.smtpUser,
    pass: env.smtpPassword,
  },
});

export const sendPasswordResetEmail = async (to: string, token: string) => {
  const resetLink = `${env.frontendUrl}/reset-password?token=${token}`;

  await transporter.sendMail({
    from: env.emailFrom,
    to,
    subject: "Password Reset Request",
    html: `
      <h1>Password Reset Request</h1>
      <p>You have requested to reset your password. Click the link below to reset it:</p>
      <p><a href="${resetLink}">Reset Password</a></p>
      <p>This link will expire in 1 hour.</p>
      <p>If you did not request this password reset, please ignore this email.</p>
    `,
  });
};
