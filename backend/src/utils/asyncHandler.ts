import { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * Wraps an async route handler function to catch any errors (sync or async)
 * and pass them to the Express error handling middleware.
 * @param fn The route handler function (RequestHandler) to wrap.
 * @returns An Express RequestHandler function.
 */
export const asyncHandler = (fn: RequestHandler): RequestHandler => {
    return (req: Request, res: Response, next: NextFunction) => {
        try {
            const result = fn(req, res, next);
            // Check if the result is a promise (has a .then method)
            if (result && typeof (result as any).then === 'function') {
                // It's a promise, so chain the catch to pass errors to next()
                (result as Promise<any>).catch(next);
            }
            // If result is not a promise (e.g., the function returned void),
            // we assume it handled the response or called next() itself.
        } catch (error) {
            // Catch synchronous errors thrown by fn
            next(error);
        }
    };
};
