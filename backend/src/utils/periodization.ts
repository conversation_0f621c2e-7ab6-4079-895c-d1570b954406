// This is a simplified version of the frontend periodization utility
// adapted for backend use with Prisma types

export interface Exercise {
  id: string;
  name: string; // Changed from title to name to match Prisma schema
  description?: string | null;
  questions?: any;
}

export interface PeriodizationEntry {
  week: number;
  assignmentDate: string;
  exerciseId: string;
  exerciseTitle: string;
  phase: string;
  skill?: string;
  type: "MT" | "MW";
}

interface ParsedExercise {
  id: string;
  name: string;
  type: "mt" | "mw";
  skill: string;
  difficulty: number;
  identifier: string;
}

// Parse exercise ID to extract type, skill, difficulty, and identifier
const parseExerciseId = (exercise: Exercise): ParsedExercise | null => {
  const parts = exercise.id.split("-");
  if (parts.length !== 3) return null;

  const [type, skill, difficultyAndId] = parts;
  if (!["mt", "mw"].includes(type)) return null;

  const difficulty = parseInt(difficultyAndId.charAt(0));
  const identifier = difficultyAndId.slice(1);

  if (isNaN(difficulty)) return null;

  return {
    id: exercise.id,
    name: exercise.name,
    type: type as "mt" | "mw",
    skill: skill.toUpperCase(),
    difficulty,
    identifier,
  };
};

// Create exercise buckets grouped by skill and sorted by difficulty
const createExerciseBuckets = (exercises: ParsedExercise[]) => {
  const buckets: { [skill: string]: ParsedExercise[] } = {};

  exercises.forEach((exercise) => {
    if (!buckets[exercise.skill]) {
      buckets[exercise.skill] = [];
    }
    buckets[exercise.skill].push(exercise);
  });

  // Sort each bucket by difficulty, then alphabetically by identifier
  Object.keys(buckets).forEach((skill) => {
    buckets[skill].sort((a, b) => {
      if (a.difficulty !== b.difficulty) {
        return a.difficulty - b.difficulty;
      }
      return a.identifier.localeCompare(b.identifier);
    });
  });

  return buckets;
};

// Create interleaved MW buckets
const createInterleavedMWBuckets = (exercises: ParsedExercise[]) => {
  const buckets: { [skill: string]: ParsedExercise[] } = {};

  exercises.forEach((exercise) => {
    if (!buckets[exercise.skill]) {
      buckets[exercise.skill] = [];
    }
    buckets[exercise.skill].push(exercise);
  });

  // Sort each bucket by difficulty, then alphabetically by identifier
  Object.keys(buckets).forEach((skill) => {
    buckets[skill].sort((a, b) => {
      if (a.difficulty !== b.difficulty) {
        return a.difficulty - b.difficulty;
      }
      return a.identifier.localeCompare(b.identifier);
    });
  });

  return buckets;
};

// Add weeks to a date
const addWeeks = (date: Date, weeks: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + weeks * 7);
  return result;
};

export const generatePeriodization = (
  offSeasonStartDate: Date,
  offSeasonEndDate: Date,
  prepStartDate: Date,
  prepEndDate: Date,
  preCompStartDate: Date,
  preCompEndDate: Date,
  _compStartDate: Date, // Not used in algorithm
  _compEndDate: Date, // Not used in algorithm
  allExercises: Exercise[],
  scheduleMentalToughness: boolean,
  scheduleMentalWellness: boolean,
  mentalWellnessStartDate?: Date,
  mentalWellnessEndDate?: Date
): { entries: PeriodizationEntry[]; errors: string[] } => {
  const entries: PeriodizationEntry[] = [];
  const errors: string[] = [];

  // Validate dates
  if (offSeasonStartDate >= prepStartDate) {
    errors.push("Off season start must be before prep start");
  }
  if (prepStartDate >= preCompStartDate) {
    errors.push("Prep start must be before pre-comp start");
  }

  if (errors.length > 0) {
    return { entries, errors };
  }

  // Parse exercises
  const parsedExercises = allExercises
    .map(parseExerciseId)
    .filter((e): e is ParsedExercise => e !== null);

  // Separate MT and MW exercises
  const mtExercises = parsedExercises.filter((e) => e.type === "mt");
  const mwExercises = parsedExercises.filter((e) => e.type === "mw");

  // Create buckets
  const mtBuckets = createExerciseBuckets(mtExercises);
  const mwBuckets = createInterleavedMWBuckets(mwExercises);

  // Track used indices for each bucket
  const mtUsedIndices: { [skill: string]: Set<number> } = {};
  Object.keys(mtBuckets).forEach((skill) => {
    mtUsedIndices[skill] = new Set();
  });

  const mwUsedIndices: { [skill: string]: Set<number> } = {};
  Object.keys(mwBuckets).forEach((skill) => {
    mwUsedIndices[skill] = new Set();
  });

  // Helper function to get next exercise from bucket
  const getNextExercise = (
    buckets: { [skill: string]: ParsedExercise[] },
    usedIndices: { [skill: string]: Set<number> },
    skill: string
  ): ParsedExercise | null => {
    const bucket = buckets[skill];
    if (!bucket || bucket.length === 0) return null;

    const usedSet = usedIndices[skill];
    for (let i = 0; i < bucket.length; i++) {
      if (!usedSet.has(i)) {
        usedSet.add(i);
        return bucket[i];
      }
    }
    return null;
  };

  // Helper function to schedule exercises for a period
  const scheduleForPeriod = (
    startDate: Date,
    endDate: Date,
    mtPattern: string[],
    mwPattern: string[],
    startWeek: number
  ): number => {
    let currentDate = new Date(startDate);
    let weekCounter = startWeek;
    let mtPatternIndex = 0;
    let mwPatternIndex = 0;

    while (currentDate <= endDate) {
      const phase = currentDate <= offSeasonEndDate ? "Off Season" :
                   currentDate <= prepEndDate ? "Prep" : "Pre-Comp";

      // Schedule MT exercise if enabled
      if (scheduleMentalToughness && mtPattern.length > 0) {
        const skill = mtPattern[mtPatternIndex % mtPattern.length];
        const exercise = getNextExercise(mtBuckets, mtUsedIndices, skill);
        
        if (exercise) {
          entries.push({
            week: weekCounter,
            assignmentDate: currentDate.toISOString().split('T')[0],
            exerciseId: exercise.id,
            exerciseTitle: exercise.name,
            phase,
            skill: exercise.skill,
            type: "MT",
          });
        }
        mtPatternIndex++;
      }

      // Schedule MW exercise if enabled and within range
      if (scheduleMentalWellness && 
          (!mentalWellnessStartDate || currentDate >= mentalWellnessStartDate) &&
          (!mentalWellnessEndDate || currentDate <= mentalWellnessEndDate) &&
          mwPattern.length > 0) {
        const skill = mwPattern[mwPatternIndex % mwPattern.length];
        const exercise = getNextExercise(mwBuckets, mwUsedIndices, skill);
        
        if (exercise) {
          entries.push({
            week: weekCounter,
            assignmentDate: currentDate.toISOString().split('T')[0],
            exerciseId: exercise.id,
            exerciseTitle: exercise.name,
            phase,
            skill: exercise.skill,
            type: "MW",
          });
        }
        mwPatternIndex++;
      }

      currentDate = addWeeks(currentDate, 1);
      weekCounter++;
    }

    return weekCounter;
  };

  // MW Pattern: Mi, P, E, R, M, A, P, L, U, S (simplified pattern)
  const mwPattern = ["M", "P", "E", "R", "M", "A", "P", "L", "U", "S"];

  let currentWeek = 1;

  // Off Season: MT (B1, B2, B3, C5), MW (full pattern)
  const offSeasonMTPattern = ["B1", "B2", "B3", "C5"];
  currentWeek = scheduleForPeriod(
    offSeasonStartDate,
    offSeasonEndDate,
    offSeasonMTPattern,
    mwPattern,
    currentWeek
  );

  // Prep: MT (C1, C2, C3, C5), MW (full pattern)
  const prepMTPattern = ["C1", "C2", "C3", "C5"];
  currentWeek = scheduleForPeriod(
    prepStartDate,
    prepEndDate,
    prepMTPattern,
    mwPattern,
    currentWeek
  );

  // Pre-Comp: MT (C4, C5), MW (full pattern)
  const preCompMTPattern = ["C4", "C5"];
  scheduleForPeriod(
    preCompStartDate,
    preCompEndDate,
    preCompMTPattern,
    mwPattern,
    currentWeek
  );

  // Sort entries by assignment date
  entries.sort(
    (a, b) =>
      new Date(a.assignmentDate).getTime() -
      new Date(b.assignmentDate).getTime()
  );

  return { entries, errors };
};
