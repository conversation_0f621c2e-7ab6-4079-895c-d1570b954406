{"name": "backend", "version": "1.0.0", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon -r dotenv/config src/server.ts", "test": "echo \"Error: no test specified\" && exit 1", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "ts-node prisma/seed-safe.ts", "db:seed:clean": "ts-node prisma/seed.ts", "db:seed:force": "ts-node prisma/seed.ts", "db:deploy": "./scripts/deploy-rds.sh", "db:test": "ts-node scripts/test-rds-connection.ts"}, "prisma": {"seed": "ts-node prisma/seed-safe.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/s3-request-presigner": "^3.832.0", "@prisma/client": "^6.5.0", "@types/bcryptjs": "^2.4.6", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "nodemailer": "^6.10.1", "replicate": "^1.0.1", "sharp": "^0.34.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.14.0", "nodemon": "^3.1.9", "prisma": "^6.5.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}