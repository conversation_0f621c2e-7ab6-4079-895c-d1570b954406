const { TTSService } = require("../dist/services/tts.service");
require('dotenv').config();

async function testAudioGeneration() {
  console.log("Testing Audio Generation...\n");
  
  // Test 1: Check environment variables
  console.log("1. Environment Variables:");
  console.log(`   REPLICATE_API_KEY: ${process.env.REPLICATE_API_KEY ? 'SET' : 'NOT SET'}`);
  console.log(`   REPLICATE_KOKORO_MODEL: ${process.env.REPLICATE_KOKORO_MODEL || 'NOT SET'}\n`);

  if (!process.env.REPLICATE_API_KEY || !process.env.REPLICATE_KOKORO_MODEL) {
    console.log("❌ Required environment variables are not set.");
    return;
  }

  // Test 2: Generate audio
  try {
    console.log("2. Testing audio generation...");
    
    const testText = "Hello, this is a test message for audio generation.";
    const options = {
      voice: "af_bella",
      speed: 1.0,
      language: "en",
    };

    console.log(`   Text: "${testText}"`);
    console.log(`   Options:`, options);
    console.log("   Generating audio...\n");

    const audioBuffer = await TTSService.generateAudio(testText, options);

    console.log("3. Results:");
    console.log(`   ✅ Audio generated successfully`);
    console.log(`   📊 Buffer size: ${audioBuffer.length} bytes`);
    console.log(`   📊 Buffer type: ${audioBuffer.constructor.name}`);
    
    if (audioBuffer.length > 0) {
      console.log("   🎉 Audio generation test passed!");
    } else {
      console.log("   ❌ Audio buffer is empty");
    }

  } catch (error) {
    console.log("   ❌ Audio generation failed");
    console.log(`   Error: ${error.message}`);
    
    if (error.stack) {
      console.log(`   Stack trace: ${error.stack}`);
    }
  }

  console.log("\n" + "=".repeat(50));
  console.log("Test completed!");
}

// Only run if this file is executed directly
if (require.main === module) {
  testAudioGeneration().catch(console.error);
}

module.exports = { testAudioGeneration };
