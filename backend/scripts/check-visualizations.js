#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to check visualizations in the database for missing titles
 * Run with: node scripts/check-visualizations.js
 */

const { PrismaClient } = require("@prisma/client");
require("dotenv").config();

const prisma = new PrismaClient();

async function checkVisualizations() {
  console.log("🔍 Checking visualizations in the database...\n");

  try {
    // Get all visualizations
    const visualizations = await prisma.visualization.findMany({
      select: {
        id: true,
        title: true,
        description: true,
        createdAt: true,
        creator: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    console.log(`📊 Found ${visualizations.length} visualizations total\n`);

    if (visualizations.length === 0) {
      console.log("ℹ️  No visualizations found in the database.");
      console.log("   This might be why the assignment creation is failing.");
      console.log("   Try creating a visualization first.\n");
      return;
    }

    // Check for visualizations with missing or empty titles
    const invalidVisualizations = visualizations.filter(
      (v) => !v.title || v.title.trim() === ""
    );

    if (invalidVisualizations.length > 0) {
      console.log(`❌ Found ${invalidVisualizations.length} visualizations with missing titles:`);
      invalidVisualizations.forEach((v, index) => {
        console.log(`   ${index + 1}. ID: ${v.id}`);
        console.log(`      Title: "${v.title || "NULL"}"`);
        console.log(`      Created: ${v.createdAt.toISOString()}`);
        console.log(`      Creator: ${v.creator?.firstName || "Unknown"} ${v.creator?.lastName || ""}`);
        console.log("");
      });
      
      console.log("💡 These visualizations need to be fixed or deleted.");
      console.log("   You can update them with proper titles or delete them.\n");
    } else {
      console.log("✅ All visualizations have valid titles.\n");
    }

    // Show all visualizations for reference
    console.log("📋 All visualizations:");
    visualizations.forEach((v, index) => {
      const titleStatus = v.title && v.title.trim() !== "" ? "✅" : "❌";
      console.log(`   ${index + 1}. ${titleStatus} "${v.title || "NO TITLE"}" (ID: ${v.id.slice(-8)})`);
      console.log(`      Created: ${v.createdAt.toLocaleDateString()}`);
      console.log(`      Creator: ${v.creator?.firstName || "Unknown"} ${v.creator?.lastName || ""}`);
      if (v.description) {
        console.log(`      Description: ${v.description.substring(0, 100)}${v.description.length > 100 ? "..." : ""}`);
      }
      console.log("");
    });

    // Check for any visualization assignments that might be referencing invalid visualizations
    console.log("🔗 Checking visualization assignments...");
    const assignments = await prisma.visualizationAssignment.findMany({
      select: {
        id: true,
        visualizationId: true,
        visualization: {
          select: {
            id: true,
            title: true,
          },
        },
        coachee: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10, // Show last 10 assignments
    });

    console.log(`📊 Found ${assignments.length} recent visualization assignments\n`);

    if (assignments.length > 0) {
      console.log("📋 Recent assignments:");
      assignments.forEach((a, index) => {
        const titleStatus = a.visualization?.title && a.visualization.title.trim() !== "" ? "✅" : "❌";
        console.log(`   ${index + 1}. ${titleStatus} Assignment ID: ${a.id.slice(-8)}`);
        console.log(`      Visualization: "${a.visualization?.title || "MISSING TITLE"}" (${a.visualizationId.slice(-8)})`);
        console.log(`      Coachee: ${a.coachee?.firstName || "Unknown"} ${a.coachee?.lastName || ""}`);
        console.log(`      Created: ${a.createdAt.toLocaleDateString()}`);
        console.log("");
      });
    }

    console.log("✅ Visualization check completed!");
    
    if (invalidVisualizations.length > 0) {
      console.log("\n🔧 To fix invalid visualizations, you can:");
      console.log("   1. Update them with proper titles using the admin interface");
      console.log("   2. Delete them if they're not needed");
      console.log("   3. Run a database update query to fix them");
    }

  } catch (error) {
    console.error("❌ Error checking visualizations:", error);
    console.error("   Make sure your database is running and accessible.");
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkVisualizations().catch(console.error);
