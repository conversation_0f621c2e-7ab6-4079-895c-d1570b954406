import { PrismaClient } from '@prisma/client';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const prisma = new PrismaClient();

async function testDeleteFunctionality() {
  console.log('🧪 Testing delete functionality...');

  try {
    // Get current counts
    const exerciseCount = await prisma.exercise.count();
    const visualizationCount = await prisma.visualization.count();
    const assignmentCount = await prisma.assignment.count();
    const visAssignmentCount = await prisma.visualizationAssignment.count();

    console.log('\n📊 Current database state:');
    console.log(`  Exercises: ${exerciseCount}`);
    console.log(`  Visualizations: ${visualizationCount}`);
    console.log(`  Assignments: ${assignmentCount}`);
    console.log(`  Visualization Assignments: ${visAssignmentCount}`);

    // Find the sample exercise and visualization
    const sampleExercise = await prisma.exercise.findFirst({
      where: { name: 'Goal Setting Exercise' },
      include: {
        assignments: {
          select: { id: true }
        }
      }
    });

    const sampleVisualization = await prisma.visualization.findFirst({
      where: { title: 'Pre-Competition Visualization' },
      include: {
        assignments: {
          select: { id: true }
        }
      }
    });

    if (sampleExercise) {
      console.log(`\n🎯 Found sample exercise: "${sampleExercise.name}"`);
      console.log(`  Associated assignments: ${sampleExercise.assignments.length}`);
    }

    if (sampleVisualization) {
      console.log(`\n🎨 Found sample visualization: "${sampleVisualization.title}"`);
      console.log(`  Associated assignments: ${sampleVisualization.assignments.length}`);
    }

    // Test cascade behavior by checking foreign key constraints
    console.log('\n🔗 Testing cascade relationships...');
    
    // Check if assignments reference exercises
    const assignmentsWithExercises = await prisma.assignment.findMany({
      select: {
        id: true,
        exerciseId: true,
        exercise: {
          select: { name: true }
        }
      }
    });

    console.log(`  Assignments with exercise references: ${assignmentsWithExercises.length}`);
    
    // Check if visualization assignments reference visualizations
    const visAssignmentsWithVis = await prisma.visualizationAssignment.findMany({
      select: {
        id: true,
        visualizationId: true,
        visualization: {
          select: { title: true }
        }
      }
    });

    console.log(`  Visualization assignments with visualization references: ${visAssignmentsWithVis.length}`);

    console.log('\n✅ Delete functionality test completed!');
    console.log('\n💡 To test actual deletion:');
    console.log('1. Use the admin dashboard to delete an exercise or visualization');
    console.log('2. Verify that associated assignments are also deleted');
    console.log('3. Check that the counts decrease appropriately');

  } catch (error) {
    console.error('❌ Error testing delete functionality:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDeleteFunctionality();
