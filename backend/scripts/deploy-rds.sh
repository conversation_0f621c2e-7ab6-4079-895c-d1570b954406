#!/bin/bash

# Deploy to AWS RDS Script
# This script helps deploy your database schema and data to AWS RDS

set -e  # Exit on any error

echo "🚀 Starting RDS deployment process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_error ".env.production file not found!"
    print_warning "Please create .env.production with your RDS configuration"
    exit 1
fi

# Load production environment variables
export $(cat .env.production | grep -v '^#' | xargs)

print_status "Loaded production environment variables"

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL not set in .env.production"
    exit 1
fi

print_status "DATABASE_URL configured: ${DATABASE_URL%%:*}://***"

# Test database connection
print_status "Testing database connection..."
if npx prisma db execute --url="$DATABASE_URL" --stdin <<< "SELECT 1;"; then
    print_status "✅ Database connection successful!"
else
    print_error "❌ Failed to connect to database"
    print_warning "Please check your RDS instance is running and accessible"
    exit 1
fi

# Generate Prisma client
print_status "Generating Prisma client..."
npx prisma generate

# Push database schema (creates tables if they don't exist)
print_status "Deploying database schema..."
npx prisma db push --accept-data-loss

# Optional: Run seeds if they exist
if [ -f "prisma/seed.ts" ] || [ -f "prisma/seed.js" ]; then
    print_status "Running database seeds..."
    npx prisma db seed
else
    print_warning "No seed file found, skipping seeding"
fi

print_status "🎉 RDS deployment completed successfully!"
print_warning "Remember to:"
print_warning "1. Update your production application to use the new DATABASE_URL"
print_warning "2. Configure security groups to allow access from your application servers"
print_warning "3. Set up monitoring and backups for your RDS instance"

echo ""
print_status "Your database is now ready at: ${DATABASE_URL%%@*}@***"
