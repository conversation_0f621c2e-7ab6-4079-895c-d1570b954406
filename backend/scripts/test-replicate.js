const Replicate = require("replicate");
require('dotenv').config();

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_KEY,
});

async function testReplicate() {
  console.log("Testing Replicate API Connection...\n");
  
  // Test 1: Check environment variables
  console.log("1. Environment Variables:");
  console.log(`   REPLICATE_API_KEY: ${process.env.REPLICATE_API_KEY ? 'SET' : 'NOT SET'}`);
  console.log(`   REPLICATE_KOKORO_MODEL: ${process.env.REPLICATE_KOKORO_MODEL || 'NOT SET'}\n`);

  if (!process.env.REPLICATE_API_KEY) {
    console.log("❌ REPLICATE_API_KEY is not set. Please check your .env file.");
    return;
  }

  if (!process.env.REPLICATE_KOKORO_MODEL) {
    console.log("❌ REPLICATE_KOKORO_MODEL is not set. Please check your .env file.");
    return;
  }

  // Test 2: Test API connectivity
  try {
    console.log("2. Testing Replicate API connectivity...");
    
    const input = {
      text: "Hello, this is a test message.",
      voice: "af_bella",
      speed: 1.0,
      language: "en",
    };

    console.log("   Input:", input);
    console.log("   Model:", process.env.REPLICATE_KOKORO_MODEL);
    console.log("   Making API call...\n");

    const output = await replicate.run(
      process.env.REPLICATE_KOKORO_MODEL,
      { input }
    );

    console.log("3. API Response:");
    console.log(`   Output type: ${typeof output}`);
    console.log(`   Output value:`, output);
    
    if (typeof output === "string") {
      console.log("   ✅ Received string URL - this is expected");
      
      // Test downloading the audio
      console.log("\n4. Testing audio download...");
      const response = await fetch(output);
      console.log(`   Response status: ${response.status}`);
      console.log(`   Content-Type: ${response.headers.get('content-type')}`);
      console.log(`   Content-Length: ${response.headers.get('content-length')}`);
      
      if (response.ok) {
        console.log("   ✅ Audio download successful");
      } else {
        console.log("   ❌ Audio download failed");
      }
      
    } else if (Array.isArray(output)) {
      console.log("   ✅ Received array - using first element");
      console.log(`   First element: ${output[0]}`);
    } else {
      console.log("   ❌ Unexpected output format");
    }

  } catch (error) {
    console.log("   ❌ Failed to call Replicate API");
    console.log(`   Error: ${error.message}`);
    
    if (error.message.includes('401')) {
      console.log("\n   💡 This looks like an authentication error.");
      console.log("   - Check that your REPLICATE_API_KEY is correct");
      console.log("   - Make sure you have credits in your Replicate account");
    } else if (error.message.includes('404')) {
      console.log("\n   💡 This looks like a model not found error.");
      console.log("   - Check that the model name is correct");
      console.log("   - Try using a different model version");
    }
  }

  console.log("\n" + "=".repeat(50));
  console.log("Test completed!");
}

testReplicate().catch(console.error);
