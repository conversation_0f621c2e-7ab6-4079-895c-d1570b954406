import { PrismaClient } from '@prisma/client';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.production' });

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

async function testConnection() {
  console.log('🔍 Testing RDS connection...');
  console.log('Database URL:', process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':***@'));

  try {
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Successfully connected to RDS!');

    // Test query execution
    const result = await prisma.$queryRaw`SELECT version() as version, current_database() as database, current_user as user`;
    console.log('📊 Database info:', result);

    // Check if tables exist
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log('📋 Tables in database:');
    if (Array.isArray(tables) && tables.length > 0) {
      tables.forEach((table: any) => {
        console.log(`  - ${table.table_name}`);
      });
    } else {
      console.log('  No tables found. Run migrations to create schema.');
    }

    // Test user count (if users table exists)
    try {
      const userCount = await prisma.user.count();
      console.log(`👥 Total users in database: ${userCount}`);
    } catch (error) {
      console.log('ℹ️  Users table not found or empty');
    }

    console.log('🎉 RDS connection test completed successfully!');

  } catch (error) {
    console.error('❌ Failed to connect to RDS:');
    console.error(error);
    
    if (error instanceof Error) {
      if (error.message.includes('ENOTFOUND')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('1. Check if RDS instance is running');
        console.log('2. Verify the endpoint URL is correct');
        console.log('3. Ensure your IP is whitelisted in security groups');
      } else if (error.message.includes('authentication')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('1. Verify username and password are correct');
        console.log('2. Check if the database name exists');
      } else if (error.message.includes('SSL')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('1. Add sslmode=require to your DATABASE_URL');
        console.log('2. Ensure SSL is properly configured');
      }
    }
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testConnection();
