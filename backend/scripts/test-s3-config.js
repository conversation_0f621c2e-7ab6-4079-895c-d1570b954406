#!/usr/bin/env node

/**
 * Test script to verify S3 configuration for audio files
 * Run with: node scripts/test-s3-config.js
 */

const { S3Client, ListObjectsV2Command, GetObjectCommand } = require("@aws-sdk/client-s3");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
require("dotenv").config();

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

const bucketName = process.env.AWS_S3_BUCKET;

async function testS3Configuration() {
  console.log("🔍 Testing S3 Configuration...\n");

  // Check environment variables
  console.log("📋 Environment Variables:");
  console.log(`   AWS_REGION: ${process.env.AWS_REGION || "❌ Not set"}`);
  console.log(`   AWS_S3_BUCKET: ${bucketName || "❌ Not set"}`);
  console.log(`   AWS_ACCESS_KEY_ID: ${process.env.AWS_ACCESS_KEY_ID ? "✅ Set" : "❌ Not set"}`);
  console.log(`   AWS_SECRET_ACCESS_KEY: ${process.env.AWS_SECRET_ACCESS_KEY ? "✅ Set" : "❌ Not set"}\n`);

  if (!bucketName || !process.env.AWS_REGION || !process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
    console.log("❌ Missing required environment variables. Please check your .env file.");
    process.exit(1);
  }

  try {
    // Test bucket access
    console.log("🪣 Testing bucket access...");
    const listCommand = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: "audio/",
      MaxKeys: 5,
    });

    const listResult = await s3Client.send(listCommand);
    console.log(`✅ Successfully connected to bucket: ${bucketName}`);
    console.log(`📁 Found ${listResult.Contents?.length || 0} audio files\n`);

    if (listResult.Contents && listResult.Contents.length > 0) {
      console.log("🎵 Audio files found:");
      listResult.Contents.forEach((obj, index) => {
        console.log(`   ${index + 1}. ${obj.Key} (${obj.Size} bytes)`);
      });
      console.log();

      // Test signed URL generation for the first audio file
      const firstAudioFile = listResult.Contents[0];
      const filename = firstAudioFile.Key?.replace("audio/", "");
      
      if (filename) {
        console.log("🔗 Testing signed URL generation...");
        const getCommand = new GetObjectCommand({
          Bucket: bucketName,
          Key: firstAudioFile.Key,
        });

        const signedUrl = await getSignedUrl(s3Client, getCommand, { expiresIn: 3600 });
        console.log(`✅ Successfully generated signed URL for: ${filename}`);
        console.log(`🔗 URL: ${signedUrl.substring(0, 100)}...\n`);

        // Test if the URL is accessible
        console.log("🌐 Testing URL accessibility...");
        try {
          const response = await fetch(signedUrl, { method: "HEAD" });
          if (response.ok) {
            console.log(`✅ URL is accessible (Status: ${response.status})`);
            console.log(`📊 Content-Type: ${response.headers.get("content-type")}`);
            console.log(`📏 Content-Length: ${response.headers.get("content-length")} bytes`);
          } else {
            console.log(`❌ URL returned error (Status: ${response.status})`);
          }
        } catch (fetchError) {
          console.log(`❌ Failed to fetch URL: ${fetchError.message}`);
        }
      }
    } else {
      console.log("ℹ️  No audio files found in the bucket. This is normal if no audio has been generated yet.");
    }

    console.log("\n✅ S3 configuration test completed successfully!");
    console.log("\n📝 Next steps:");
    console.log("   1. Configure CORS in your S3 bucket (see S3_BUCKET_CONFIGURATION.md)");
    console.log("   2. Test audio playback in the application");
    console.log("   3. Check browser console for any CORS errors");

  } catch (error) {
    console.error("❌ S3 configuration test failed:");
    console.error(`   Error: ${error.message}`);
    
    if (error.name === "NoSuchBucket") {
      console.error("   💡 The bucket doesn't exist or you don't have access to it.");
    } else if (error.name === "AccessDenied") {
      console.error("   💡 Check your AWS credentials and IAM permissions.");
    } else if (error.name === "InvalidAccessKeyId") {
      console.error("   💡 Your AWS Access Key ID is invalid.");
    } else if (error.name === "SignatureDoesNotMatch") {
      console.error("   💡 Your AWS Secret Access Key is invalid.");
    }
    
    process.exit(1);
  }
}

// Run the test
testS3Configuration().catch(console.error);
