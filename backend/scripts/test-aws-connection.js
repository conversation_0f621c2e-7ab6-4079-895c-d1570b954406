const { S3Client, ListBucketsCommand, PutObjectCommand, HeadObjectCommand } = require("@aws-sdk/client-s3");
require('dotenv').config();

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

async function testAWSConnection() {
  console.log("Testing AWS S3 Connection...\n");
  
  // Test 1: Check environment variables
  console.log("1. Environment Variables:");
  console.log(`   AWS_REGION: ${process.env.AWS_REGION || 'NOT SET'}`);
  console.log(`   AWS_ACCESS_KEY_ID: ${process.env.AWS_ACCESS_KEY_ID ? 'SET' : 'NOT SET'}`);
  console.log(`   AWS_SECRET_ACCESS_KEY: ${process.env.AWS_SECRET_ACCESS_KEY ? 'SET' : 'NOT SET'}`);
  console.log(`   AWS_S3_BUCKET: ${process.env.AWS_S3_BUCKET || 'NOT SET'}\n`);

  // Test 2: List buckets (basic connectivity)
  try {
    console.log("2. Testing basic AWS connectivity...");
    const listCommand = new ListBucketsCommand({});
    const response = await s3Client.send(listCommand);
    console.log("   ✅ Successfully connected to AWS");
    console.log(`   📦 Found ${response.Buckets.length} buckets in your account\n`);
    
    // Check if our target bucket exists
    const targetBucket = process.env.AWS_S3_BUCKET;
    const bucketExists = response.Buckets.some(bucket => bucket.Name === targetBucket);
    
    if (bucketExists) {
      console.log(`   ✅ Target bucket '${targetBucket}' exists`);
    } else {
      console.log(`   ❌ Target bucket '${targetBucket}' NOT FOUND`);
      console.log("   Available buckets:");
      response.Buckets.forEach(bucket => {
        console.log(`     - ${bucket.Name}`);
      });
    }
  } catch (error) {
    console.log("   ❌ Failed to connect to AWS");
    console.log(`   Error: ${error.message}\n`);
    return;
  }

  // Test 3: Test bucket permissions
  try {
    console.log("\n3. Testing bucket permissions...");
    const testKey = "test/connection-test.txt";
    const testContent = "This is a test file created at " + new Date().toISOString();
    
    // Try to upload a test file
    const putCommand = new PutObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET,
      Key: testKey,
      Body: testContent,
      ContentType: "text/plain",
    });
    
    await s3Client.send(putCommand);
    console.log("   ✅ Successfully uploaded test file");
    
    // Try to check if file exists
    const headCommand = new HeadObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET,
      Key: testKey,
    });
    
    await s3Client.send(headCommand);
    console.log("   ✅ Successfully verified test file exists");
    console.log("   🎉 All permissions working correctly!");
    
  } catch (error) {
    console.log("   ❌ Failed to test bucket permissions");
    console.log(`   Error: ${error.message}`);
    
    if (error.name === 'AccessDenied') {
      console.log("\n   💡 Suggestions:");
      console.log("   - Check your IAM user has s3:PutObject and s3:HeadObject permissions");
      console.log("   - Verify the bucket policy allows your user to access it");
      console.log("   - Make sure the bucket exists in the specified region");
    }
  }

  console.log("\n" + "=".repeat(50));
  console.log("Test completed!");
}

testAWSConnection().catch(console.error);
