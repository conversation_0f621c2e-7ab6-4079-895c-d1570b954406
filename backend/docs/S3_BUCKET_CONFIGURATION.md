# S3 Bucket Configuration for Audio Files

This document explains how to configure your AWS S3 bucket to properly serve audio files for the MyZone Mindset application.

## Current Issue

You're experiencing 403 errors when trying to play audio files. This is likely due to one or more of the following:

1. **Expired Signed URLs** - Signed URLs have a 1-hour expiry
2. **Missing CORS Configuration** - <PERSON><PERSON><PERSON> needs proper CORS headers
3. **Bucket Access Permissions** - Bucket may not allow public read access for fallback URLs

## Solution Implemented

The application now includes:

1. **Automatic URL Refresh** - When a 403 error is detected, the AudioPlayer automatically requests a fresh signed URL
2. **New API Endpoint** - `GET /api/audio/visualizations/:id/audio-url` generates fresh signed URLs
3. **Enhanced Error Handling** - Better error detection and recovery

## S3 Bucket Configuration Steps

### 1. Configure CORS

Add this CORS configuration to your S3 bucket:

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "HEAD"],
    "AllowedOrigins": [
      "http://localhost:5173",
      "https://myzone-mindset.netlify.app"
    ],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3000
  }
]
```

**To configure CORS:**
1. Go to AWS S3 Console
2. Select your bucket: `myzone-mindset-visualization-audio`
3. Go to "Permissions" tab
4. Scroll down to "Cross-origin resource sharing (CORS)"
5. Click "Edit" and paste the JSON above
6. Save changes

### 2. Configure Bucket Policy (Optional)

If you want to allow public read access as a fallback (not recommended for security):

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::myzone-mindset-visualization-audio/audio/*"
    }
  ]
}
```

**To configure bucket policy:**
1. Go to AWS S3 Console
2. Select your bucket: `myzone-mindset-visualization-audio`
3. Go to "Permissions" tab
4. Scroll down to "Bucket policy"
5. Click "Edit" and paste the JSON above
6. Save changes

### 3. Block Public Access Settings

If you added the bucket policy above, you need to adjust block public access settings:

1. Go to "Permissions" tab
2. Click "Edit" on "Block public access (bucket settings)"
3. Uncheck "Block public access to buckets and objects granted through new public bucket or access point policies"
4. Uncheck "Block public and cross-account access to buckets and objects through any public bucket or access point policies"
5. Save changes

**Note:** This reduces security. The signed URL approach is preferred.

## Recommended Approach

**Use Signed URLs Only (Recommended):**
1. Configure CORS (step 1 above)
2. Keep block public access enabled
3. Rely on the automatic URL refresh feature

This approach is more secure as it doesn't expose your audio files publicly.

## Testing the Fix

1. **Start the backend server:**
   ```bash
   cd backend
   npm run dev
   ```

2. **Start the frontend:**
   ```bash
   cd frontend
   npm run dev
   ```

3. **Test audio playback:**
   - Navigate to a visualization with audio
   - Try playing the audio
   - If you get a 403 error, the AudioPlayer should automatically refresh the URL and retry

## Monitoring

Check the browser console for these messages:
- `"Detected potential 403 error, attempting to refresh audio URL..."`
- `"Audio URL refreshed successfully"`
- `"Failed to refresh audio URL:"`

## Troubleshooting

### Still Getting 403 Errors

1. **Check AWS Credentials:**
   - Verify `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` in `.env`
   - Ensure the IAM user has `s3:GetObject` permissions

2. **Check Bucket Name:**
   - Verify `AWS_S3_BUCKET=myzone-mindset-visualization-audio` in `.env`

3. **Check Region:**
   - Verify `AWS_REGION=us-east-1` in `.env`

4. **Test Signed URL Generation:**
   ```bash
   # In backend directory
   curl -X GET "http://localhost:3001/api/audio/visualizations/YOUR_VISUALIZATION_ID/audio-url" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

### CORS Errors

1. **Check CORS Configuration** - Ensure the CORS policy includes your frontend URL
2. **Check Request Headers** - Audio requests should not include authentication headers

### Network Issues

1. **Check Network Tab** - Look for failed requests in browser dev tools
2. **Check Response Headers** - Verify CORS headers are present
3. **Test Direct URL Access** - Try accessing the signed URL directly in a new tab

## Security Notes

- Signed URLs expire after 1 hour for security
- The automatic refresh feature ensures seamless user experience
- Avoid public bucket policies if possible
- Monitor access logs for unusual activity

## Next Steps

If issues persist:
1. Check CloudWatch logs for S3 access patterns
2. Consider using CloudFront for better performance and caching
3. Implement audio file lifecycle policies to manage storage costs
