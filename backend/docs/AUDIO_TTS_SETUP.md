# Audio TTS Setup Guide

This guide explains how to set up and use the Kokoro TTS (Text-to-Speech) integration with AWS S3 storage for the MyZone Mindset application.

## Overview

The audio TTS system provides:
- Text-to-speech conversion using Kokoro TTS on Replicate
- Audio file storage in AWS S3
- Intelligent caching to avoid regenerating identical audio
- Multiple voice options and language support
- RESTful API endpoints for audio generation and management

## Prerequisites

### 1. Replicate Account
- Sign up at [Replicate](https://replicate.com)
- Get your API key from the account settings
- The system uses the Kokoro TTS model: `lucataco/kokoro-82m`

### 2. AWS Account
- Create an AWS account
- Set up an S3 bucket for audio storage
- Create IAM credentials with S3 access

### 3. Environment Variables

Add the following to your `.env` file:

```env
# Replicate API for Kokoro TTS
REPLICATE_API_KEY=your_replicate_api_key_here
REPLICATE_KOKORO_MODEL=lucataco/kokoro-82m:a5b8b0b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8

# AWS Configuration for S3 Storage
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET=your_s3_bucket_name
```

## AWS S3 Setup

### 1. Create S3 Bucket
```bash
aws s3 mb s3://your-bucket-name --region us-east-1
```

### 2. Configure Bucket Policy (Optional - for public access)
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-bucket-name/audio/*"
    }
  ]
}
```

### 3. Configure CORS (if needed for frontend access)
```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "HEAD"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": []
  }
]
```

## API Endpoints

### 1. Generate Audio for Visualization
```http
POST /api/audio/visualizations/:id/audio
Authorization: Bearer <token>
Content-Type: application/json

{
  "voice": "af_bella",
  "speed": 1.0,
  "language": "en"
}
```

**Response:**
```json
{
  "message": "Audio generated successfully",
  "audioUrl": "https://bucket.s3.region.amazonaws.com/audio/hash.wav",
  "cached": false,
  "visualization": { ... },
  "ttsOptions": {
    "voice": "af_bella",
    "speed": 1.0,
    "language": "en"
  }
}
```

### 2. Generate Custom Audio
```http
POST /api/audio/generate
Authorization: Bearer <token>
Content-Type: application/json

{
  "text": "Hello, this is a test message.",
  "voice": "af_bella",
  "speed": 1.0,
  "language": "en"
}
```

### 3. Delete Audio
```http
DELETE /api/audio/visualizations/:id/audio
Authorization: Bearer <token>
```

### 4. Get Available Voices (Public)
```http
GET /api/audio/voices
```

**Response:**
```json
{
  "voices": [
    "af_bella",
    "af_nicole",
    "af_sarah",
    "am_adam",
    "am_michael",
    "bf_emma",
    "bf_isabella",
    "bm_george",
    "bm_lewis"
  ],
  "defaultVoice": "af_bella",
  "supportedLanguages": ["en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh", "ja", "hu", "ko"],
  "speedRange": { "min": 0.5, "max": 2.0, "default": 1.0 }
}
```

## Voice Options

### Available Voices
- **af_bella**: Female, American English
- **af_nicole**: Female, American English
- **af_sarah**: Female, American English
- **am_adam**: Male, American English
- **am_michael**: Male, American English
- **bf_emma**: Female, British English
- **bf_isabella**: Female, British English
- **bm_george**: Male, British English
- **bm_lewis**: Male, British English

### Supported Languages
- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Italian (it)
- Portuguese (pt)
- Polish (pl)
- Turkish (tr)
- Russian (ru)
- Dutch (nl)
- Czech (cs)
- Arabic (ar)
- Chinese (zh)
- Japanese (ja)
- Hungarian (hu)
- Korean (ko)

## Caching System

The system implements intelligent caching to avoid regenerating identical audio:

1. **Cache Key Generation**: Based on text content, voice, language, and speed
2. **Cache Check**: Before generating new audio, checks if identical audio exists
3. **Cache Storage**: Audio files are stored with deterministic filenames
4. **Cache Benefits**: Reduces API costs and improves response times

## Error Handling

The system handles various error scenarios:

- **Invalid text**: Empty or too long text (max 5000 characters)
- **Invalid voice**: Voice not in supported list
- **Invalid speed**: Speed outside 0.5-2.0 range
- **Invalid language**: Language not supported
- **API failures**: Replicate API errors
- **Storage failures**: S3 upload/download errors
- **Authentication**: Missing or invalid tokens

## File Structure

```
audio/
├── cache/
│   ├── en/
│   │   ├── af_bella/
│   │   │   └── hash.wav
│   │   └── am_adam/
│   │       └── hash.wav
│   └── es/
│       └── af_bella/
│           └── hash.wav
└── uploads/
    └── random-uuid.wav
```

## Monitoring and Logging

The system provides comprehensive logging:

- Audio generation requests
- Cache hits/misses
- Upload progress and completion
- Error details and stack traces
- Performance metrics

## Cost Optimization

1. **Caching**: Reduces duplicate API calls
2. **Compression**: Audio files are optimized for size
3. **CDN**: Consider using CloudFront for global distribution
4. **Lifecycle**: Set up S3 lifecycle policies for old files

## Security Considerations

1. **Authentication**: All endpoints (except voices) require valid JWT
2. **Input validation**: Text length and content validation
3. **Rate limiting**: Consider implementing rate limits
4. **Access control**: S3 bucket permissions and CORS configuration
5. **API keys**: Secure storage of Replicate and AWS credentials

## Troubleshooting

### Common Issues

1. **"REPLICATE_API_KEY is not configured"**
   - Ensure the environment variable is set correctly

2. **"AWS_S3_BUCKET is not configured"**
   - Check AWS environment variables

3. **"Failed to upload audio"**
   - Verify S3 permissions and bucket existence

4. **"Invalid voice"**
   - Use the `/api/audio/voices` endpoint to get valid voices

5. **"Text is too long"**
   - Limit text to 5000 characters or less

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will provide detailed console output for troubleshooting.
