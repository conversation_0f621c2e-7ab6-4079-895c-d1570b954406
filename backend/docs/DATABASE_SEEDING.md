# Database Seeding Guide

This guide explains the different database seeding options available for the MyZone Mindset application.

## Available Seed Commands

### 1. Safe Seed (<PERSON><PERSON>ult)
```bash
npm run db:seed
```
- **Purpose**: Ensures essential data exists without destroying existing data
- **Behavior**: Uses `upsert` operations to create data only if it doesn't exist
- **Use case**: Production environments, adding missing essential data
- **Safety**: ✅ Safe - preserves all existing data

### 2. Clean Seed (Destructive)
```bash
npm run db:seed:clean
# or
npm run db:seed:force
```
- **Purpose**: Completely resets the database with fresh seed data
- **Behavior**: Deletes ALL existing data before creating new seed data
- **Use case**: Development, testing, demo environments
- **Safety**: ⚠️ Destructive - removes all existing data

## What Gets Seeded

Both seed scripts create the following sample data:

### Users
- **Admin**: `<EMAIL>` / `admin123!`
- **HR Admin**: `<EMAIL>` / `hr123!`
- **Coach**: `<EMAIL>` / `coach123!`
- **Coachee**: `<EMAIL>` / `coachee123!`

### Organization
- **Sample Sports Organization** with HR admin assigned

### Relationships
- Coach-Coachee relationship between sample users
- Organization memberships for coach and coachee

### Sample Content
- **Goal Setting Exercise** with multiple question types
- **Pre-Competition Visualization** with sample script

## Usage Examples

### Development Setup
```bash
# First time setup - create schema and seed data
npm run db:push
npm run db:seed:clean

# Reset database during development
npm run db:seed:clean
```

### Production Deployment
```bash
# Deploy schema
npm run db:push

# Add essential data (safe)
npm run db:seed
```

### Adding Missing Data
```bash
# Ensure all essential accounts exist
npm run db:seed
```

## Data Deletion Order (Clean Seed)

The clean seed script deletes data in the correct order to respect foreign key constraints:

1. Activity logs
2. Assignment submissions
3. Assignments
4. Visualization assignments
5. Exercises
6. Visualizations
7. Coach-coachee relationships
8. Organization memberships
9. Organizations
10. Users

## Environment Considerations

### Development
- Use `db:seed:clean` freely for testing
- Reset data as needed during development
- Test with fresh data regularly

### Staging
- Use `db:seed` to ensure essential data exists
- Avoid `db:seed:clean` unless intentionally resetting
- Test production deployment procedures

### Production
- **NEVER** use `db:seed:clean` in production
- Only use `db:seed` to add missing essential data
- Always backup before any seeding operations

## Customizing Seed Data

### Modifying Safe Seed (`prisma/seed-safe.ts`)
- Add new essential users, organizations, or content
- Use `upsert` operations to avoid conflicts
- Test thoroughly in development first

### Modifying Clean Seed (`prisma/seed.ts`)
- Add deletion statements for new tables (in correct order)
- Add creation statements for new seed data
- Maintain foreign key constraint order

## Troubleshooting

### Foreign Key Constraint Errors
If you get foreign key errors during clean seeding:
1. Check the deletion order in `seed.ts`
2. Ensure child records are deleted before parent records
3. Add missing `deleteMany` statements for new tables

### Unique Constraint Errors
If you get unique constraint errors during safe seeding:
1. Check that `upsert` operations use correct `where` clauses
2. Ensure unique fields (like email) are properly handled
3. Verify the data doesn't already exist with different values

### Connection Errors
If seeding fails with connection errors:
1. Verify database is running and accessible
2. Check DATABASE_URL in environment variables
3. Test connection with `npm run db:test`

## Best Practices

1. **Always backup production data** before any seeding operations
2. **Test seed scripts** in development environment first
3. **Use safe seed by default** in production environments
4. **Document any custom seed data** added to your application
5. **Version control seed scripts** to track changes over time

## Integration with Deployment

The seeding is integrated into the deployment process:

```bash
# Full deployment with clean seed (development)
npm run db:deploy  # Includes clean seeding

# Production deployment
npm run db:push    # Schema only
npm run db:seed    # Safe seeding only
```
