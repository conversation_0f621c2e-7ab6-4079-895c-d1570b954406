# AWS RDS Deployment Guide

This guide will help you deploy your PostgreSQL database to AWS RDS.

## Prerequisites

- AWS Account with appropriate permissions
- AWS CLI installed and configured
- Node.js and npm installed

## Step 1: Create RDS Instance

### Option A: Using AWS Console

1. Go to [AWS RDS Console](https://console.aws.amazon.com/rds/)
2. Click "Create database"
3. Choose "Standard create"
4. Select "PostgreSQL"
5. Configure the following settings:

**Engine Options:**
- Engine version: PostgreSQL 15.4 (or latest)

**Templates:**
- Choose "Free tier" for development/testing
- Choose "Production" for production workloads

**Settings:**
- DB instance identifier: `myzone-mindset-db`
- Master username: `postgres`
- Master password: Create a strong password (save this!)

**DB Instance Class:**
- db.t3.micro (free tier eligible)

**Storage:**
- Storage type: General Purpose SSD (gp2)
- Allocated storage: 20 GB
- Enable storage autoscaling: Yes
- Maximum storage threshold: 100 GB

**Connectivity:**
- VPC: Default VPC (or your custom VPC)
- Public access: Yes (for initial setup, restrict later)
- VPC security group: Create new or use existing

**Database Options:**
- Initial database name: `myzone_mindset`

**Backup:**
- Backup retention period: 7 days
- Backup window: Choose a low-traffic time

**Monitoring:**
- Enable Enhanced monitoring: Yes (optional)

**Maintenance:**
- Auto minor version upgrade: Yes

### Option B: Using AWS CLI

```bash
aws rds create-db-instance \
  --db-instance-identifier myzone-mindset-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --engine-version 15.4 \
  --master-username postgres \
  --master-user-password "YourSecurePassword123!" \
  --allocated-storage 20 \
  --storage-type gp2 \
  --db-name myzone_mindset \
  --backup-retention-period 7 \
  --storage-encrypted \
  --publicly-accessible \
  --no-multi-az
```

## Step 2: Configure Security Groups

1. Go to EC2 Console > Security Groups
2. Find the security group associated with your RDS instance
3. Add inbound rule:
   - Type: PostgreSQL
   - Protocol: TCP
   - Port: 5432
   - Source: Your IP address or application server security group

## Step 3: Update Environment Configuration

1. Copy `.env.production.example` to `.env.production`
2. Update the DATABASE_URL with your RDS endpoint:

```env
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/myzone_mindset?schema=public&sslmode=require"
```

3. Get your RDS endpoint from AWS Console or CLI:

```bash
aws rds describe-db-instances --db-instance-identifier myzone-mindset-db --query 'DBInstances[0].Endpoint.Address' --output text
```

## Step 4: Deploy Database Schema

Run the deployment script:

```bash
cd backend
npm run db:deploy
```

Or manually:

```bash
# Load production environment
export $(cat .env.production | grep -v '^#' | xargs)

# Test connection
npx prisma db execute --url="$DATABASE_URL" --stdin <<< "SELECT 1;"

# Generate Prisma client
npx prisma generate

# Deploy schema
npx prisma db push

# Seed initial data
npx prisma db seed
```

## Step 5: Verify Deployment

Test your database connection:

```bash
# Using psql (if installed)
psql "$DATABASE_URL" -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';"

# Using Prisma
npx prisma studio --url="$DATABASE_URL"
```

## Step 6: Security Hardening

### 1. Restrict Public Access
- Modify RDS instance to disable public accessibility
- Use VPC endpoints or bastion hosts for access

### 2. Use IAM Database Authentication (Optional)
```bash
aws rds modify-db-instance \
  --db-instance-identifier myzone-mindset-db \
  --enable-iam-database-authentication
```

### 3. Enable Encryption in Transit
Ensure your DATABASE_URL includes `sslmode=require`

### 4. Use AWS Secrets Manager
Store database credentials securely:

```bash
aws secretsmanager create-secret \
  --name myzone-mindset/db-credentials \
  --description "Database credentials for MyZone Mindset" \
  --secret-string '{"username":"postgres","password":"YourSecurePassword123!"}'
```

## Step 7: Monitoring and Maintenance

### Enable CloudWatch Monitoring
- CPU utilization
- Database connections
- Read/Write IOPS
- Free storage space

### Set up Alerts
```bash
aws cloudwatch put-metric-alarm \
  --alarm-name "RDS-HighCPU" \
  --alarm-description "RDS CPU utilization is too high" \
  --metric-name CPUUtilization \
  --namespace AWS/RDS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=DBInstanceIdentifier,Value=myzone-mindset-db \
  --evaluation-periods 2
```

## Troubleshooting

### Connection Issues
1. Check security group rules
2. Verify RDS instance is in "Available" state
3. Test network connectivity
4. Verify credentials

### Performance Issues
1. Monitor CloudWatch metrics
2. Check for long-running queries
3. Consider upgrading instance class
4. Optimize database queries

### Common Errors

**Error: "Connection refused"**
- Check security group allows port 5432
- Verify RDS instance is publicly accessible (if needed)

**Error: "SSL connection required"**
- Add `sslmode=require` to DATABASE_URL

**Error: "Authentication failed"**
- Verify username and password
- Check if IAM authentication is enabled

## Cost Optimization

1. Use db.t3.micro for development (free tier)
2. Enable storage autoscaling
3. Set appropriate backup retention period
4. Consider Aurora Serverless for variable workloads
5. Use Reserved Instances for production

## Backup and Recovery

### Manual Backup
```bash
aws rds create-db-snapshot \
  --db-instance-identifier myzone-mindset-db \
  --db-snapshot-identifier myzone-mindset-snapshot-$(date +%Y%m%d)
```

### Point-in-Time Recovery
RDS automatically maintains transaction logs for point-in-time recovery within the backup retention period.

## Next Steps

1. Set up application deployment to use RDS
2. Configure connection pooling
3. Implement database monitoring
4. Set up automated backups
5. Plan for scaling and high availability
