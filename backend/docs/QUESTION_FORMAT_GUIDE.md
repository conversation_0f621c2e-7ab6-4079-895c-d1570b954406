# Question Format Guide

This guide explains the correct format for exercise questions in the MyZone Mindset application.

## Overview

Questions are stored in the `Exercise.questions` field as JSON and must follow specific TypeScript interfaces defined in `backend/src/types/exercise.types.ts`.

## Question Structure

All questions must be wrapped in a `questions` object:

```json
{
  "questions": [
    // Array of question objects
  ]
}
```

## Question Types

### 1. Free Text Question

```typescript
{
  id: "q1",
  type: "free_text",
  prompt: "What is your primary athletic goal for this season?",
  description: "Be specific about what you want to achieve and when.",
  required: true,
  placeholder: "e.g., Improve my 5K time by 30 seconds",
  maxLength: 500
}
```

**Required fields:**

- `id`: Unique identifier
- `type`: Must be `"free_text"`
- `prompt`: The question text

**Optional fields:**

- `description`: Additional context
- `required`: Boolean (default: false)
- `placeholder`: Input placeholder text
- `maxLength`: Maximum character limit

### 2. Likert Scale Question

#### Single Response

```typescript
{
  id: "q2",
  type: "likert",
  prompt: "How confident are you in achieving this goal?",
  description: "Rate your current confidence level honestly.",
  required: true,
  responseMode: "single",
  scaleMin: 1,
  scaleMax: 5,
  labels: {
    1: "Not confident at all",
    2: "Slightly confident",
    3: "Moderately confident",
    4: "Very confident",
    5: "Extremely confident"
  }
}
```

#### Multi Response

```typescript
{
  id: "q2b",
  type: "likert",
  prompt: "Rate your current and goal confidence levels",
  responseMode: "multi",
  multiResponseLabels: ["Current", "Goal"],
  scaleMin: 1,
  scaleMax: 5,
  labels: {
    1: "Not confident",
    5: "Very confident"
  }
}
```

**Required fields:**

- `id`, `type`, `prompt`
- `responseMode`: `"single"` or `"multi"`
- `scaleMin`, `scaleMax`: Number range
- `labels`: Object mapping scale values to descriptions

**Conditional fields:**

- `multiResponseLabels`: Required when `responseMode` is `"multi"`

### 3. Task Question

```typescript
{
  id: "q3",
  type: "task",
  prompt: "Create an action plan for achieving your goal",
  description: "Check this box when you have completed the task.",
  required: true,
  isCompleted: false
}
```

**Required fields:**

- `id`, `type`, `prompt`
- `isCompleted`: Boolean indicating completion status

### 4. Table Question

#### Fixed Rows

```typescript
{
  id: "q4",
  type: "table",
  prompt: "Weekly training schedule",
  description: "Plan your training activities for each day.",
  required: false,
  rowMode: "fixed",
  fixedRows: 7,
  columns: [
    {
      id: "day",
      header: "Day",
      columnType: "free_text",
      placeholder: "e.g., Monday"
    },
    {
      id: "intensity",
      header: "Intensity Level",
      columnType: "likert",
      scaleMin: 1,
      scaleMax: 5,
      labels: {
        1: "Very Light",
        3: "Moderate",
        5: "Very Hard"
      }
    }
  ]
}
```

#### Dynamic Rows

```typescript
{
  id: "q4b",
  type: "table",
  prompt: "List your training goals",
  rowMode: "dynamic",
  columns: [
    {
      id: "goal",
      header: "Goal",
      columnType: "free_text",
      placeholder: "Enter your goal"
    }
  ]
}
```

**Required fields:**

- `id`, `type`, `prompt`
- `rowMode`: `"fixed"` or `"dynamic"`
- `columns`: Array of column definitions

**Conditional fields:**

- `fixedRows`: Required when `rowMode` is `"fixed"`

**Column Types:**

- `free_text`: Text input with optional `placeholder`
- `likert`: Scale input with `scaleMin`, `scaleMax`, and `labels`

### 5. Graph 2D Question

```typescript
{
  id: "q5",
  type: "graph_2d",
  prompt: "Track your progress over time",
  description: "Plot your expected performance improvement.",
  required: false,
  xAxisLabel: "Weeks",
  yAxisLabel: "Performance Level (1-10)",
  xAxisMin: 0,
  xAxisMax: 12,
  yAxisMin: 1,
  yAxisMax: 10
}
```

**Required fields:**

- `id`, `type`, `prompt`
- `xAxisLabel`, `yAxisLabel`: Axis labels

**Optional fields:**

- `xAxisMin`, `xAxisMax`: X-axis range
- `yAxisMin`, `yAxisMax`: Y-axis range

## Common Fields

All question types support these common fields:

- `id`: Unique identifier within the exercise
- `type`: Question type discriminator
- `prompt`: The main question text
- `description`: Optional additional context
- `required`: Whether the question must be answered (default: false)

## Validation Rules

1. **Unique IDs**: Each question must have a unique `id` within the exercise
2. **Required Fields**: All required fields for each question type must be present
3. **Type Safety**: The `type` field determines which additional fields are required
4. **Scale Validation**: For Likert questions, `scaleMin` must be less than `scaleMax`
5. **Label Mapping**: Likert `labels` object keys must be numbers within the scale range

## Example Complete Exercise

```typescript
{
  id: "sample-exercise-id",
  name: "Goal Setting Exercise",
  description: "A comprehensive exercise to help athletes set goals.",
  questions: [
    {
      id: "q1",
      type: "free_text",
      prompt: "What is your primary athletic goal?",
      required: true,
      maxLength: 500
    },
    {
      id: "q2",
      type: "likert",
      prompt: "How confident are you?",
      responseMode: "single",
      scaleMin: 1,
      scaleMax: 5,
      labels: {
        1: "Not confident",
        5: "Very confident"
      }
    }
  ]
}
```

## Migration Notes

When updating existing questions:

1. **Old Format**: Questions used `question` instead of `prompt`
2. **Old Format**: Likert questions used `scale` and `labels` array instead of `scaleMin`/`scaleMax` and `labels` object
3. **Old Format**: Questions were directly in an array instead of wrapped in a `questions` object

## Testing

Always test question formats by:

1. Running the seed script: `npm run db:seed:clean`
2. Verifying questions display correctly in the frontend
3. Testing form submission with various question types
4. Validating data storage and retrieval
