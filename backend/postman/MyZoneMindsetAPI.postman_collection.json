{"info": {"_postman_id": "YOUR_UNIQUE_COLLECTION_ID", "name": "MyZone Mindset API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"firstName\": \"Test\",\n    \"lastName\": \"User\",\n    \"role\": \"COACHEE\" // Optional, defaults to COACHEE. Can be COACH or ADMIN\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "description": "Registers a new user (Coachee, Coach, or Admin)."}, "response": []}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "description": "Logs in an existing user and returns user details and a JWT token."}, "response": []}, {"name": "Request Password Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/request-reset", "host": ["{{baseUrl}}"], "path": ["api", "auth", "request-reset"]}, "description": "Request a password reset link to be sent via email"}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"token\": \"reset_token_from_email\",\n    \"newPassword\": \"new_password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "auth", "reset-password"]}, "description": "Reset password using token received via email"}, "response": []}], "description": "Authentication related endpoints"}, {"name": "Exercises", "item": [{"name": "Get All Exercises", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/exercises", "host": ["{{baseUrl}}"], "path": ["api", "exercises"]}, "description": "Retrieves all exercises. Requires authentication."}, "response": []}, {"name": "Get Exercise by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/exercises/:id", "host": ["{{baseUrl}}"], "path": ["api", "exercises", ":id"], "variable": [{"key": "id", "value": "exercise-uuid-here", "description": "UUID of the exercise"}]}, "description": "Retrieves a specific exercise by ID. Requires authentication."}, "response": []}, {"name": "Create Exercise", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Sample Exercise\",\n    \"description\": \"This is a sample exercise description\",\n    \"questions\": [\n        {\n            \"id\": 1,\n            \"text\": \"What is your goal?\",\n            \"type\": \"text\"\n        },\n        {\n            \"id\": 2,\n            \"text\": \"Rate your confidence\",\n            \"type\": \"scale\",\n            \"min\": 1,\n            \"max\": 10\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/exercises", "host": ["{{baseUrl}}"], "path": ["api", "exercises"]}, "description": "Creates a new exercise. Requires authentication and COACH/ADMIN role."}, "response": []}, {"name": "Update Exercise", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Exercise Name\",\n    \"description\": \"Updated exercise description\",\n    \"questions\": [\n        {\n            \"id\": 1,\n            \"text\": \"Updated question text\",\n            \"type\": \"text\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/exercises/:id", "host": ["{{baseUrl}}"], "path": ["api", "exercises", ":id"], "variable": [{"key": "id", "value": "exercise-uuid-here", "description": "UUID of the exercise to update"}]}, "description": "Updates an existing exercise. Requires authentication and COACH/ADMIN role."}, "response": []}, {"name": "Delete Exercise", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/exercises/:id", "host": ["{{baseUrl}}"], "path": ["api", "exercises", ":id"], "variable": [{"key": "id", "value": "exercise-uuid-here", "description": "UUID of the exercise to delete"}]}, "description": "Deletes an exercise. Requires authentication and COACH/ADMIN role."}, "response": []}], "description": "Exercise management endpoints"}, {"name": "Visualizations", "item": [{"name": "Get All Visualizations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/visualizations", "host": ["{{baseUrl}}"], "path": ["api", "visualizations"]}, "description": "Retrieves all visualizations. Requires authentication."}, "response": []}, {"name": "Get Visualization by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/visualizations/:id", "host": ["{{baseUrl}}"], "path": ["api", "visualizations", ":id"], "variable": [{"key": "id", "value": "visualization-uuid-here", "description": "UUID of the visualization"}]}, "description": "Retrieves a specific visualization by ID. Requires authentication."}, "response": []}, {"name": "Create Visualization", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Sample Visualization\",\n    \"description\": \"This is a sample visualization description\",\n    \"audioUrl\": \"https://example.com/audio/sample.mp3\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/visualizations", "host": ["{{baseUrl}}"], "path": ["api", "visualizations"]}, "description": "Creates a new visualization. Requires authentication and COACH/ADMIN role."}, "response": []}, {"name": "Update Visualization", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Visualization Title\",\n    \"description\": \"Updated visualization description\",\n    \"audioUrl\": \"https://example.com/audio/updated.mp3\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/visualizations/:id", "host": ["{{baseUrl}}"], "path": ["api", "visualizations", ":id"], "variable": [{"key": "id", "value": "visualization-uuid-here", "description": "UUID of the visualization to update"}]}, "description": "Updates an existing visualization. Requires authentication and COACH/ADMIN role."}, "response": []}, {"name": "Delete Visualization", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/visualizations/:id", "host": ["{{baseUrl}}"], "path": ["api", "visualizations", ":id"], "variable": [{"key": "id", "value": "visualization-uuid-here", "description": "UUID of the visualization to delete"}]}, "description": "Deletes a visualization. Requires authentication and COACH/ADMIN role."}, "response": []}], "description": "Visualization management endpoints"}, {"name": "Organizations", "item": [{"name": "Get All Organizations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/organizations", "host": ["{{baseUrl}}"], "path": ["api", "organizations"]}, "description": "Retrieves all organizations. Requires authentication."}, "response": []}, {"name": "Get Organization by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/organizations/:id", "host": ["{{baseUrl}}"], "path": ["api", "organizations", ":id"], "variable": [{"key": "id", "value": "organization-uuid-here", "description": "UUID of the organization"}]}, "description": "Retrieves a specific organization by ID. Requires authentication."}, "response": []}, {"name": "Create Organization", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Sample Organization\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/organizations", "host": ["{{baseUrl}}"], "path": ["api", "organizations"]}, "description": "Creates a new organization. Requires authentication and ADMIN role."}, "response": []}, {"name": "Update Organization", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Organization Name\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/organizations/:id", "host": ["{{baseUrl}}"], "path": ["api", "organizations", ":id"], "variable": [{"key": "id", "value": "organization-uuid-here", "description": "UUID of the organization to update"}]}, "description": "Updates an existing organization. Requires authentication and ADMIN role."}, "response": []}, {"name": "Delete Organization", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/organizations/:id", "host": ["{{baseUrl}}"], "path": ["api", "organizations", ":id"], "variable": [{"key": "id", "value": "organization-uuid-here", "description": "UUID of the organization to delete"}]}, "description": "Deletes an organization. Requires authentication and ADMIN role."}, "response": []}, {"name": "Add Coach to Organization", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"coach-user-uuid-here\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/organizations/:organizationId/coaches", "host": ["{{baseUrl}}"], "path": ["api", "organizations", ":organizationId", "coaches"], "variable": [{"key": "organizationId", "value": "organization-uuid-here", "description": "UUID of the organization"}]}, "description": "Adds a coach to an organization. Requires authentication and ADMIN role."}, "response": []}, {"name": "Add Coachee to Organization", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"coachee-user-uuid-here\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/organizations/:organizationId/coachees", "host": ["{{baseUrl}}"], "path": ["api", "organizations", ":organizationId", "coachees"], "variable": [{"key": "organizationId", "value": "organization-uuid-here", "description": "UUID of the organization"}]}, "description": "Adds a coachee to an organization. Requires authentication and ADMIN/COACH role."}, "response": []}], "description": "Organization management endpoints"}, {"name": "Assignments", "item": [{"name": "Create Assignment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"exerciseId\": \"exercise-uuid-here\",\n    \"coacheeId\": \"coachee-uuid-here\",\n    \"dueDate\": \"2025-04-30T00:00:00.000Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assignments", "host": ["{{baseUrl}}"], "path": ["api", "assignments"]}, "description": "Creates a new exercise assignment. Requires authentication and COACH role."}, "response": []}, {"name": "Get All Assignments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/assignments", "host": ["{{baseUrl}}"], "path": ["api", "assignments"]}, "description": "Retrieves all assignments for the authenticated user. For coaches, shows assignments they've created. For coachees, shows assignments assigned to them."}, "response": []}], "description": "Exercise assignment management endpoints"}, {"name": "Visualization Assignments", "item": [{"name": "Create Visualization Assignment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"visualizationId\": \"visualization-uuid-here\",\n    \"coacheeId\": \"coachee-uuid-here\",\n    \"dueDate\": \"2025-04-30T00:00:00.000Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/visualizationAssignments", "host": ["{{baseUrl}}"], "path": ["api", "visualizationAssignments"]}, "description": "Creates a new visualization assignment. Requires authentication and COACH role."}, "response": []}, {"name": "Get All Visualization Assignments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/visualizationAssignments", "host": ["{{baseUrl}}"], "path": ["api", "visualizationAssignments"]}, "description": "Retrieves all visualization assignments for the authenticated user. For coaches, shows assignments they've created. For coachees, shows assignments assigned to them."}, "response": []}, {"name": "Update Visualization Assignment Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"COMPLETED\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/visualizationAssignments/:id/status", "host": ["{{baseUrl}}"], "path": ["api", "visualizationAssignments", ":id", "status"], "variable": [{"key": "id", "value": "visualization-assignment-uuid-here", "description": "UUID of the visualization assignment"}]}, "description": "Updates the status of a visualization assignment. Requires authentication."}, "response": []}], "description": "Visualization assignment management endpoints"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-save the token from login responses", "if (pm.response.code === 200 && pm.request.url.toString().includes('/api/auth/login')) {", "    const jsonResponse = pm.response.json();", "    if (jsonResponse && jsonResponse.token) {", "        pm.environment.set('token', jsonResponse.token);", "        console.log('Token saved to environment');", "    }", "}"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3001", "type": "string"}]}