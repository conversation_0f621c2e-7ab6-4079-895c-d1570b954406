import {
  Visualization,
  CreateVisualizationData,
} from "../types/api/visualizations.types";
import axiosInstance from "./axiosConfig";

export const getAllVisualizations = async (): Promise<Visualization[]> => {
  const response = await axiosInstance.get<Visualization[]>("/visualizations");
  return response.data;
};

export const getVisualizationById = async (
  id: string
): Promise<Visualization> => {
  const response = await axiosInstance.get<Visualization>(
    `/visualizations/${id}`
  );
  return response.data;
};

export const createVisualization = async (
  data: CreateVisualizationData
): Promise<Visualization> => {
  const response = await axiosInstance.post<Visualization>(
    "/visualizations",
    data
  );
  return response.data;
};

export const updateVisualization = async (
  id: string,
  data: Partial<CreateVisualizationData>
): Promise<Visualization> => {
  const response = await axiosInstance.put<Visualization>(
    `/visualizations/${id}`,
    data
  );
  return response.data;
};

export const deleteVisualization = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/visualizations/${id}`);
};
