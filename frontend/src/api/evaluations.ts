import axiosInstance from "./axiosConfig";

export interface Evaluation {
  id: string;
  coacheeId: string;
  coachId: string;
  title?: string;
  notes?: string;
  composure: number;
  concentration: number;
  confidence: number;
  copeability: number;
  cohesion: number;
  createdAt: string;
  updatedAt: string;
  coachee: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  coach: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreateEvaluationData {
  coacheeId: string;
  title?: string;
  notes?: string;
  composure: number;
  concentration: number;
  confidence: number;
  copeability: number;
  cohesion: number;
}

export interface UpdateEvaluationData {
  title?: string;
  notes?: string;
  composure?: number;
  concentration?: number;
  confidence?: number;
  copeability?: number;
  cohesion?: number;
}

export interface EvaluationTrend {
  date: string;
  composure: number;
  concentration: number;
  confidence: number;
  copeability: number;
  cohesion: number;
  overall: number;
}

export interface EvaluationsResponse {
  evaluations: Evaluation[];
  total: number;
  hasMore: boolean;
}

export const getAllEvaluations = async (params?: {
  coacheeId?: string;
  limit?: number;
  offset?: number;
  startDate?: string;
  endDate?: string;
}): Promise<EvaluationsResponse> => {
  const response = await axiosInstance.get<EvaluationsResponse>("/evaluations", {
    params,
  });
  return response.data;
};

export const getEvaluationById = async (id: string): Promise<Evaluation> => {
  const response = await axiosInstance.get<Evaluation>(`/evaluations/${id}`);
  return response.data;
};

export const createEvaluation = async (
  data: CreateEvaluationData
): Promise<Evaluation> => {
  const response = await axiosInstance.post<Evaluation>("/evaluations", data);
  return response.data;
};

export const updateEvaluation = async (
  id: string,
  data: UpdateEvaluationData
): Promise<Evaluation> => {
  const response = await axiosInstance.put<Evaluation>(
    `/evaluations/${id}`,
    data
  );
  return response.data;
};

export const deleteEvaluation = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/evaluations/${id}`);
};

export const getEvaluationTrends = async (
  coacheeId: string
): Promise<EvaluationTrend[]> => {
  const response = await axiosInstance.get<EvaluationTrend[]>(
    `/evaluations/coachee/${coacheeId}/trends`
  );
  return response.data;
};

export const getLatestEvaluation = async (
  coacheeId: string
): Promise<Evaluation | null> => {
  try {
    const response = await axiosInstance.get<Evaluation>(
      `/evaluations/coachee/${coacheeId}/latest`
    );
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 404) {
      return null; // No evaluations found
    }
    throw error;
  }
};
