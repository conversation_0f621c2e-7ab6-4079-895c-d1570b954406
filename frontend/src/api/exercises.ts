import { CreateExerciseData, Exercise } from "../types/api/exercises.types";
import axiosInstance from "./axiosConfig";

export const getAllExercises = async (): Promise<Exercise[]> => {
  const response = await axiosInstance.get<Exercise[]>("/exercises");
  return response.data;
};

export const getExerciseById = async (id: string): Promise<Exercise> => {
  const response = await axiosInstance.get<Exercise>(`/exercises/${id}`);
  return response.data;
};

export const createExercise = async (
  data: CreateExerciseData
): Promise<Exercise> => {
  const response = await axiosInstance.post<Exercise>("/exercises", data);
  return response.data;
};

export const updateExercise = async (
  id: string,
  data: Partial<CreateExerciseData>
): Promise<Exercise> => {
  const response = await axiosInstance.put<Exercise>(`/exercises/${id}`, data);
  return response.data;
};

export const deleteExercise = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/exercises/${id}`);
};
