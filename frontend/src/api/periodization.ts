import axiosInstance from "./axiosConfig";

export interface CreatePeriodizationData {
  coacheeId: string;
  name: string;
  offSeasonStartDate: string;
  offSeasonEndDate: string;
  prepStartDate: string;
  prepEndDate: string;
  preCompStartDate: string;
  preCompEndDate: string;
  competitionStartDate: string;
  competitionEndDate: string;
  scheduleMentalToughness: boolean;
  scheduleMentalWellness: boolean;
  mentalWellnessStartDate?: string;
  mentalWellnessEndDate?: string;
}

export interface PeriodizationPreviewData {
  coacheeId: string;
  offSeasonStartDate: string;
  offSeasonEndDate: string;
  prepStartDate: string;
  prepEndDate: string;
  preCompStartDate: string;
  preCompEndDate: string;
  competitionStartDate: string;
  competitionEndDate: string;
  scheduleMentalToughness: boolean;
  scheduleMentalWellness: boolean;
  mentalWellnessStartDate?: string;
  mentalWellnessEndDate?: string;
}

export interface PeriodizationEntry {
  week: number;
  assignmentDate: string;
  exerciseId: string;
  exerciseTitle: string;
  phase: string;
  skill?: string;
  type: "MT" | "MW";
}

export interface PeriodizationPreview {
  entries: PeriodizationEntry[];
  errors: string[];
  totalAssignments: number;
  phaseBreakdown: {
    offSeason: number;
    prep: number;
    preComp: number;
  };
}

export interface Periodization {
  id: string;
  coacheeId: string;
  coachId: string;
  name: string;
  offSeasonStartDate: string;
  offSeasonEndDate: string;
  prepStartDate: string;
  prepEndDate: string;
  preCompStartDate: string;
  preCompEndDate: string;
  competitionStartDate: string;
  competitionEndDate: string;
  scheduleMentalToughness: boolean;
  scheduleMentalWellness: boolean;
  mentalWellnessStartDate?: string;
  mentalWellnessEndDate?: string;
  status: "ACTIVE" | "COMPLETED" | "CANCELLED";
  createdAt: string;
  updatedAt: string;
  coachee: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  coach: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreatePeriodizationResponse {
  message: string;
  periodization: Periodization;
  assignmentCount: number;
}

/**
 * Generate a preview of periodization without creating records
 */
export const generatePeriodizationPreview = async (
  data: PeriodizationPreviewData
): Promise<PeriodizationPreview> => {
  const response = await axiosInstance.post<PeriodizationPreview>(
    "/periodizations/preview",
    data
  );
  return response.data;
};

/**
 * Create a new periodization and generate assignments
 */
export const createPeriodization = async (
  data: CreatePeriodizationData
): Promise<CreatePeriodizationResponse> => {
  const response = await axiosInstance.post<CreatePeriodizationResponse>(
    "/periodizations",
    data
  );
  return response.data;
};

/**
 * Get all periodizations for the authenticated user
 */
export const getPeriodizations = async (): Promise<Periodization[]> => {
  const response = await axiosInstance.get<Periodization[]>("/periodizations");
  return response.data;
};

/**
 * Get periodization by ID
 */
export const getPeriodizationById = async (
  id: string
): Promise<Periodization> => {
  const response = await axiosInstance.get<Periodization>(
    `/periodizations/${id}`
  );
  return response.data;
};

/**
 * Update periodization status
 */
export const updatePeriodizationStatus = async (
  id: string,
  status: "ACTIVE" | "COMPLETED" | "CANCELLED"
): Promise<{ message: string; periodization: Periodization }> => {
  const response = await axiosInstance.patch<{
    message: string;
    periodization: Periodization;
  }>(`/periodizations/${id}/status`, { status });
  return response.data;
};

/**
 * Delete periodization
 */
export const deletePeriodization = async (
  id: string,
  deleteAssignments: boolean = false
): Promise<{ message: string }> => {
  const response = await axiosInstance.delete<{ message: string }>(
    `/periodizations/${id}?deleteAssignments=${deleteAssignments}`
  );
  return response.data;
};
