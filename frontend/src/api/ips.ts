import axiosInstance from "./axiosConfig";
import {
  IPSRecord,
  CreateIPSRecordRequest,
  UpdateIPSRecordRequest,
  GetIPSRecordsRequest,
  GetIPSRecordsResponse,
  IPSTrendData,
  GetIPSTrendsRequest,
  GetIPSTrendsResponse,
} from "../types/api/ips.types";

/**
 * Create a new IPS record
 */
export const createIPSRecord = async (
  data: CreateIPSRecordRequest
): Promise<IPSRecord> => {
  const response = await axiosInstance.post<IPSRecord>("/ips", data);
  return response.data;
};

/**
 * Get all IPS records with optional filtering
 */
export const getIPSRecords = async (
  params?: GetIPSRecordsRequest
): Promise<GetIPSRecordsResponse> => {
  const response = await axiosInstance.get<GetIPSRecordsResponse>("/ips", {
    params,
  });
  return response.data;
};

/**
 * Get IPS record by ID
 */
export const getIPSRecordById = async (id: string): Promise<IPSRecord> => {
  const response = await axiosInstance.get<IPSRecord>(`/ips/${id}`);
  return response.data;
};

/**
 * Update IPS record
 */
export const updateIPSRecord = async (
  id: string,
  data: UpdateIPSRecordRequest
): Promise<IPSRecord> => {
  const response = await axiosInstance.put<IPSRecord>(`/ips/${id}`, data);
  return response.data;
};

/**
 * Delete IPS record
 */
export const deleteIPSRecord = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/ips/${id}`);
};

/**
 * Get IPS trends for visualization
 */
export const getIPSTrends = async (
  params: GetIPSTrendsRequest
): Promise<GetIPSTrendsResponse> => {
  const response = await axiosInstance.get<GetIPSTrendsResponse>("/ips/trends", {
    params,
  });
  return response.data;
};

/**
 * Get IPS records for a specific coachee
 */
export const getCoacheeIPSRecords = async (
  coacheeId: string,
  limit = 50,
  offset = 0
): Promise<GetIPSRecordsResponse> => {
  return getIPSRecords({ coacheeId, limit, offset });
};

/**
 * Get IPS records for the current user (coachee)
 */
export const getMyIPSRecords = async (
  limit = 50,
  offset = 0
): Promise<GetIPSRecordsResponse> => {
  return getIPSRecords({ limit, offset });
};

/**
 * Get IPS trends for the current user (coachee)
 */
export const getMyIPSTrends = async (
  startDate?: string,
  endDate?: string
): Promise<GetIPSTrendsResponse> => {
  // For coachees, the backend will automatically filter to their own records
  // so we don't need to pass coacheeId
  const params: any = {};
  if (startDate) params.startDate = startDate;
  if (endDate) params.endDate = endDate;
  
  const response = await axiosInstance.get<GetIPSTrendsResponse>("/ips/trends", {
    params,
  });
  return response.data;
};
