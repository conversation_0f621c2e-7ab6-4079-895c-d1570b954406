import axiosInstance from "./axiosConfig";
import {
  ActivityLog,
  ActivityLogFilters,
  ActivityLogResponse,
  ActivityStatsResponse,
} from "../types/api/activityLog.types";

export const getActivityLogs = async (
  filters: ActivityLogFilters = {}
): Promise<ActivityLogResponse> => {
  const params = new URLSearchParams();

  if (filters.coacheeId) params.append("coacheeId", filters.coacheeId);
  if (filters.coachId) params.append("coachId", filters.coachId);
  if (filters.eventType) params.append("eventType", filters.eventType);
  if (filters.startDate) params.append("startDate", filters.startDate);
  if (filters.endDate) params.append("endDate", filters.endDate);
  if (filters.limit) params.append("limit", filters.limit.toString());
  if (filters.offset) params.append("offset", filters.offset.toString());

  const response = await axiosInstance.get<ActivityLogResponse>(
    `/activity-logs?${params.toString()}`
  );
  return response.data;
};

export const getCoacheeActivityLogs = async (
  coacheeId: string,
  filters: Omit<ActivityLogFilters, "coacheeId"> = {}
): Promise<ActivityLogResponse> => {
  const params = new URLSearchParams();

  if (filters.eventType) params.append("eventType", filters.eventType);
  if (filters.startDate) params.append("startDate", filters.startDate);
  if (filters.endDate) params.append("endDate", filters.endDate);
  if (filters.limit) params.append("limit", filters.limit.toString());
  if (filters.offset) params.append("offset", filters.offset.toString());

  const response = await axiosInstance.get<ActivityLogResponse>(
    `/activity-logs/coachee/${coacheeId}?${params.toString()}`
  );
  return response.data;
};

export const getCoachActivityLogs = async (
  coachId: string,
  filters: Omit<ActivityLogFilters, "coachId"> = {}
): Promise<ActivityLogResponse> => {
  const params = new URLSearchParams();

  if (filters.eventType) params.append("eventType", filters.eventType);
  if (filters.startDate) params.append("startDate", filters.startDate);
  if (filters.endDate) params.append("endDate", filters.endDate);
  if (filters.limit) params.append("limit", filters.limit.toString());
  if (filters.offset) params.append("offset", filters.offset.toString());

  const response = await axiosInstance.get<ActivityLogResponse>(
    `/activity-logs/coach/${coachId}?${params.toString()}`
  );
  return response.data;
};

export const getRecentActivity = async (
  limit: number = 10
): Promise<{ success: boolean; data: ActivityLog[] }> => {
  const response = await axiosInstance.get<{ success: boolean; data: ActivityLog[] }>(
    `/activity-logs/recent?limit=${limit}`
  );
  return response.data;
};

export const getActivityStats = async (
  startDate?: string,
  endDate?: string
): Promise<ActivityStatsResponse> => {
  const params = new URLSearchParams();

  if (startDate) params.append("startDate", startDate);
  if (endDate) params.append("endDate", endDate);

  const response = await axiosInstance.get<ActivityStatsResponse>(
    `/activity-logs/stats?${params.toString()}`
  );
  return response.data;
};
