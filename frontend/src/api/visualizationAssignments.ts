import {
  VisualizationAssignment,
  CreateVisualizationAssignmentData,
} from "../types/api/visualizationAssignment.types";
import axiosInstance from "./axiosConfig";

export const getAllVisualizationAssignments = async (): Promise<
  VisualizationAssignment[]
> => {
  const response = await axiosInstance.get<VisualizationAssignment[]>(
    "/visualization-assignments"
  );
  return response.data;
};

export const getVisualizationAssignmentById = async (
  id: string
): Promise<VisualizationAssignment> => {
  const response = await axiosInstance.get<VisualizationAssignment>(
    `/visualization-assignments/${id}`
  );
  return response.data;
};

export const createVisualizationAssignment = async (
  data: CreateVisualizationAssignmentData
): Promise<VisualizationAssignment> => {
  const response = await axiosInstance.post<VisualizationAssignment>(
    "/visualization-assignments",
    data
  );
  return response.data;
};

export const markVisualizationAssignmentComplete = async (
  id: string
): Promise<VisualizationAssignment> => {
  const response = await axiosInstance.post<VisualizationAssignment>(
    `/visualization-assignments/${id}/complete`
  );
  return response.data;
};

export const deleteVisualizationAssignment = async (
  id: string
): Promise<void> => {
  await axiosInstance.delete(`/visualization-assignments/${id}`);
};
