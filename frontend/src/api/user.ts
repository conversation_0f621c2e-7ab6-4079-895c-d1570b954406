import { User } from "../types/api/user.types";
import axiosInstance from "./axiosConfig";

export const checkIfUserExists = async (email: string): Promise<boolean> => {
  const response = await axiosInstance.get<User>(`/user/${email}`);
  if (response.status === 200 && response.data) {
    return true;
  }
  return false;
};

export const getUserData = async (): Promise<User> => {
  const response = await axiosInstance.get<User>(`/user`);
  if (response.status === 200 && response.data) {
    return response.data;
  }
  throw new Error("User not found");
};

export const getAllUsers = async (): Promise<User[]> => {
  const response = await axiosInstance.get<User[]>(`/user/all`);
  if (response.status === 200 && response.data) {
    return response.data;
  }
  throw new Error("No users found");
};

export const getUserById = async (id: string): Promise<User> => {
  const response = await axiosInstance.get<User>(`/user/id/${id}`);
  if (response.status === 200 && response.data) {
    return response.data;
  }
  throw new Error(`User with ID ${id} not found`);
};
