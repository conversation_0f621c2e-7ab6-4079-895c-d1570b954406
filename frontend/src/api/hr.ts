import axiosInstance from "./axiosConfig";
import { User } from "../types/api/user.types";

export interface OrganizationMember {
  id: string;
  organizationId: string;
  userId: string;
  role: "COACH" | "COACHEE";
  addedAt: string;
  user: User;
}

export interface OrganizationStats {
  totalCoaches: number;
  totalCoachees: number;
  totalAssignments: number;
  completedAssignments: number;
  pendingAssignments: number;
  completionRate: number;
}

// Add coach to organization
export const addCoachToOrganization = async (
  organizationId: string,
  userId: string
): Promise<OrganizationMember> => {
  const response = await axiosInstance.post<OrganizationMember>(
    `/organizations/${organizationId}/coaches`,
    { userId }
  );
  return response.data;
};

// Add coachee to organization
export const addCoacheeToOrganization = async (
  organizationId: string,
  userId: string
): Promise<OrganizationMember> => {
  const response = await axiosInstance.post<OrganizationMember>(
    `/organizations/${organizationId}/coachees`,
    { userId }
  );
  return response.data;
};

// Remove coach from organization
export const removeCoachFromOrganization = async (
  organizationId: string,
  userId: string
): Promise<void> => {
  await axiosInstance.delete(
    `/organizations/${organizationId}/coaches/${userId}`
  );
};

// Remove coachee from organization
export const removeCoacheeFromOrganization = async (
  organizationId: string,
  userId: string
): Promise<void> => {
  await axiosInstance.delete(
    `/organizations/${organizationId}/coachees/${userId}`
  );
};

// Get coaches in organization
export const getCoachesInOrganization = async (
  organizationId: string
): Promise<User[]> => {
  const response = await axiosInstance.get<User[]>(
    `/organizations/${organizationId}/coaches`
  );
  return response.data;
};

// Get coachees in organization
export const getCoacheesInOrganization = async (
  organizationId: string
): Promise<User[]> => {
  const response = await axiosInstance.get<User[]>(
    `/organizations/${organizationId}/coachees`
  );
  return response.data;
};

// Get organization statistics
export const getOrganizationStats = async (
  organizationId: string
): Promise<OrganizationStats> => {
  const response = await axiosInstance.get<OrganizationStats>(
    `/organizations/${organizationId}/stats`
  );
  return response.data;
};

// Get organization assignments
export const getOrganizationAssignments = async (
  organizationId: string
): Promise<any[]> => {
  const response = await axiosInstance.get<any[]>(
    `/organizations/${organizationId}/assignments`
  );
  return response.data;
};

// Get available users (not in organization)
export const getAvailableUsers = async (
  organizationId: string,
  role?: "COACH" | "COACHEE"
): Promise<User[]> => {
  const params = new URLSearchParams();
  if (role) {
    params.append("role", role);
  }
  
  const response = await axiosInstance.get<User[]>(
    `/organizations/${organizationId}/available-users?${params.toString()}`
  );
  return response.data;
};

// Bulk add users to organization
export const bulkAddUsersToOrganization = async (
  organizationId: string,
  userIds: string[],
  role: "COACH" | "COACHEE"
): Promise<OrganizationMember[]> => {
  const response = await axiosInstance.post<OrganizationMember[]>(
    `/organizations/${organizationId}/bulk-add`,
    { userIds, role }
  );
  return response.data;
};

// Get organization analytics
export const getOrganizationAnalytics = async (
  organizationId: string,
  timeframe?: "week" | "month" | "quarter" | "year"
): Promise<{
  assignmentTrends: Array<{
    date: string;
    completed: number;
    assigned: number;
  }>;
  coachPerformance: Array<{
    coachId: string;
    coachName: string;
    totalAssignments: number;
    completedAssignments: number;
    completionRate: number;
  }>;
  coacheeProgress: Array<{
    coacheeId: string;
    coacheeName: string;
    totalAssignments: number;
    completedAssignments: number;
    averageScore: number;
  }>;
}> => {
  const params = new URLSearchParams();
  if (timeframe) {
    params.append("timeframe", timeframe);
  }
  
  const response = await axiosInstance.get(
    `/organizations/${organizationId}/analytics?${params.toString()}`
  );
  return response.data;
};

// Export organization data
export const exportOrganizationData = async (
  organizationId: string,
  format: "csv" | "xlsx" | "pdf"
): Promise<Blob> => {
  const response = await axiosInstance.get(
    `/organizations/${organizationId}/export?format=${format}`,
    { responseType: "blob" }
  );
  return response.data;
};

// Get organization dashboard summary
export const getOrganizationDashboard = async (
  organizationId: string
): Promise<{
  overview: {
    totalCoaches: number;
    totalCoachees: number;
    totalAssignments: number;
    completionRate: number;
  };
  recentActivity: Array<{
    id: string;
    type: "assignment_created" | "assignment_completed" | "user_added";
    description: string;
    timestamp: string;
    user?: User;
  }>;
  upcomingDeadlines: Array<{
    assignmentId: string;
    exerciseName: string;
    coacheeName: string;
    dueDate: string;
    status: string;
  }>;
  topPerformers: Array<{
    userId: string;
    name: string;
    role: "COACH" | "COACHEE";
    metric: string;
    value: number;
  }>;
}> => {
  const response = await axiosInstance.get(
    `/organizations/${organizationId}/dashboard`
  );
  return response.data;
};
