import {
  CoacheeApiResponse,
  CoacheeDetailsApiResponse,
  User,
} from "../types/api/user.types";
import axiosInstance from "./axiosConfig";

export const getAllCoachees = async (): Promise<CoacheeApiResponse[]> => {
  const response = await axiosInstance.get<any[]>("/coach/coachees");
  return response.data;
};

export const addCoacheeByEmail = async (
  email: string
): Promise<Omit<User, "role">> => {
  const response = await axiosInstance.post<any>("/coach/coachee", { email });
  return response.data;
};

export const getCoacheeDetails = async (
  coacheeId: string
): Promise<CoacheeDetailsApiResponse> => {
  const response = await axiosInstance.get<CoacheeDetailsApiResponse>(
    `/coach/coachee/${coacheeId}/details`
  );
  return response.data;
};

export const updateCoachNotes = async (
  coacheeId: string,
  notes: string
): Promise<any> => {
  const response = await axiosInstance.put("/coach/coachee/notes", {
    coacheeId,
    notes,
  });
  return response.data;
};
