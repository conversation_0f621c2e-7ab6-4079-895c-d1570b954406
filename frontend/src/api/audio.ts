import axiosInstance from "./axiosConfig";

export interface TTSOptions {
  voice?: string;
  speed?: number;
  language?: string;
}

export interface AudioGenerationResponse {
  message: string;
  audioUrl: string;
  cached: boolean;
  visualization?: any;
  ttsOptions: TTSOptions;
}

export interface CustomAudioGenerationResponse {
  message: string;
  audioUrl: string;
  cached: boolean;
  text: string;
  ttsOptions: TTSOptions;
}

export interface AvailableVoicesResponse {
  voices: string[];
  defaultVoice: string;
  supportedLanguages: string[];
  speedRange: {
    min: number;
    max: number;
    default: number;
  };
}

/**
 * Generate audio for a visualization
 */
export const generateVisualizationAudio = async (
  visualizationId: string,
  options: TTSOptions = {}
): Promise<AudioGenerationResponse> => {
  const response = await axiosInstance.post<AudioGenerationResponse>(
    `/audio/visualizations/${visualizationId}/audio`,
    options
  );
  return response.data;
};

/**
 * Generate audio from custom text
 */
export const generateCustomAudio = async (
  text: string,
  options: TTSOptions = {}
): Promise<CustomAudioGenerationResponse> => {
  const response = await axiosInstance.post<CustomAudioGenerationResponse>(
    "/audio/generate",
    {
      text,
      ...options,
    }
  );
  return response.data;
};

/**
 * Delete audio for a visualization
 */
export const deleteVisualizationAudio = async (
  visualizationId: string
): Promise<void> => {
  await axiosInstance.delete(`/audio/visualizations/${visualizationId}/audio`);
};

/**
 * Get available TTS voices and options
 */
export const getAvailableVoices =
  async (): Promise<AvailableVoicesResponse> => {
    const response = await axiosInstance.get<AvailableVoicesResponse>(
      "/audio/voices"
    );
    return response.data;
  };

/**
 * Get a fresh audio URL for a visualization (to handle expired signed URLs)
 */
export const getVisualizationAudioUrl = async (
  visualizationId: string
): Promise<{ audioUrl: string; expiresIn: number; message: string }> => {
  const response = await axiosInstance.get<{
    audioUrl: string;
    expiresIn: number;
    message: string;
  }>(`/audio/visualizations/${visualizationId}/audio-url`);
  return response.data;
};
