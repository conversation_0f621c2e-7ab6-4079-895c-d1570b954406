import { RegisterData, LoginData, AuthResponse } from "../types/api/auth.types";
import axiosInstance from "./axiosConfig";

export const register = async (data: RegisterData): Promise<AuthResponse> => {
  const response = await axiosInstance.post<AuthResponse>(
    "/auth/register",
    data
  );
  localStorage.setItem("token", response.data.token);
  return response.data;
};

export const login = async (data: LoginData): Promise<AuthResponse> => {
  const response = await axiosInstance.post<AuthResponse>("/auth/login", data);
  localStorage.setItem("token", response.data.token);
  return response.data;
};

export const requestPasswordReset = async (email: string): Promise<void> => {
  await axiosInstance.post("/auth/request-reset", { email });
};

export const resetPassword = async (
  token: string,
  newPassword: string
): Promise<void> => {
  await axiosInstance.post("/auth/reset-password", { token, newPassword });
};
