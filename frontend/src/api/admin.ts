import { User, UserDetailsForAdminApiResponse } from "../types/api/user.types";
import axiosInstance from "./axiosConfig";

export const getUserForAdminById = async (
  id: string
): Promise<UserDetailsForAdminApiResponse> => {
  const response = await axiosInstance.get<UserDetailsForAdminApiResponse>(
    `admin/user/${id}`
  );
  if (response.status === 200 && response.data) {
    return response.data;
  }
  throw new Error(`User with ID ${id} not found`);
};
