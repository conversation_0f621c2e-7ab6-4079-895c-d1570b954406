import {
  CreateOrganizationData,
  Organization,
  OrganizationMember,
} from "../types/api/organizations.types";
import axiosInstance from "./axiosConfig";

export const getAllOrganizations = async (): Promise<Organization[]> => {
  const response = await axiosInstance.get<Organization[]>("/organizations");
  return response.data;
};

export const getOrganizationById = async (
  id: string
): Promise<Organization> => {
  const response = await axiosInstance.get<Organization>(
    `/organizations/${id}`
  );
  return response.data;
};

export const createOrganization = async (
  data: CreateOrganizationData
): Promise<Organization> => {
  const response = await axiosInstance.post<Organization>(
    "/organizations",
    data
  );
  return response.data;
};

export const updateOrganization = async (
  id: string,
  data: Partial<CreateOrganizationData>
): Promise<Organization> => {
  const response = await axiosInstance.put<Organization>(
    `/organizations/${id}`,
    data
  );
  return response.data;
};

export const deleteOrganization = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/organizations/${id}`);
};

export const getOrganizationMembers = async (
  id: string
): Promise<OrganizationMember[]> => {
  const response = await axiosInstance.get<OrganizationMember[]>(
    `/organizations/${id}/members`
  );
  return response.data;
};

export const addOrganizationMember = async (
  organizationId: string,
  userId: string,
  role: "COACH" | "COACHEE"
): Promise<void> => {
  await axiosInstance.post(`/organizations/${organizationId}/members`, {
    userId,
    role,
  });
};

export const removeOrganizationMember = async (
  organizationId: string,
  userId: string
): Promise<void> => {
  await axiosInstance.delete(
    `/organizations/${organizationId}/members/${userId}`
  );
};
