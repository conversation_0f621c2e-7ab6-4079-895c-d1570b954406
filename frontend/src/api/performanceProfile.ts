import axiosInstance from "./axiosConfig";
import {
  PerformanceProfile,
  CreatePerformanceProfileData,
  UpdatePerformanceGoalsData,
  LatestActiveGoalsResponse,
} from "../types/api/performanceProfile.types";

export const createPerformanceProfile = async (
  data: CreatePerformanceProfileData
): Promise<PerformanceProfile> => {
  const response = await axiosInstance.post<PerformanceProfile>(
    "/performance-profiles",
    data
  );
  return response.data;
};

export const getPerformanceProfiles = async (
  coacheeId?: string,
  limit?: number,
  offset?: number
): Promise<PerformanceProfile[]> => {
  const params = new URLSearchParams();
  if (coacheeId) params.append("coacheeId", coacheeId);
  if (limit) params.append("limit", limit.toString());
  if (offset) params.append("offset", offset.toString());

  const response = await axiosInstance.get<PerformanceProfile[]>(
    `/performance-profiles?${params.toString()}`
  );
  return response.data;
};

export const getPerformanceProfileById = async (
  id: string
): Promise<PerformanceProfile> => {
  const response = await axiosInstance.get<PerformanceProfile>(
    `/performance-profiles/${id}`
  );
  return response.data;
};

export const updatePerformanceGoals = async (
  profileId: string,
  data: UpdatePerformanceGoalsData
): Promise<PerformanceProfile> => {
  const response = await axiosInstance.put<PerformanceProfile>(
    `/performance-profiles/${profileId}/goals`,
    data
  );
  return response.data;
};

export const getLatestActiveGoals = async (
  coacheeId?: string
): Promise<LatestActiveGoalsResponse | null> => {
  const params = new URLSearchParams();
  if (coacheeId) params.append("coacheeId", coacheeId);

  const response = await axiosInstance.get<LatestActiveGoalsResponse>(
    `/performance-profiles/latest-goals?${params.toString()}`
  );
  return response.data;
};

export const deletePerformanceProfile = async (
  id: string
): Promise<{ message: string }> => {
  const response = await axiosInstance.delete<{ message: string }>(
    `/performance-profiles/${id}`
  );
  return response.data;
};
