import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { AuthProvider, useAuth } from "./context/AuthContext";
import Login from "./pages/Login";
import Register from "./pages/Register";
import CoachDashboard from "./pages/CoachDashboard";
import AdminDashboard from "./pages/AdminDashboard";
import Layout from "./components/Layout";
import HRDashboard from "./pages/HRDashboard";
import CoacheeDashboard from "./pages/CoacheeDashboard";

function AppRoutes() {
  const { user } = useAuth();

  if (user === undefined) {
    return <div>Loading...</div>;
  }

  return (
    <Routes>
      {!user ? (
        <>
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </>
      ) : (
        <Route element={<Layout />}>
          {user?.role === "COACH" && (
            <Route path="/dashboard/*" element={<CoachDashboard />} />
          )}
          {user?.role === "COACHEE" && (
            <Route path="/dashboard/*" element={<CoacheeDashboard />} />
          )}
          {user?.role === "HR_ADMIN" && (
            <Route path="/dashboard/*" element={<HRDashboard />} />
          )}
          {user?.role === "ADMIN" && (
            <Route path="/dashboard/*" element={<AdminDashboard />} />
          )}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Route>
      )}
    </Routes>
  );
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppRoutes />
      </Router>
    </AuthProvider>
  );
}

export default App;
