@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Audio Player Styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #2563eb;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #2563eb;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider:disabled::-webkit-slider-thumb {
  background: #9ca3af;
  cursor: not-allowed;
}

.slider:disabled::-moz-range-thumb {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Rich Text Editor Styles */
.ql-toolbar {
  border-top: 1px solid #d1d5db !important;
  border-left: 1px solid #d1d5db !important;
  border-right: 1px solid #d1d5db !important;
  border-bottom: none !important;
  border-top-left-radius: 0.375rem !important;
  border-top-right-radius: 0.375rem !important;
  background: #f9fafb;
}

.ql-container {
  border-bottom: 1px solid #d1d5db !important;
  border-left: 1px solid #d1d5db !important;
  border-right: 1px solid #d1d5db !important;
  border-top: none !important;
  border-bottom-left-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
  font-family: inherit;
}

.ql-editor {
  border-top: 1px solid #d1d5db !important;
  border-bottom-left-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
  background: #ffffff;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
}

.rich-text-editor-wrapper .ql-editor {
  min-height: var(--editor-height, 200px);
}

.ql-editor.ql-blank::before {
  font-style: normal !important;
  color: #9ca3af !important;
}

.ql-editor:focus {
  outline: none;
}

.ql-toolbar.ql-snow .ql-picker-label:hover,
.ql-toolbar.ql-snow .ql-picker-label.ql-active,
.ql-toolbar.ql-snow .ql-picker-item:hover,
.ql-toolbar.ql-snow .ql-picker-item.ql-selected {
  color: #2563eb;
}

.ql-toolbar.ql-snow button:hover,
.ql-toolbar.ql-snow button.ql-active {
  color: #2563eb;
}

.ql-toolbar.ql-snow button:hover .ql-stroke,
.ql-toolbar.ql-snow button.ql-active .ql-stroke {
  stroke: #2563eb;
}

.ql-toolbar.ql-snow button:hover .ql-fill,
.ql-toolbar.ql-snow button.ql-active .ql-fill {
  fill: #2563eb;
}

/* Rich Text Display Styles */
.rich-text-display {
  line-height: 1.6;
}

.rich-text-display h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  margin-top: 1rem;
}

.rich-text-display h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 0.75rem;
}

.rich-text-display h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 0.75rem;
}

.rich-text-display p {
  margin-bottom: 0.75rem;
}

.rich-text-display strong {
  font-weight: 600;
}

.rich-text-display em {
  font-style: italic;
}

.rich-text-display u {
  text-decoration: underline;
}

.rich-text-display s {
  text-decoration: line-through;
}

.rich-text-display ol,
.rich-text-display ul {
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
}

.rich-text-display ol {
  list-style-type: decimal;
}

.rich-text-display ul {
  list-style-type: disc;
}

.rich-text-display li {
  margin-bottom: 0.25rem;
}

.rich-text-display a {
  color: #2563eb;
  text-decoration: underline;
}

.rich-text-display a:hover {
  color: #1d4ed8;
}

.rich-text-display img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 0.75rem 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.rich-text-display img:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease-in-out;
}