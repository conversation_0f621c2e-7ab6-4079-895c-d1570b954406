import { Route, Routes } from "react-router";
import { LoadingSpinner } from "../components/ui/LoadingSpinner";
import React from "react";
import HRHome from "../components/hr/HRHome";
import HRSidebar from "../components/hr/HRSidebar";
import HRMembers from "../components/hr/HRMembers";
import HRAnalytics from "../components/hr/HRAnalytics";
import Container from "../components/ui/Container";
import { HRProvider } from "../context/HRContext";

export default function HRDashboard() {
  return (
    <HRProvider>
      <Container>
        <div className="flex flex-row h-full">
          <HRSidebar />
          <div className="w-full mx-auto overflow-y-auto px-4 sm:px-6 lg:px-8 py-8">
            <Routes>
              <Route
                path="/"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <HRHome />
                  </React.Suspense>
                }
              />
              <Route
                path="/members"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <HRMembers />
                  </React.Suspense>
                }
              />
              <Route
                path="/analytics"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <HRAnalytics />
                  </React.Suspense>
                }
              />
              <Route
                path="/settings"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <div className="text-center py-12">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Settings
                      </h3>
                      <p className="text-gray-500">
                        Settings feature coming soon!
                      </p>
                    </div>
                  </React.Suspense>
                }
              />
            </Routes>
          </div>
        </div>
      </Container>
    </HRProvider>
  );
}
