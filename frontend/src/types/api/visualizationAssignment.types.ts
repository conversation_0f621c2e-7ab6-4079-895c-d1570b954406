import { Visualization } from "./visualizations.types";

export type VisualizationAssignmentStatus =
  | "PENDING"
  | "VIEWED"
  | "COMPLETED"
  | "OVERDUE";

export interface VisualizationAssignment {
  id: string;
  visualizationId: string;
  coachId: string;
  coacheeId: string;
  status: VisualizationAssignmentStatus;
  dueDate?: string;
  visualization: Visualization;
  createdAt: string;
  updatedAt: string;
}

export interface CreateVisualizationAssignmentData {
  visualizationId: string;
  coacheeId: string;
  dueDate?: string;
}
