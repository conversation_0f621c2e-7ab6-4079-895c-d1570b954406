export enum PerformanceCategory {
  PHYSICAL = "PHYSICAL",
  TECHNICAL = "TECHNICAL",
  TACTICAL = "TACTICAL",
  MENTAL = "MENTAL",
}

export interface PerformanceProfileGoal {
  id: string;
  category: PerformanceCategory;
  goalName: string;
  currentRating: number;
  targetRating: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface PerformanceProfile {
  id: string;
  coacheeId: string;
  startDate: Date;
  targetDate: Date;
  createdAt: Date;
  updatedAt: Date;
  goals: PerformanceProfileGoal[];
  coachee: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreatePerformanceProfileData {
  startDate: string;
  targetDate: string;
  goals: {
    category: PerformanceCategory;
    goalName: string;
    currentRating: number;
    targetRating: number;
  }[];
}

export interface UpdatePerformanceGoalsData {
  goals: {
    id?: string; // Optional for new goals
    category?: PerformanceCategory; // Required for new goals
    goalName?: string;
    currentRating?: number;
    targetRating?: number;
    isActive?: boolean;
  }[];
}

export interface LatestActiveGoalsResponse {
  profileId: string;
  startDate: Date;
  targetDate: Date;
  goalsByCategory: Record<PerformanceCategory, PerformanceProfileGoal[]>;
}

export interface GoalFormData {
  category: PerformanceCategory;
  goalName: string;
  currentRating: number;
  targetRating: number;
}
