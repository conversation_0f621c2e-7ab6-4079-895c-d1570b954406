export type ActivityEventType =
  | "EXERCISE_ASSIGNED"
  | "EXERCISE_COMPLETED"
  | "VISUALIZATION_ASSIGNED"
  | "VISUALIZATION_COMPLETED"
  | "COACH_ASSIGNED"
  | "FEEDBACK_PROVIDED"
  | "EVALUATION_CREATED"
  | "EVALUATION_UPDATED"
  | "PERFORMANCE_PROFILE_CREATED"
  | "PERFORMANCE_PROFILE_UPDATED"
  | "PERFORMANCE_PROFILE_DELETED";

export interface ActivityLog {
  id: string;
  coachId?: string;
  coacheeId?: string;
  assignmentId?: string;
  visualizationAssignmentId?: string;
  eventType: ActivityEventType;
  eventMessage: string;
  timestamp: string;
  coach?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  coachee?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  assignment?: {
    id: string;
    exercise: {
      id: string;
      name: string;
    };
  };
  visualizationAssignment?: {
    id: string;
    visualization: {
      id: string;
      title: string;
    };
  };
}

export interface ActivityLogFilters {
  coacheeId?: string;
  coachId?: string;
  eventType?: ActivityEventType;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

export interface ActivityLogResponse {
  success: boolean;
  data: ActivityLog[];
  pagination?: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export interface ActivityStatsResponse {
  success: boolean;
  data: {
    totalActivities: number;
    exerciseAssignments: number;
    exerciseCompletions: number;
    visualizationAssignments: number;
    visualizationCompletions: number;
    coachAssignments: number;
    completionRate: number;
  };
}
