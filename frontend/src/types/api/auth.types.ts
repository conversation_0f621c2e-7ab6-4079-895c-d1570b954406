import { UserRole } from "./user.types";

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: UserRole;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: {
    id: string;
    email: string;
    role: UserRole;
    firstName: string;
    lastName: string;
    resetToken: string | null;
    resetTokenExpiry: string | null;
    createdAt: string;
    updatedAt: string;
  };
}
