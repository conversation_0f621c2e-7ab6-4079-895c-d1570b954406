export interface Visualization {
  id: string;
  title: string;
  description: string;
  audioUrl?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  creator?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  updater?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  assignments?: Array<{
    id: string;
    status: string;
    createdAt: string;
  }>;
}

export interface CreateVisualizationData {
  id?: string; // Optional for updates
  title: string;
  description: string;
  audioUrl?: string;
}
