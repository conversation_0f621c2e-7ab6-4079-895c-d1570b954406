import { AssignmentStatus } from "./assignments.types";

export type UserRole = "COACHEE" | "COACH" | "ADMIN" | "HR_ADMIN";

export type User = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
};

export type CoacheeApiResponse = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  organizationMemberships: {}[];
};

export type CoacheeDetailsApiResponse = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: Date;
  organizationMemberships: {
    organization: {
      id: string;
      name: string;
    };
  }[];
  coacheeRelationships: {
    coachNotes: string;
  }[];
  receivedAssignments: {
    id: string;
    dueDate: Date | null;
    status: AssignmentStatus;
    coachFeedback: string | null;
    createdAt: Date;
    aiSummary: string | null;
    feedbackAt: Date | null;
    exercise: {
      id: string;
      name: string;
      description: string;
    };
  }[];
  receivedVisAssignments: {
    id: string;
    dueDate: Date | null;
    status: AssignmentStatus;
    createdAt: Date;
    visualization: {
      id: string;
      title: string;
      description: string;
    };
  }[];
};

export type UserDetailsForAdminApiResponse = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  createdAt: Date;
  organizationMemberships: {
    organization: {
      id: string;
      name: string;
    };
  }[];
  coachRelationships: {
    coachee: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
    };
    coachNotes: string | null;
  }[];
  coacheeRelationships: {
    coach: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
    };
  }[];
};
