import { ReactNode } from "react";

// DataTable component types
export interface Column<T> {
  header: string;
  accessorKey: keyof T;
  cell?: (item: T) => ReactNode;
  align?: "left" | "center" | "right";
}

// Button component types
export type ButtonVariant = "primary" | "secondary" | "outline" | "danger";
export type ButtonSize = "sm" | "md" | "lg";

// Card component types
export interface CardProps {
  children: ReactNode;
  className?: string;
  header?: ReactNode;
  footer?: ReactNode;
}

// PageHeader component types
export interface PageHeaderProps {
  title: string;
  description?: string;
  actionButton?: {
    label: string;
    onClick: () => void;
  };
}

// ErrorMessage component types
export interface ErrorMessageProps {
  message: ReactNode;
  className?: string;
  onDismiss?: () => void;
}
