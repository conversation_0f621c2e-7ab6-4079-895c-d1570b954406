import {
  createContext,
  useContext,
  ReactNode,
  useEffect,
  useState,
} from "react";
import { User } from "../types/api/user.types";
import { login, register } from "../api/auth";
import { getUserData } from "../api/user";

interface AuthContextType {
  user: User | null | undefined;
  signIn: (email: string, password: string) => Promise<any>;
  signUp: (
    email: string,
    password: string,
    fullName: string,
    role: User["role"]
  ) => Promise<any>;
  signOut: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null | undefined>(undefined);

  useEffect(() => {
    fetchUserDetails();
  }, []);

  async function fetchUserDetails() {
    try {
      const response = await getUserData();
      if (response) {
        setUser(response);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
      setUser(null);
    }
  }

  async function signUp(
    email: string,
    password: string,
    fullName: string,
    role: User["role"]
  ) {
    try {
      const [firstName, ...lastNameParts] = fullName.split(" ");
      const lastName = lastNameParts.join(" ");

      const response = await register({
        email,
        password,
        firstName,
        lastName,
        role,
      });

      setUser(response.user);
      return response;
    } catch (error) {
      console.error("Registration error:", error);
      throw error;
    }
  }

  async function signIn(email: string, password: string) {
    try {
      const response = await login({ email, password });
      setUser(response.user);
      return response;
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  }

  function signOut() {
    localStorage.removeItem("token");
    setUser(null);
  }

  const value = {
    user,
    signIn,
    signUp,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
