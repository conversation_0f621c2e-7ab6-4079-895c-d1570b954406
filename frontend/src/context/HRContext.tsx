import { createContext, useContext, useState } from "react";
import { getAllOrganizations, getOrganizationById } from "../api/organizations";
import { getAllUsers } from "../api/user";
import { getAllAssignments } from "../api/assignments";
import { User } from "../types/api/user.types";
import { Assignment } from "../types/api/assignments.types";

export interface Organization {
  id: string;
  name: string;
  description?: string;
  hrAdminId: string;
  createdAt: string;
  updatedAt: string;
  coachMemberships: {
    coachId: string;
    coach: User;
  }[];
  coacheeMemberships: {
    coacheeId: string;
    coachee: User;
  }[];
}

export interface HRContextType {
  // Organization data (single organization for this HR admin)
  organization: Organization | null;

  // Users
  allUsers: User[];
  coaches: User[];
  coachees: User[];

  // Assignments and Analytics
  assignments: Assignment[];
  assignmentStats: {
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    overdue: number;
    completionRate: number;
  };

  // Loading states
  loading: {
    organization: boolean;
    users: boolean;
    assignments: boolean;
    analytics: boolean;
  };

  // Actions
  fetchMyOrganization: () => Promise<void>;
  fetchAllUsers: () => Promise<void>;
  fetchAssignments: () => Promise<void>;
  addCoach: (userId: string) => Promise<void>;
  addCoachee: (userId: string) => Promise<void>;
  removeCoach: (userId: string) => Promise<void>;
  removeCoachee: (userId: string) => Promise<void>;
  refreshData: () => Promise<void>;
}

const HRContext = createContext<HRContextType | undefined>(undefined);

export function HRProvider({ children }: { children: React.ReactNode }) {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [coaches, setCoaches] = useState<User[]>([]);
  const [coachees, setCoachees] = useState<User[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [assignmentStats, setAssignmentStats] = useState({
    total: 0,
    pending: 0,
    inProgress: 0,
    completed: 0,
    overdue: 0,
    completionRate: 0,
  });

  const [loading, setLoading] = useState({
    organization: false,
    users: false,
    assignments: false,
    analytics: false,
  });

  const updateLoading = (key: keyof typeof loading, value: boolean) => {
    setLoading((prev) => ({ ...prev, [key]: value }));
  };

  async function fetchMyOrganization() {
    updateLoading("organization", true);
    try {
      // Fetch the organization for the current HR admin
      const response = await getAllOrganizations();
      const myOrg = response?.[0]; // HR admin manages only one organization
      setOrganization(myOrg || null);

      // Extract coaches and coachees from the organization
      if (myOrg) {
        setCoaches(myOrg.coachMemberships?.map((m) => m.coach) || []);
        setCoachees(myOrg.coacheeMemberships?.map((m) => m.coachee) || []);
      }
    } catch (error) {
      console.error("Error fetching organization:", error);
      setOrganization(null);
    } finally {
      updateLoading("organization", false);
    }
  }

  async function fetchAllUsers() {
    updateLoading("users", true);
    try {
      const response = await getAllUsers();
      setAllUsers(response || []);
    } catch (error) {
      console.error("Error fetching users:", error);
      setAllUsers([]);
    } finally {
      updateLoading("users", false);
    }
  }

  async function fetchAssignments() {
    updateLoading("assignments", true);
    updateLoading("analytics", true);
    try {
      const response = await getAllAssignments();

      // Filter assignments for organization members
      const orgCoachIds = coaches.map((c) => c.id);
      const orgCoacheeIds = coachees.map((c) => c.id);

      const filteredAssignments = response.filter(
        (assignment) =>
          orgCoachIds.includes(assignment.coachId) ||
          orgCoacheeIds.includes(assignment.coacheeId)
      );

      setAssignments(filteredAssignments);

      // Calculate statistics
      const total = filteredAssignments.length;
      const pending = filteredAssignments.filter(
        (a) => a.status === "PENDING"
      ).length;
      const inProgress = filteredAssignments.filter(
        (a) => a.status === "IN_PROGRESS"
      ).length;
      const completed = filteredAssignments.filter(
        (a) => a.status === "COMPLETED"
      ).length;
      const overdue = filteredAssignments.filter(
        (a) => a.status === "OVERDUE"
      ).length;
      const completionRate =
        total > 0 ? Math.round((completed / total) * 100) : 0;

      setAssignmentStats({
        total,
        pending,
        inProgress,
        completed,
        overdue,
        completionRate,
      });
    } catch (error) {
      console.error("Error fetching assignments:", error);
      setAssignments([]);
    } finally {
      updateLoading("assignments", false);
      updateLoading("analytics", false);
    }
  }

  async function addCoach(userId: string) {
    if (!organization) return;
    try {
      // TODO: Implement API call
      console.log("Adding coach to organization:", {
        organizationId: organization.id,
        userId,
      });
      // Refresh organization data after adding
      await fetchMyOrganization();
    } catch (error) {
      console.error("Error adding coach:", error);
      throw error;
    }
  }

  async function addCoachee(userId: string) {
    if (!organization) return;
    try {
      // TODO: Implement API call
      console.log("Adding coachee to organization:", {
        organizationId: organization.id,
        userId,
      });
      // Refresh organization data after adding
      await fetchMyOrganization();
    } catch (error) {
      console.error("Error adding coachee:", error);
      throw error;
    }
  }

  async function removeCoach(userId: string) {
    if (!organization) return;
    try {
      // TODO: Implement API call
      console.log("Removing coach from organization:", {
        organizationId: organization.id,
        userId,
      });
      // Refresh organization data after removing
      await fetchMyOrganization();
    } catch (error) {
      console.error("Error removing coach:", error);
      throw error;
    }
  }

  async function removeCoachee(userId: string) {
    if (!organization) return;
    try {
      // TODO: Implement API call
      console.log("Removing coachee from organization:", {
        organizationId: organization.id,
        userId,
      });
      // Refresh organization data after removing
      await fetchMyOrganization();
    } catch (error) {
      console.error("Error removing coachee:", error);
      throw error;
    }
  }

  async function refreshData() {
    await Promise.all([
      fetchMyOrganization(),
      fetchAllUsers(),
      fetchAssignments(),
    ]);
  }

  return (
    <HRContext.Provider
      value={{
        organization,
        allUsers,
        coaches,
        coachees,
        assignments,
        assignmentStats,
        loading,
        fetchMyOrganization,
        fetchAllUsers,
        fetchAssignments,
        addCoach,
        addCoachee,
        removeCoach,
        removeCoachee,
        refreshData,
      }}
    >
      {children}
    </HRContext.Provider>
  );
}

export function useHR() {
  const context = useContext(HRContext);
  if (context === undefined) {
    throw new Error("useHR must be used within an HRProvider");
  }
  return context;
}
