import { createContext, useContext, useState } from "react";
import {
  getAllCoachees,
  addCoacheeByEmail,
  getCoacheeDetails,
  updateCoachNotes,
} from "../api/coach";
import { getAllExercises } from "../api/exercises";
import { createAssignment, getAllAssignments } from "../api/assignments";
import { getAllUsers } from "../api/user";
import {
  getAllVisualizations,
  createVisualization,
  updateVisualization,
} from "../api/visualizations";
import {
  getAllVisualizationAssignments,
  createVisualizationAssignment,
} from "../api/visualizationAssignments";
import {
  getAllEvaluations,
  getEvaluationTrends,
  getLatestEvaluation,
  createEvaluation,
  updateEvaluation,
  deleteEvaluation,
  Evaluation,
  EvaluationTrend,
  CreateEvaluationData,
  UpdateEvaluationData,
} from "../api/evaluations";
import {
  CoacheeApiResponse,
  CoacheeDetailsApiResponse,
  User,
} from "../types/api/user.types";
import { Exercise } from "../types/api/exercises.types";
import {
  Assignment,
  CreateAssignmentData,
} from "../types/api/assignments.types";
import {
  Visualization,
  CreateVisualizationData,
} from "../types/api/visualizations.types";
import {
  VisualizationAssignment,
  CreateVisualizationAssignmentData,
} from "../types/api/visualizationAssignment.types";

export interface CoachContextType {
  // Coachees
  myCoachees: CoacheeApiResponse[];
  coacheeDetails: CoacheeDetailsApiResponse | null;
  availableUsers: User[];

  // Exercises and Assignments
  allExercises: Exercise[];
  myAssignments: Assignment[];

  // Visualizations and Visualization Assignments
  allVisualizations: Visualization[];
  myVisualizationAssignments: VisualizationAssignment[];

  // Evaluations
  myEvaluations: Evaluation[];
  coacheeEvaluationTrends: EvaluationTrend[];
  latestCoacheeEvaluation: Evaluation | null;

  // Loading states
  loading: {
    coachees: boolean;
    exercises: boolean;
    assignments: boolean;
    users: boolean;
    visualizations: boolean;
    visualizationAssignments: boolean;
    evaluations: boolean;
  };

  // Actions
  fetchMyCoachees: () => Promise<void>;
  fetchCoacheeDetails: (coacheeId: string) => Promise<void>;
  fetchAvailableUsers: () => Promise<void>;
  assignCoacheeByEmail: (email: string) => Promise<void>;
  updateCoacheeNotes: (coacheeId: string, notes: string) => Promise<void>;

  fetchAllExercises: () => Promise<void>;
  fetchMyAssignments: () => Promise<void>;
  createNewAssignment: (data: CreateAssignmentData) => Promise<void>;

  fetchAllVisualizations: () => Promise<void>;
  fetchMyVisualizationAssignments: () => Promise<void>;
  createNewVisualizationAssignment: (
    data: CreateVisualizationAssignmentData
  ) => Promise<void>;
  createNewVisualization: (
    data: CreateVisualizationData
  ) => Promise<Visualization>;
  updateExistingVisualization: (
    id: string,
    data: CreateVisualizationData
  ) => Promise<Visualization>;

  fetchMyEvaluations: () => Promise<void>;
  fetchCoacheeEvaluationTrends: (coacheeId: string) => Promise<void>;
  fetchLatestCoacheeEvaluation: (coacheeId: string) => Promise<void>;
  createNewEvaluation: (data: CreateEvaluationData) => Promise<void>;
  updateExistingEvaluation: (
    id: string,
    data: UpdateEvaluationData
  ) => Promise<void>;
  deleteExistingEvaluation: (id: string) => Promise<void>;
}

const CoachContext = createContext<CoachContextType | undefined>(undefined);

export function CoachProvider({ children }: { children: React.ReactNode }) {
  const [myCoachees, setMyCoachees] = useState<CoacheeApiResponse[]>([]);
  const [coacheeDetails, setCoacheeDetails] =
    useState<CoacheeDetailsApiResponse | null>(null);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [allExercises, setAllExercises] = useState<Exercise[]>([]);
  const [myAssignments, setMyAssignments] = useState<Assignment[]>([]);
  const [allVisualizations, setAllVisualizations] = useState<Visualization[]>(
    []
  );
  const [myVisualizationAssignments, setMyVisualizationAssignments] = useState<
    VisualizationAssignment[]
  >([]);
  const [myEvaluations, setMyEvaluations] = useState<Evaluation[]>([]);
  const [coacheeEvaluationTrends, setCoacheeEvaluationTrends] = useState<
    EvaluationTrend[]
  >([]);
  const [latestCoacheeEvaluation, setLatestCoacheeEvaluation] =
    useState<Evaluation | null>(null);

  const [loading, setLoading] = useState({
    coachees: false,
    exercises: false,
    assignments: false,
    users: false,
    visualizations: false,
    visualizationAssignments: false,
    evaluations: false,
  });

  const updateLoading = (key: keyof typeof loading, value: boolean) => {
    setLoading((prev) => ({ ...prev, [key]: value }));
  };

  async function fetchMyCoachees() {
    updateLoading("coachees", true);
    try {
      const response = await getAllCoachees();
      setMyCoachees(response || []);
    } catch (error) {
      console.error("Error fetching coachees:", error);
      setMyCoachees([]);
    } finally {
      updateLoading("coachees", false);
    }
  }

  async function fetchCoacheeDetails(coacheeId: string) {
    try {
      const response = await getCoacheeDetails(coacheeId);
      setCoacheeDetails(response);
    } catch (error) {
      console.error(`Error fetching coachee details for ${coacheeId}:`, error);
      setCoacheeDetails(null);
    }
  }

  async function fetchAvailableUsers() {
    updateLoading("users", true);
    try {
      const response = await getAllUsers();
      // Filter to only show coachees who are not already assigned
      const coacheeUsers =
        response?.filter((user) => user.role === "COACHEE") || [];
      setAvailableUsers(coacheeUsers);
    } catch (error) {
      console.error("Error fetching available users:", error);
      setAvailableUsers([]);
    } finally {
      updateLoading("users", false);
    }
  }

  async function assignCoacheeByEmail(email: string) {
    try {
      await addCoacheeByEmail(email);
      // Refresh the coachees list after assignment
      await fetchMyCoachees();
    } catch (error) {
      console.error("Error assigning coachee:", error);
      throw error; // Re-throw to handle in component
    }
  }

  async function updateCoacheeNotes(coacheeId: string, notes: string) {
    try {
      await updateCoachNotes(coacheeId, notes);
    } catch (error) {
      console.error("Error updating coachee notes:", error);
      throw error; // Re-throw to handle in component
    }
  }

  async function fetchAllExercises() {
    updateLoading("exercises", true);
    try {
      const response = await getAllExercises();
      setAllExercises(response || []);
    } catch (error) {
      console.error("Error fetching exercises:", error);
      setAllExercises([]);
    } finally {
      updateLoading("exercises", false);
    }
  }

  async function fetchMyAssignments() {
    updateLoading("assignments", true);
    try {
      const response = await getAllAssignments();
      setMyAssignments(response || []);
    } catch (error) {
      console.error("Error fetching assignments:", error);
      setMyAssignments([]);
    } finally {
      updateLoading("assignments", false);
    }
  }

  async function createNewAssignment(data: CreateAssignmentData) {
    try {
      await createAssignment(data);
      // Refresh assignments after creation
      await fetchMyAssignments();
    } catch (error) {
      console.error("Error creating assignment:", error);
      throw error; // Re-throw to handle in component
    }
  }

  async function fetchAllVisualizations() {
    updateLoading("visualizations", true);
    try {
      const response = await getAllVisualizations();
      setAllVisualizations(response || []);
    } catch (error) {
      console.error("Error fetching visualizations:", error);
      setAllVisualizations([]);
    } finally {
      updateLoading("visualizations", false);
    }
  }

  async function fetchMyVisualizationAssignments() {
    updateLoading("visualizationAssignments", true);
    try {
      const response = await getAllVisualizationAssignments();
      setMyVisualizationAssignments(response || []);
    } catch (error) {
      console.error("Error fetching visualization assignments:", error);
      setMyVisualizationAssignments([]);
    } finally {
      updateLoading("visualizationAssignments", false);
    }
  }

  async function createNewVisualizationAssignment(
    data: CreateVisualizationAssignmentData
  ) {
    try {
      await createVisualizationAssignment(data);
      // Refresh visualization assignments after creation
      await fetchMyVisualizationAssignments();
    } catch (error) {
      console.error("Error creating visualization assignment:", error);
      throw error; // Re-throw to handle in component
    }
  }

  async function createNewVisualization(
    data: CreateVisualizationData
  ): Promise<Visualization> {
    try {
      const newVisualization = await createVisualization(data);
      // Refresh visualizations after creation
      await fetchAllVisualizations();
      return newVisualization;
    } catch (error) {
      console.error("Error creating visualization:", error);
      throw error; // Re-throw to handle in component
    }
  }

  async function updateExistingVisualization(
    id: string,
    data: CreateVisualizationData
  ): Promise<Visualization> {
    try {
      const updatedVisualization = await updateVisualization(id, data);
      // Refresh visualizations after update
      await fetchAllVisualizations();
      return updatedVisualization;
    } catch (error) {
      console.error("Error updating visualization:", error);
      throw error; // Re-throw to handle in component
    }
  }

  async function fetchMyEvaluations() {
    updateLoading("evaluations", true);
    try {
      const response = await getAllEvaluations();
      setMyEvaluations(response.evaluations || []);
    } catch (error) {
      console.error("Error fetching evaluations:", error);
      setMyEvaluations([]);
    } finally {
      updateLoading("evaluations", false);
    }
  }

  async function fetchCoacheeEvaluationTrends(coacheeId: string) {
    try {
      const response = await getEvaluationTrends(coacheeId);
      setCoacheeEvaluationTrends(response || []);
    } catch (error) {
      console.error("Error fetching evaluation trends:", error);
      setCoacheeEvaluationTrends([]);
    }
  }

  async function fetchLatestCoacheeEvaluation(coacheeId: string) {
    try {
      const response = await getLatestEvaluation(coacheeId);
      setLatestCoacheeEvaluation(response);
    } catch (error) {
      console.error("Error fetching latest evaluation:", error);
      setLatestCoacheeEvaluation(null);
    }
  }

  async function createNewEvaluation(data: CreateEvaluationData) {
    try {
      await createEvaluation(data);
      // Refresh evaluations after creation
      await fetchMyEvaluations();
    } catch (error) {
      console.error("Error creating evaluation:", error);
      throw error; // Re-throw to handle in component
    }
  }

  async function updateExistingEvaluation(
    id: string,
    data: UpdateEvaluationData
  ) {
    try {
      await updateEvaluation(id, data);
      // Refresh evaluations after update
      await fetchMyEvaluations();
    } catch (error) {
      console.error("Error updating evaluation:", error);
      throw error; // Re-throw to handle in component
    }
  }

  async function deleteExistingEvaluation(id: string) {
    try {
      await deleteEvaluation(id);
      // Refresh evaluations after deletion
      await fetchMyEvaluations();
    } catch (error) {
      console.error("Error deleting evaluation:", error);
      throw error; // Re-throw to handle in component
    }
  }

  return (
    <CoachContext.Provider
      value={{
        myCoachees,
        coacheeDetails,
        availableUsers,
        allExercises,
        myAssignments,
        allVisualizations,
        myVisualizationAssignments,
        myEvaluations,
        coacheeEvaluationTrends,
        latestCoacheeEvaluation,
        loading,
        fetchMyCoachees,
        fetchCoacheeDetails,
        fetchAvailableUsers,
        assignCoacheeByEmail,
        updateCoacheeNotes,
        fetchAllExercises,
        fetchMyAssignments,
        createNewAssignment,
        fetchAllVisualizations,
        fetchMyVisualizationAssignments,
        createNewVisualizationAssignment,
        createNewVisualization,
        updateExistingVisualization,
        fetchMyEvaluations,
        fetchCoacheeEvaluationTrends,
        fetchLatestCoacheeEvaluation,
        createNewEvaluation,
        updateExistingEvaluation,
        deleteExistingEvaluation,
      }}
    >
      {children}
    </CoachContext.Provider>
  );
}

export function useCoach() {
  const context = useContext(CoachContext);
  if (context === undefined) {
    throw new Error("useCoach must be used within a CoachProvider");
  }
  return context;
}
