import { createContext, useContext, useState } from "react";
import { getAllAssignments, getAssignmentById, submitAnswers } from "../api/assignments";
import { Assignment, SubmitAnswersData } from "../types/api/assignments.types";

export interface CoacheeContextType {
  // Assignments
  myAssignments: Assignment[];
  currentAssignment: Assignment | null;
  
  // Loading states
  loading: {
    assignments: boolean;
    assignment: boolean;
    submitting: boolean;
  };
  
  // Actions
  fetchMyAssignments: () => Promise<void>;
  fetchAssignmentById: (id: string) => Promise<void>;
  submitAssignment: (id: string, data: SubmitAnswersData) => Promise<void>;
  clearCurrentAssignment: () => void;
}

const CoacheeContext = createContext<CoacheeContextType | undefined>(undefined);

export function CoacheeProvider({ children }: { children: React.ReactNode }) {
  const [myAssignments, setMyAssignments] = useState<Assignment[]>([]);
  const [currentAssignment, setCurrentAssignment] = useState<Assignment | null>(null);
  
  const [loading, setLoading] = useState({
    assignments: false,
    assignment: false,
    submitting: false,
  });

  const updateLoading = (key: keyof typeof loading, value: boolean) => {
    setLoading(prev => ({ ...prev, [key]: value }));
  };

  async function fetchMyAssignments() {
    updateLoading('assignments', true);
    try {
      const response = await getAllAssignments();
      // Filter to only show assignments where the current user is the coachee
      setMyAssignments(response || []);
    } catch (error) {
      console.error("Error fetching assignments:", error);
      setMyAssignments([]);
    } finally {
      updateLoading('assignments', false);
    }
  }

  async function fetchAssignmentById(id: string) {
    updateLoading('assignment', true);
    try {
      const response = await getAssignmentById(id);
      setCurrentAssignment(response);
    } catch (error) {
      console.error(`Error fetching assignment ${id}:`, error);
      setCurrentAssignment(null);
    } finally {
      updateLoading('assignment', false);
    }
  }

  async function submitAssignment(id: string, data: SubmitAnswersData) {
    updateLoading('submitting', true);
    try {
      const response = await submitAnswers(id, data);
      // Update the assignment in the list
      setMyAssignments(prev => 
        prev.map(assignment => 
          assignment.id === id ? response : assignment
        )
      );
      // Update current assignment if it's the same one
      if (currentAssignment?.id === id) {
        setCurrentAssignment(response);
      }
    } catch (error) {
      console.error("Error submitting assignment:", error);
      throw error; // Re-throw to handle in component
    } finally {
      updateLoading('submitting', false);
    }
  }

  function clearCurrentAssignment() {
    setCurrentAssignment(null);
  }

  return (
    <CoacheeContext.Provider
      value={{
        myAssignments,
        currentAssignment,
        loading,
        fetchMyAssignments,
        fetchAssignmentById,
        submitAssignment,
        clearCurrentAssignment,
      }}
    >
      {children}
    </CoacheeContext.Provider>
  );
}

export function useCoachee() {
  const context = useContext(CoacheeContext);
  if (context === undefined) {
    throw new Error("useCoachee must be used within a CoacheeProvider");
  }
  return context;
}
