import {
  createContext,
  useContext,
  useState,
  useC<PERSON>back,
  useEffect,
} from "react";
import { getAllUsers } from "../api/user";
import { User, UserDetailsForAdminApiResponse } from "../types/api/user.types";
import {
  getAllExercises,
  createExercise as createExerciseApi,
  updateExercise,
  getExerciseById,
  deleteExercise,
} from "../api/exercises";
import { Exercise } from "../types/api/exercises.types";
import { Question } from "../types/exercise.types";
import {
  getAllVisualizations,
  deleteVisualization,
} from "../api/visualizations";
import { Visualization } from "../types/api/visualizations.types";
import { getUserForAdminById } from "../api/admin";
import { getAllOrganizations } from "../api/organizations";
import { Organization } from "../types/api/organizations.types";
import { getRecentActivity, getActivityStats } from "../api/activityLogs";
import { ActivityLog } from "../types/api/activityLog.types";
import {
  getAllEvaluations,
  getEvaluationTrends,
  Evaluation,
  EvaluationTrend,
} from "../api/evaluations";

interface DashboardStats {
  totalUsers: number;
  totalOrganizations: number;
  totalExercises: number;
  totalVisualizations: number;
  activeAssignments: number;
  monthlyAssignments: number;
  recentActivity: ActivityLog[];
  usersByRole: {
    admin: number;
    hrAdmin: number;
    coach: number;
    coachee: number;
  };
  activityStats: {
    totalActivities: number;
    exerciseAssignments: number;
    exerciseCompletions: number;
    visualizationAssignments: number;
    visualizationCompletions: number;
    coachAssignments: number;
    completionRate: number;
  };
}

export interface AdminContextType {
  allUsers: User[];
  userDetails: UserDetailsForAdminApiResponse | null;
  allOrganizations: Organization[];
  allExercises: Exercise[];
  allVisualizations: Visualization[];
  allEvaluations: Evaluation[];
  dashboardStats: DashboardStats | null;
  loading: {
    users: boolean;
    organizations: boolean;
    exercises: boolean;
    visualizations: boolean;
    evaluations: boolean;
    dashboard: boolean;
    userDetails: boolean;
  };
  error: string | null;
  fetchAllUsers: () => Promise<void>;
  fetchUserDetails: (id: string) => Promise<void>;
  fetchAllOrganizations: () => Promise<void>;
  fetchAllExercises: () => Promise<void>;
  fetchExerciseById: (id: string) => Promise<Exercise | null>;
  createExercise: (data: any) => Promise<void>;
  editExercise: (id: string, data: any) => Promise<void>;
  deleteExerciseById: (id: string) => Promise<void>;
  fetchAllVisualizations: () => Promise<void>;
  deleteVisualizationById: (id: string) => Promise<void>;
  fetchAllEvaluations: () => Promise<void>;
  getEvaluationTrendsForCoachee: (
    coacheeId: string
  ) => Promise<EvaluationTrend[]>;
  fetchDashboardStats: () => Promise<void>;
  clearError: () => void;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);
export function AdminProvider({ children }: { children: React.ReactNode }) {
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [userDetails, setUserDetails] =
    useState<UserDetailsForAdminApiResponse | null>(null);
  const [allExercises, setAllExercises] = useState<Exercise[]>([]);
  const [allVisualizations, setAllVisualizations] = useState<any[]>([]);
  const [allOrganizations, setAllOrganizations] = useState<any[]>([]);
  const [allEvaluations, setAllEvaluations] = useState<Evaluation[]>([]);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(
    null
  );
  const [loading, setLoading] = useState({
    users: false,
    organizations: false,
    exercises: false,
    visualizations: false,
    evaluations: false,
    dashboard: false,
    userDetails: false,
  });
  const [error, setError] = useState<string | null>(null);

  const updateLoading = (key: keyof typeof loading, value: boolean) => {
    setLoading((prev) => ({ ...prev, [key]: value }));
  };

  const clearError = () => {
    setError(null);
  };

  const fetchAllUsers = useCallback(async () => {
    updateLoading("users", true);
    setError(null);
    try {
      const response = await getAllUsers();
      if (response) {
        setAllUsers(response);
      } else {
        setAllUsers([]);
      }
    } catch (error: any) {
      console.error("Error fetching all users:", error);
      setError(error.response?.data?.message || "Failed to fetch users");
      setAllUsers([]);
    } finally {
      updateLoading("users", false);
    }
  }, []);

  async function fetchUserDetails(id: string) {
    updateLoading("userDetails", true);
    setError(null);
    try {
      const response = await getUserForAdminById(id);
      if (response) {
        setUserDetails(response);
      } else {
        console.warn(`No user found with ID: ${id}`);
        setUserDetails(null);
      }
    } catch (error: any) {
      console.error(`Error fetching user with ID ${id}:`, error);
      setError(error.response?.data?.message || "Failed to fetch user details");
      setUserDetails(null);
    } finally {
      updateLoading("userDetails", false);
    }
  }

  const fetchAllOrganizations = useCallback(async () => {
    updateLoading("organizations", true);
    setError(null);
    try {
      const response = await getAllOrganizations();
      if (response) {
        setAllOrganizations(response);
      } else {
        setAllOrganizations([]);
      }
    } catch (error: any) {
      console.error("Error fetching organizations:", error);
      setError(
        error.response?.data?.message || "Failed to fetch organizations"
      );
      setAllOrganizations([]);
    } finally {
      updateLoading("organizations", false);
    }
  }, []);

  const fetchAllExercises = useCallback(async () => {
    updateLoading("exercises", true);
    setError(null);
    try {
      const response = await getAllExercises();
      if (response) {
        setAllExercises(response);
      }
    } catch (error: any) {
      console.error("Error fetching all exercises:", error);
      setError(error.response?.data?.message || "Failed to fetch exercises");
      setAllExercises([]);
    } finally {
      updateLoading("exercises", false);
    }
  }, []);

  async function fetchExerciseById(id: string) {
    try {
      const response = await getExerciseById(id);
      if (response) {
        return response;
      } else {
        console.warn(`No exercise found with ID: ${id}`);
        return null;
      }
    } catch (error) {
      console.error(`Error fetching exercise with ID ${id}:`, error);
      return null; // Return null if there's an error
    }
  }

  async function createExercise(data: {
    id?: string;
    name: string;
    description: string;
    questions: Omit<Question, "id">[];
  }) {
    try {
      const newExercise = await createExerciseApi(data);
      console.log("New exercise created:", newExercise);
      fetchAllExercises(); // Refresh the list after creation
    } catch (error) {
      console.error("Error creating exercise:", error);
      throw error; // Re-throw to handle it in the component
    }
  }

  async function editExercise(
    id: string,
    data: {
      name?: string;
      description?: string;
      questions?: Omit<Question, "id">[];
    }
  ) {
    try {
      const updatedExercise = await updateExercise(id, data);
      console.log("Exercise updated:", updatedExercise);
      fetchAllExercises(); // Refresh the list after update
    } catch (error) {
      console.error("Error updating exercise:", error);
      throw error; // Re-throw to handle it in the component
    }
  }

  async function deleteExerciseById(id: string) {
    try {
      setError(null);
      await deleteExercise(id);
      // Refresh exercises after deletion
      await fetchAllExercises();
    } catch (error: any) {
      console.error("Error deleting exercise:", error);
      setError(error.response?.data?.message || "Failed to delete exercise");
      throw error;
    }
  }

  async function deleteVisualizationById(id: string) {
    try {
      setError(null);
      await deleteVisualization(id);
      // Refresh visualizations after deletion
      await fetchAllVisualizations();
    } catch (error: any) {
      console.error("Error deleting visualization:", error);
      setError(
        error.response?.data?.message || "Failed to delete visualization"
      );
      throw error;
    }
  }

  const fetchAllVisualizations = useCallback(async () => {
    updateLoading("visualizations", true);
    setError(null);
    try {
      const data = await getAllVisualizations();
      setAllVisualizations(data);
    } catch (error: any) {
      console.error("Error fetching visualizations:", error);
      setError(
        error.response?.data?.message || "Failed to fetch visualizations"
      );
    } finally {
      updateLoading("visualizations", false);
    }
  }, []);

  const fetchAllEvaluations = useCallback(async () => {
    updateLoading("evaluations", true);
    setError(null);
    try {
      const response = await getAllEvaluations();
      setAllEvaluations(response.evaluations || []);
    } catch (error: any) {
      console.error("Error fetching evaluations:", error);
      setError(error.response?.data?.message || "Failed to fetch evaluations");
      setAllEvaluations([]);
    } finally {
      updateLoading("evaluations", false);
    }
  }, []);

  const getEvaluationTrendsForCoachee = async (
    coacheeId: string
  ): Promise<EvaluationTrend[]> => {
    try {
      const trends = await getEvaluationTrends(coacheeId);
      return trends || [];
    } catch (error: any) {
      console.error("Error fetching evaluation trends:", error);
      return [];
    }
  };

  const fetchDashboardStats = useCallback(async () => {
    updateLoading("dashboard", true);
    setError(null);
    try {
      // Fetch all data needed for dashboard stats
      await Promise.all([
        fetchAllUsers(),
        fetchAllOrganizations(),
        fetchAllExercises(),
        fetchAllVisualizations(),
      ]);

      // Fetch activity data separately
      const [recentActivityResponse, activityStatsResponse] = await Promise.all(
        [getRecentActivity(10), getActivityStats()]
      );

      // Extract activity data
      const recentActivity = recentActivityResponse?.data || [];
      const activityStats = activityStatsResponse?.data || {
        totalActivities: 0,
        exerciseAssignments: 0,
        exerciseCompletions: 0,
        visualizationAssignments: 0,
        visualizationCompletions: 0,
        coachAssignments: 0,
        completionRate: 0,
      };

      // Update dashboard stats with activity data
      setDashboardStats((prevStats) => ({
        ...prevStats!,
        recentActivity,
        activityStats,
      }));
    } catch (error: any) {
      console.error("Error fetching dashboard stats:", error);
      setError(
        error.response?.data?.message || "Failed to fetch dashboard statistics"
      );
    } finally {
      updateLoading("dashboard", false);
    }
  }, [
    fetchAllUsers,
    fetchAllOrganizations,
    fetchAllExercises,
    fetchAllVisualizations,
  ]);

  // Calculate dashboard stats whenever data changes
  useEffect(() => {
    if (
      allUsers.length > 0 ||
      allOrganizations.length > 0 ||
      allExercises.length > 0 ||
      allVisualizations.length > 0
    ) {
      // Calculate stats from the fetched data
      const usersByRole = allUsers.reduce(
        (acc, user) => {
          switch (user.role) {
            case "ADMIN":
              acc.admin++;
              break;
            case "HR_ADMIN":
              acc.hrAdmin++;
              break;
            case "COACH":
              acc.coach++;
              break;
            case "COACHEE":
              acc.coachee++;
              break;
          }
          return acc;
        },
        { admin: 0, hrAdmin: 0, coach: 0, coachee: 0 }
      );

      const stats: DashboardStats = {
        totalUsers: allUsers.length,
        totalOrganizations: allOrganizations.length,
        totalExercises: allExercises.length,
        totalVisualizations: allVisualizations.length,
        activeAssignments: 45, // Mock data - would come from API
        monthlyAssignments: 123, // Mock data - would come from API
        recentActivity: [], // Will be populated by fetchDashboardStats
        usersByRole,
        activityStats: {
          totalActivities: 0,
          exerciseAssignments: 0,
          exerciseCompletions: 0,
          visualizationAssignments: 0,
          visualizationCompletions: 0,
          coachAssignments: 0,
          completionRate: 0,
        }, // Will be populated by fetchDashboardStats
      };

      setDashboardStats(stats);
    }
  }, [allUsers, allOrganizations, allExercises, allVisualizations]);

  return (
    <AdminContext.Provider
      value={{
        allUsers,
        userDetails,
        allOrganizations,
        allExercises,
        allVisualizations,
        allEvaluations,
        dashboardStats,
        loading,
        error,
        fetchAllUsers,
        fetchUserDetails,
        fetchAllOrganizations,
        fetchAllExercises,
        fetchExerciseById,
        createExercise,
        editExercise,
        deleteExerciseById,
        fetchAllVisualizations,
        deleteVisualizationById,
        fetchAllEvaluations,
        getEvaluationTrendsForCoachee,
        fetchDashboardStats,
        clearError,
      }}
    >
      {children}
    </AdminContext.Provider>
  );
}

export function useAdmin() {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error("useAdmin must be used within an AdminProvider");
  }
  return context;
}
