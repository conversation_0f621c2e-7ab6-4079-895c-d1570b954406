import {
  Home,
  Users,
  Building2,
  <PERSON><PERSON><PERSON>,
  Eye,
  BarChart3,
  Settings,
  LogOut,
  Activity,
  Calendar,
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";

const AdminSidebar = () => {
  const location = useLocation();
  const { signOut } = useAuth();

  const navigation = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: Home,
      current:
        location.pathname === "/dashboard" ||
        location.pathname === "/dashboard/",
    },
    {
      name: "Users",
      href: "/dashboard/users",
      icon: Users,
      current: location.pathname.startsWith("/dashboard/users"),
    },
    {
      name: "Organizations",
      href: "/dashboard/organizations",
      icon: Building2,
      current: location.pathname.startsWith("/dashboard/organizations"),
    },
    {
      name: "Exercises",
      href: "/dashboard/exercises",
      icon: BookOpen,
      current: location.pathname.startsWith("/dashboard/exercises"),
    },
    {
      name: "Visualizations",
      href: "/dashboard/visualizations",
      icon: Eye,
      current: location.pathname.startsWith("/dashboard/visualizations"),
    },
    {
      name: "Activity Logs",
      href: "/dashboard/activity-logs",
      icon: Activity,
      current: location.pathname.startsWith("/dashboard/activity-logs"),
    },
    {
      name: "Periodization",
      href: "/dashboard/periodization",
      icon: Calendar,
      current: location.pathname.startsWith("/dashboard/periodization"),
    },
    {
      name: "Analytics",
      href: "/dashboard/analytics",
      icon: BarChart3,
      current: location.pathname.startsWith("/dashboard/analytics"),
    },
    {
      name: "Settings",
      href: "/dashboard/settings",
      icon: Settings,
      current: location.pathname === "/dashboard/settings",
    },
  ];

  const handleLogout = () => {
    signOut();
  };

  return (
    <div className="flex flex-col w-64 bg-white border-r border-gray-200 h-full">
      {/* Logo */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-900">Admin Panel</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                item.current
                  ? "bg-blue-100 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
            >
              <Icon
                className={`mr-3 h-5 w-5 ${
                  item.current
                    ? "text-blue-500"
                    : "text-gray-400 group-hover:text-gray-500"
                }`}
              />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* User Actions */}
      <div className="px-4 py-4 border-t border-gray-200">
        <button
          onClick={handleLogout}
          className="group flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors"
        >
          <LogOut className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
          Sign Out
        </button>
      </div>
    </div>
  );
};

export default AdminSidebar;
