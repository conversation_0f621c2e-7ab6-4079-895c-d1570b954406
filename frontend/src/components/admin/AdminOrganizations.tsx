import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAdmin } from "../../context/AdminContext";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { DataTable } from "../ui/DataTable";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Button } from "../ui/Button";
import { Column } from "../../types/components.types";
import { Organization } from "../../types/api/organizations.types";
import {
  Building2,
  Users,
  UserCheck,
  Calendar,
  Eye,
  Edit,
  Trash,
} from "lucide-react";
import { deleteOrganization } from "../../api/organizations";

const AdminOrganizations = () => {
  const navigate = useNavigate();
  const { allOrganizations, loading, error, fetchAllOrganizations } =
    useAdmin();

  useEffect(() => {
    fetchAllOrganizations();
  }, [fetchAllOrganizations]);

  const handleDeleteOrganization = async (organizationId: string) => {
    if (window.confirm("Are you sure you want to delete this organization?")) {
      try {
        await deleteOrganization(organizationId);
        fetchAllOrganizations();
      } catch (error) {
        console.error("Error deleting organization:", error);
      }
    }
  };

  const columns: Column<Organization>[] = [
    {
      header: "Organization",
      accessorKey: "name",
      cell: (organization) => (
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
            <Building2 className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">
              {organization.name}
            </p>
            <p className="text-xs text-gray-500">
              {organization.coachMemberships.length +
                organization.coacheeMemberships.length}{" "}
              members
            </p>
          </div>
        </div>
      ),
    },
    {
      header: "Members",
      accessorKey: "id",
      cell: (organization) => (
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <UserCheck className="h-4 w-4 text-green-600 mr-1" />
            <span className="text-sm text-gray-900">
              {organization.coachMemberships?.length || 0} coaches
            </span>
          </div>
          <div className="flex items-center">
            <Users className="h-4 w-4 text-blue-600 mr-1" />
            <span className="text-sm text-gray-900">
              {organization.coacheeMemberships?.length || 0} coachees
            </span>
          </div>
        </div>
      ),
    },
    {
      header: "Created",
      accessorKey: "createdAt",
      cell: (organization) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm text-gray-500">
            {new Date(organization.createdAt).toLocaleDateString()}
          </span>
        </div>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (organization) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() =>
              navigate(`/dashboard/organizations/${organization.id}`)
            }
          >
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button size="sm" variant="outline">
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleDeleteOrganization(organization.id)}
            className="text-red-600 hover:text-red-800"
          >
            <Trash className="h-3 w-3 mr-1" />
            Delete
          </Button>
        </div>
      ),
    },
  ];

  if (loading.organizations) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Organizations"
        description="Manage organizations and their members"
        actionButton={{
          label: "Create Organization",
          onClick: () => navigate("/dashboard/organizations/create"),
        }}
      />

      {error && <ErrorMessage message={error} />}

      {/* Organization Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <Building2 className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Total Organizations
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allOrganizations.length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <UserCheck className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Coaches</p>
              <p className="text-lg font-semibold text-gray-900">
                {allOrganizations.reduce(
                  (sum, org) => sum + (org.coachMemberships?.length || 0),
                  0
                )}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-orange-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Total Coachees
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allOrganizations.reduce(
                  (sum, org) => sum + (org.coacheeMemberships?.length || 0),
                  0
                )}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Organizations Table */}
      <Card>
        <CardHeader title="All Organizations" />
        <div className="p-6 pt-0">
          <DataTable
            columns={columns}
            data={allOrganizations}
            className="w-full"
          />
        </div>
      </Card>
    </div>
  );
};

export default AdminOrganizations;
