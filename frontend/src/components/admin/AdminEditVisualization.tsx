import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";

import {
  getVisualizationById,
  updateVisualization,
} from "../../api/visualizations";
import {
  generateVisualizationAudio,
  getAvailableVoices,
  TTSOptions,
} from "../../api/audio";
import { ErrorMessage } from "../ui/ErrorMessage";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Visualization } from "../../types/api/visualizations.types";
import { Volume2 } from "lucide-react";
import { AudioPlayer } from "../ui/AudioPlayer";

const AdminEditVisualization = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [visualization, setVisualization] = useState<Visualization | null>(
    null
  );
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [audioUrl, setAudioUrl] = useState("");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Audio generation state
  const [audioGenerating, setAudioGenerating] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<string[]>([]);
  const [selectedVoice, setSelectedVoice] = useState("af_bella");
  const [selectedSpeed, setSelectedSpeed] = useState(1.0);
  const [selectedLanguage, setSelectedLanguage] = useState("en");
  const [supportedLanguages, setSupportedLanguages] = useState<string[]>([]);

  const [showAudioOptions, setShowAudioOptions] = useState(false);

  useEffect(() => {
    const fetchVisualization = async () => {
      if (!id) {
        setError("Visualization ID is required");
        setLoading(false);
        return;
      }

      try {
        const data = await getVisualizationById(id);
        setVisualization(data);
        setTitle(data.title);
        setDescription(data.description);
        setAudioUrl(data.audioUrl || "");
      } catch (error) {
        console.error("Error fetching visualization:", error);
        setError("Failed to load visualization");
      } finally {
        setLoading(false);
      }
    };

    const loadVoices = async () => {
      try {
        const voicesData = await getAvailableVoices();
        setAvailableVoices(voicesData.voices);
        setSupportedLanguages(voicesData.supportedLanguages);
        setSelectedVoice(voicesData.defaultVoice);
      } catch (error) {
        console.error("Error loading voices:", error);
      }
    };

    fetchVisualization();
    loadVoices();
  }, [id]);

  const handleUpdateVisualization = async () => {
    if (!id) {
      setError("Visualization ID is required");
      return;
    }

    if (!title.trim()) {
      setError("Title is required");
      return;
    }

    if (!description.trim()) {
      setError("Description is required");
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const updateData = {
        title: title.trim(),
        description: description.trim(),
        audioUrl: audioUrl.trim() || undefined,
      };

      await updateVisualization(id, updateData);

      // Navigate back to visualizations list
      navigate("/dashboard/visualizations");
    } catch (error) {
      console.error("Error updating visualization:", error);
      setError("Failed to update visualization. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate("/dashboard/visualizations");
  };

  const handleGenerateAudio = async () => {
    if (!id || !description.trim()) {
      setError("Description is required to generate audio");
      return;
    }

    setAudioGenerating(true);
    setError(null);

    try {
      const ttsOptions: TTSOptions = {
        voice: selectedVoice,
        speed: selectedSpeed,
        language: selectedLanguage,
      };

      const audioResponse = await generateVisualizationAudio(id, ttsOptions);
      setAudioUrl(audioResponse.audioUrl);
      setShowAudioOptions(false);

      // Show success message
      setError(null);
    } catch (error) {
      console.error("Error generating audio:", error);
      setError("Failed to generate audio. Please try again.");
    } finally {
      setAudioGenerating(false);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!visualization) {
    return (
      <div className="space-y-6">
        <PageHeader
          title="Edit Visualization"
          description="Visualization not found"
        />
        <ErrorMessage message="Visualization not found" />
        <Button onClick={handleCancel}>Back to Visualizations</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Edit Visualization"
        description={`Editing: ${visualization.title}`}
      />

      {error && <ErrorMessage message={error} />}

      <Card>
        <CardHeader title="Visualization Details" />
        <div className="p-6 pt-0 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ID
            </label>
            <input
              type="text"
              value={id}
              disabled
              className="w-full bg-gray-50 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
            />
            <p className="text-xs text-gray-500 mt-1">
              Visualization ID cannot be changed
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter visualization title"
              className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter visualization description or transcript"
              className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
              rows={6}
              required
            />
          </div>

          {/* Audio Management Section */}
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Audio Management
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAudioOptions(!showAudioOptions)}
                disabled={audioGenerating}
              >
                {audioGenerating ? "Generating..." : "Generate New Audio"}
              </Button>
            </div>

            {/* Current Audio Status */}
            {audioUrl ? (
              <div className="mb-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Volume2 className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    Audio Preview
                  </span>
                </div>
                <AudioPlayer
                  audioUrl={audioUrl}
                  title={title || "Visualization Audio"}
                  visualizationId={id}
                  onError={(error) => setError(error)}
                />
              </div>
            ) : (
              <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg mb-4">
                <p className="text-sm text-gray-600">
                  No audio file available. Generate audio from the description
                  or enter a URL manually.
                </p>
              </div>
            )}

            {/* Audio Generation Options */}
            {showAudioOptions && (
              <div className="space-y-4 bg-blue-50 p-4 rounded-lg mb-4">
                <h4 className="font-medium text-gray-900">
                  Generate Audio from Description
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Voice
                    </label>
                    <select
                      value={selectedVoice}
                      onChange={(e) => setSelectedVoice(e.target.value)}
                      className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
                    >
                      {availableVoices.map((voice) => (
                        <option key={voice} value={voice}>
                          {voice
                            .replace(/_/g, " ")
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Language
                    </label>
                    <select
                      value={selectedLanguage}
                      onChange={(e) => setSelectedLanguage(e.target.value)}
                      className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
                    >
                      {supportedLanguages.map((lang) => (
                        <option key={lang} value={lang}>
                          {lang.toUpperCase()}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Speed: {selectedSpeed}x
                    </label>
                    <input
                      type="range"
                      min="0.5"
                      max="2.0"
                      step="0.1"
                      value={selectedSpeed}
                      onChange={(e) =>
                        setSelectedSpeed(parseFloat(e.target.value))
                      }
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>0.5x</span>
                      <span>2.0x</span>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <Button
                    onClick={handleGenerateAudio}
                    disabled={audioGenerating || !description.trim()}
                    size="sm"
                  >
                    {audioGenerating ? "Generating..." : "Generate Audio"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowAudioOptions(false)}
                    size="sm"
                  >
                    Cancel
                  </Button>
                </div>

                {audioGenerating && (
                  <div className="flex items-center space-x-2 text-blue-600">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm">
                      Generating audio from description...
                    </span>
                  </div>
                )}
              </div>
            )}

            {/* Manual Audio URL Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Audio URL (Manual Entry)
              </label>
              <input
                type="url"
                value={audioUrl}
                onChange={(e) => setAudioUrl(e.target.value)}
                placeholder="https://example.com/audio.mp3"
                className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 px-2 py-1.5"
              />
              <p className="text-xs text-gray-500 mt-1">
                Manually enter the URL to an existing audio file, or use the
                generate button above
              </p>
            </div>
          </div>

          <div className="flex space-x-4 pt-4">
            <Button onClick={handleUpdateVisualization} disabled={saving}>
              {saving ? "Updating..." : "Update Visualization"}
            </Button>
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AdminEditVisualization;
