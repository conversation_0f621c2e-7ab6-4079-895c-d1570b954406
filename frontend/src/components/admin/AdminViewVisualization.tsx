import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Button } from "../ui/Button";
import { Visualization } from "../../types/api/visualizations.types";
import { getVisualizationById } from "../../api/visualizations";
import {
  Eye,
  Calendar,
  User,
  Edit,
  ArrowLeft,
  Volume2,
  FileText,
  ExternalLink,
} from "lucide-react";
import { AudioPlayer } from "../ui/AudioPlayer";

const AdminViewVisualization = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [visualization, setVisualization] = useState<Visualization | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVisualization = async () => {
      if (!id) {
        setError("Visualization ID is required");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const data = await getVisualizationById(id);
        setVisualization(data);
        setError(null);
      } catch (error: any) {
        console.error("Error fetching visualization:", error);
        setError(
          error.response?.data?.message || "Failed to load visualization"
        );
        setVisualization(null);
      } finally {
        setLoading(false);
      }
    };

    fetchVisualization();
  }, [id]);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!visualization) {
    return (
      <div className="space-y-6">
        <PageHeader
          title="Visualization Not Found"
          description="The requested visualization could not be found"
        />
        <Card className="p-6 text-center">
          <p className="text-gray-500">
            The visualization you're looking for doesn't exist or you don't have
            permission to view it.
          </p>
          <Button
            onClick={() => navigate("/dashboard/visualizations")}
            className="mt-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Visualizations
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={visualization.title}
        description="View visualization details and content"
        actionButton={{
          label: "Edit Visualization",
          onClick: () =>
            navigate(`/dashboard/visualizations/edit/${visualization.id}`),
        }}
      />

      {error && <ErrorMessage message={error} />}

      {/* Back Button */}
      <Button
        variant="outline"
        onClick={() => navigate("/dashboard/visualizations")}
        className="mb-4"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Visualizations
      </Button>

      {/* Visualization Overview */}
      <Card>
        <CardHeader title="Visualization Overview" />
        <div className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">
                Description
              </h3>
              <p className="text-gray-900">
                {visualization.description || "No description provided"}
              </p>
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Visualization ID
                </h3>
                <p className="text-gray-900 font-mono text-sm">
                  {visualization.id}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Created
                </h3>
                <div className="flex items-center text-gray-900">
                  <Calendar className="h-4 w-4 mr-2" />
                  {new Date(visualization.createdAt).toLocaleDateString(
                    "en-US",
                    {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    }
                  )}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Created By
                </h3>
                <div className="flex items-center text-gray-900">
                  <User className="h-4 w-4 mr-2" />
                  {visualization.creator
                    ? `${visualization.creator.firstName} ${visualization.creator.lastName} (${visualization.creator.email})`
                    : visualization.createdBy}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Visualization Content */}
      <Card>
        <CardHeader title="Content" />
        <div className="p-6 pt-0">
          <div className="space-y-6">
            {/* Text Content */}
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-3">
                Text Content
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 border">
                <div className="flex items-center mb-3">
                  <FileText className="h-5 w-5 text-gray-600 mr-2" />
                  <span className="font-medium text-gray-900">Description</span>
                </div>
                <p className="text-gray-700 leading-relaxed">
                  {visualization.description || "No text content provided"}
                </p>
              </div>
            </div>

            {/* Audio Content */}
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-3">
                Audio Content
              </h3>
              {visualization.audioUrl ? (
                <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <Volume2 className="h-5 w-5 text-green-600 mr-2" />
                      <span className="font-medium text-green-900">
                        Audio Available
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          window.open(visualization.audioUrl, "_blank")
                        }
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Open
                      </Button>
                    </div>
                  </div>

                  {/* Audio Player */}
                  <div className="mt-3">
                    <AudioPlayer
                      audioUrl={visualization.audioUrl}
                      title={visualization.title}
                      visualizationId={visualization.id}
                      onError={(error) =>
                        console.error("Audio playback error:", error)
                      }
                    />
                  </div>

                  {/* <div className="mt-2 text-sm text-green-700">
                    <p className="font-mono break-all">
                      {visualization.audioUrl}
                    </p>
                  </div> */}
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center text-gray-500">
                    <FileText className="h-5 w-5 mr-2" />
                    <span>
                      No audio content available - text only visualization
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Visualization Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Content Type</p>
              <p className="text-lg font-semibold text-gray-900">
                {visualization.audioUrl ? "Audio + Text" : "Text Only"}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Volume2 className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Audio Status</p>
              <p className="text-lg font-semibold text-gray-900">
                {visualization.audioUrl ? "Available" : "Not Available"}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Calendar className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Assignments</p>
              <p className="text-lg font-semibold text-gray-900">
                {visualization.assignments?.length || 0}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Assignments */}
      {visualization.assignments && visualization.assignments.length > 0 && (
        <Card>
          <CardHeader title="Recent Assignments" />
          <div className="p-6 pt-0">
            <div className="space-y-3">
              {visualization.assignments.slice(0, 5).map((assignment) => (
                <div
                  key={assignment.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          assignment.status === "COMPLETED"
                            ? "bg-green-500"
                            : assignment.status === "PENDING"
                            ? "bg-yellow-500"
                            : assignment.status === "IN_PROGRESS"
                            ? "bg-blue-500"
                            : "bg-gray-500"
                        }`}
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Assignment #{assignment.id.slice(-8)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(assignment.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span
                      className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
                        assignment.status === "COMPLETED"
                          ? "bg-green-100 text-green-800"
                          : assignment.status === "PENDING"
                          ? "bg-yellow-100 text-yellow-800"
                          : assignment.status === "IN_PROGRESS"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {assignment.status.replace("_", " ")}
                    </span>
                  </div>
                </div>
              ))}
            </div>
            {visualization.assignments.length > 5 && (
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-500">
                  Showing 5 of {visualization.assignments.length} assignments
                </p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="outline"
          onClick={() => navigate("/dashboard/visualizations")}
        >
          Back to Visualizations
        </Button>
        <Button
          onClick={() =>
            navigate(`/dashboard/visualizations/edit/${visualization.id}`)
          }
        >
          <Edit className="h-4 w-4 mr-2" />
          Edit Visualization
        </Button>
      </div>
    </div>
  );
};

export default AdminViewVisualization;
