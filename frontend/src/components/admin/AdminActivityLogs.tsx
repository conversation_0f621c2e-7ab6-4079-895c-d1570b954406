import { useEffect, useState } from "react";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Button } from "../ui/Button";
import { DataTable } from "../ui/DataTable";
import { Column } from "../../types/components.types";
import {
  ActivityLog,
  ActivityEventType,
} from "../../types/api/activityLog.types";
import { getActivityLogs } from "../../api/activityLogs";
import {
  Activity,
  Calendar,
  User,
  Users,
  BookOpen,
  Eye,
  MessageSquare,
  Filter,
  Download,
  RefreshCw,
} from "lucide-react";
import DateInput from "../ui/input/DateInput";
import SelectInput from "../ui/input/SelectInput";

const AdminActivityLogs = () => {
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    eventType: "" as ActivityEventType | "",
    startDate: "",
    endDate: "",
    limit: 50,
    offset: 0,
  });
  const [totalLogs, setTotalLogs] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  const fetchActivityLogs = async () => {
    try {
      setLoading(true);
      setError(null);

      const filterParams = {
        ...filters,
        eventType: filters.eventType || undefined,
        startDate: filters.startDate || undefined,
        endDate: filters.endDate || undefined,
      };

      const response = await getActivityLogs(filterParams);
      setActivityLogs(response.data);
      setTotalLogs(response.pagination?.total || response.data.length);
      setHasMore(response.pagination?.hasMore || false);
    } catch (error: any) {
      console.error("Error fetching activity logs:", error);
      setError(
        error.response?.data?.message || "Failed to fetch activity logs"
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActivityLogs();
  }, [filters]);

  const getEventTypeIcon = (eventType: ActivityEventType) => {
    switch (eventType) {
      case "EXERCISE_ASSIGNED":
        return <BookOpen className="h-4 w-4 text-blue-600" />;
      case "EXERCISE_COMPLETED":
        return <BookOpen className="h-4 w-4 text-green-600" />;
      case "VISUALIZATION_ASSIGNED":
        return <Eye className="h-4 w-4 text-purple-600" />;
      case "VISUALIZATION_COMPLETED":
        return <Eye className="h-4 w-4 text-green-600" />;
      case "COACH_ASSIGNED":
        return <Users className="h-4 w-4 text-orange-600" />;
      case "FEEDBACK_PROVIDED":
        return <MessageSquare className="h-4 w-4 text-indigo-600" />;
      case "EVALUATION_CREATED":
        return <Activity className="h-4 w-4 text-yellow-600" />;
      case "EVALUATION_UPDATED":
        return <Activity className="h-4 w-4 text-yellow-600" />;
      case "PERFORMANCE_PROFILE_CREATED":
        return <Activity className="h-4 w-4 text-indigo-600" />;
      case "PERFORMANCE_PROFILE_UPDATED":
        return <Activity className="h-4 w-4 text-indigo-600" />;
      case "PERFORMANCE_PROFILE_DELETED":
        return <Activity className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getEventTypeBadgeColor = (eventType: ActivityEventType) => {
    switch (eventType) {
      case "EXERCISE_ASSIGNED":
        return "bg-blue-100 text-blue-800";
      case "EXERCISE_COMPLETED":
        return "bg-green-100 text-green-800";
      case "VISUALIZATION_ASSIGNED":
        return "bg-purple-100 text-purple-800";
      case "VISUALIZATION_COMPLETED":
        return "bg-green-100 text-green-800";
      case "COACH_ASSIGNED":
        return "bg-orange-100 text-orange-800";
      case "FEEDBACK_PROVIDED":
        return "bg-indigo-100 text-indigo-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatEventType = (eventType: ActivityEventType) => {
    return eventType
      .replace(/_/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (l) => l.toUpperCase());
  };

  const handleFilterChange = (key: string, value: string | number) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      offset: 0, // Reset pagination when filters change
    }));
  };

  const handleLoadMore = () => {
    setFilters((prev) => ({
      ...prev,
      offset: prev.offset + prev.limit,
    }));
  };

  const handleRefresh = () => {
    fetchActivityLogs();
  };

  const handleClearFilters = () => {
    setFilters({
      eventType: "",
      startDate: "",
      endDate: "",
      limit: 50,
      offset: 0,
    });
  };

  const handleExport = () => {
    // Create CSV content
    const headers = ["Event Type", "Message", "Coach", "Coachee", "Timestamp"];
    const csvContent = [
      headers.join(","),
      ...activityLogs.map((log) =>
        [
          log.eventType,
          `"${log.eventMessage.replace(/"/g, '""')}"`,
          log.coach
            ? `"${log.coach.firstName} ${log.coach.lastName} (${log.coach.email})"`
            : "",
          log.coachee
            ? `"${log.coachee.firstName} ${log.coachee.lastName} (${log.coachee.email})"`
            : "",
          new Date(log.timestamp).toLocaleString(),
        ].join(",")
      ),
    ].join("\n");

    // Create and download file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `activity-logs-${new Date().toISOString().split("T")[0]}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const columns: Column<ActivityLog>[] = [
    {
      header: "Event",
      accessorKey: "eventType",
      cell: (log) => (
        <div className="flex items-center space-x-2">
          {getEventTypeIcon(log.eventType)}
          <span
            className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${getEventTypeBadgeColor(
              log.eventType
            )}`}
          >
            {formatEventType(log.eventType)}
          </span>
        </div>
      ),
    },
    {
      header: "Message",
      accessorKey: "eventMessage",
      cell: (log) => (
        <div className="max-w-md">
          <p className="text-sm text-gray-900 truncate">{log.eventMessage}</p>
        </div>
      ),
    },
    {
      header: "Coach",
      accessorKey: "coach",
      cell: (log) => (
        <div className="text-sm">
          {log.coach ? (
            <div>
              <p className="font-medium text-gray-900">
                {log.coach.firstName} {log.coach.lastName}
              </p>
              <p className="text-gray-500">{log.coach.email}</p>
            </div>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      header: "Coachee",
      accessorKey: "coachee",
      cell: (log) => (
        <div className="text-sm">
          {log.coachee ? (
            <div>
              <p className="font-medium text-gray-900">
                {log.coachee.firstName} {log.coachee.lastName}
              </p>
              <p className="text-gray-500">{log.coachee.email}</p>
            </div>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      header: "Timestamp",
      accessorKey: "timestamp",
      cell: (log) => (
        <div className="text-sm text-gray-500">
          <div className="flex items-center">
            <Calendar className="h-3 w-3 mr-1" />
            {new Date(log.timestamp).toLocaleDateString()}
          </div>
          <div className="text-xs text-gray-400">
            {new Date(log.timestamp).toLocaleTimeString()}
          </div>
        </div>
      ),
    },
  ];

  const eventTypeOptions = [
    { value: "", label: "All Events" },
    { value: "EXERCISE_ASSIGNED", label: "Exercise Assigned" },
    { value: "EXERCISE_COMPLETED", label: "Exercise Completed" },
    { value: "VISUALIZATION_ASSIGNED", label: "Visualization Assigned" },
    { value: "VISUALIZATION_COMPLETED", label: "Visualization Completed" },
    { value: "COACH_ASSIGNED", label: "Coach Assigned" },
    { value: "FEEDBACK_PROVIDED", label: "Feedback Provided" },
    { value: "EVALUATION_CREATED", label: "Evaluation Created" },
    { value: "EVALUATION_UPDATED", label: "Evaluation Updated" },
    {
      value: "PERFORMANCE_PROFILE_CREATED",
      label: "Performance Profile Created",
    },
    {
      value: "PERFORMANCE_PROFILE_UPDATED",
      label: "Performance Profile Updated",
    },
    {
      value: "PERFORMANCE_PROFILE_DELETED",
      label: "Performance Profile Deleted",
    },
  ];

  if (loading && activityLogs.length === 0) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Activity Logs"
        description="Monitor system activity and user interactions"
      />

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="outline"
          onClick={handleExport}
          disabled={activityLogs.length === 0}
        >
          <Download className="h-4 w-4 mr-2" />
          Export CSV
        </Button>
        <Button onClick={handleRefresh} disabled={loading}>
          <RefreshCw
            className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      {error && <ErrorMessage message={error} />}

      {/* Activity Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <Activity className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Total Activities
              </p>
              <p className="text-lg font-semibold text-gray-900">{totalLogs}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Exercise Events
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {
                  activityLogs.filter(
                    (log) =>
                      log.eventType === "EXERCISE_ASSIGNED" ||
                      log.eventType === "EXERCISE_COMPLETED"
                  ).length
                }
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Visualization Events
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {
                  activityLogs.filter(
                    (log) =>
                      log.eventType === "VISUALIZATION_ASSIGNED" ||
                      log.eventType === "VISUALIZATION_COMPLETED"
                  ).length
                }
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-orange-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Coach Events</p>
              <p className="text-lg font-semibold text-gray-900">
                {
                  activityLogs.filter(
                    (log) =>
                      log.eventType === "COACH_ASSIGNED" ||
                      log.eventType === "FEEDBACK_PROVIDED"
                  ).length
                }
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader
          title="Filters"
          action={
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilters}
              disabled={
                !filters.eventType && !filters.startDate && !filters.endDate
              }
            >
              Clear Filters
            </Button>
          }
        />
        <div className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <SelectInput
              options={eventTypeOptions}
              label="Event Type"
              value={filters.eventType}
              onChange={(value) => handleFilterChange("eventType", value)}
            />
            <DateInput
              label="Start Date"
              value={filters.startDate}
              onChange={(value) => handleFilterChange("startDate", value)}
            />

            <DateInput
              label="End Date"
              value={filters.endDate}
              onChange={(value) => handleFilterChange("endDate", value)}
            />
          </div>
        </div>
      </Card>

      {/* Activity Logs Table */}
      <Card>
        <CardHeader title="Activity Logs" />
        <div className="p-6 pt-0">
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              <DataTable
                columns={columns}
                data={activityLogs}
                className="w-full"
              />

              {/* Pagination Info and Load More */}
              <div className="mt-6 flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  Showing {activityLogs.length} of {totalLogs} activities
                  {filters.offset > 0 &&
                    ` (starting from ${filters.offset + 1})`}
                </div>
                {hasMore && (
                  <Button
                    variant="outline"
                    onClick={handleLoadMore}
                    disabled={loading}
                  >
                    {loading ? "Loading..." : "Load More"}
                  </Button>
                )}
              </div>

              {activityLogs.length === 0 && !loading && (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No activity logs found.</p>
                </div>
              )}
            </>
          )}
        </div>
      </Card>
    </div>
  );
};

export default AdminActivityLogs;
