import { LucideIcon, ArrowR<PERSON> } from "lucide-react";
import { Link } from "react-router-dom";

interface QuickActionCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  href: string;
  onClick?: () => void;
  disabled?: boolean;
}

const QuickActionCard = ({ 
  title, 
  description, 
  icon: Icon, 
  href, 
  onClick,
  disabled = false 
}: QuickActionCardProps) => {
  const content = (
    <div className={`flex items-center p-4 border border-gray-200 rounded-lg transition-all ${
      disabled 
        ? "opacity-50 cursor-not-allowed" 
        : "hover:bg-gray-50 hover:border-gray-300 hover:shadow-sm cursor-pointer"
    }`}>
      <div className="flex-shrink-0">
        <Icon className={`h-6 w-6 ${disabled ? "text-gray-400" : "text-blue-600"}`} />
      </div>
      <div className="ml-4 flex-1">
        <h3 className="text-sm font-medium text-gray-900">{title}</h3>
        <p className="text-sm text-gray-500">{description}</p>
      </div>
      <div className="flex-shrink-0">
        <ArrowRight className={`h-4 w-4 ${disabled ? "text-gray-400" : "text-gray-400 group-hover:text-gray-600"}`} />
      </div>
    </div>
  );

  if (disabled) {
    return <div className="group">{content}</div>;
  }

  if (onClick) {
    return (
      <button onClick={onClick} className="group w-full text-left">
        {content}
      </button>
    );
  }

  return (
    <Link to={href} className="group block">
      {content}
    </Link>
  );
};

export default QuickActionCard;
