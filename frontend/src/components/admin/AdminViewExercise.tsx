import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useAdmin } from "../../context/AdminContext";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Button } from "../ui/Button";
import RichTextDisplay from "../ui/RichTextDisplay";
import { Exercise } from "../../types/api/exercises.types";
import { Question } from "../../types/exercise.types";
import {
  BookOpen,
  Calendar,
  User,
  Edit,
  ArrowLeft,
  FileText,
  MessageSquare,
  BarChart3,
  CheckSquare,
  Table,
  TrendingUp,
} from "lucide-react";

const AdminViewExercise = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { fetchExerciseById, loading, error } = useAdmin();
  const [exercise, setExercise] = useState<Exercise | null>(null);

  useEffect(() => {
    if (id) {
      fetchExerciseById(id).then((fetchedExercise) => {
        if (fetchedExercise) {
          setExercise(fetchedExercise);
        }
      });
    }
  }, [id, fetchExerciseById]);

  const getQuestionTypeIcon = (type: Question["type"]) => {
    switch (type) {
      case "free_text":
        return <MessageSquare className="h-4 w-4" />;
      case "likert":
        return <BarChart3 className="h-4 w-4" />;
      case "task":
        return <CheckSquare className="h-4 w-4" />;
      case "table":
        return <Table className="h-4 w-4" />;
      case "graph_2d":
        return <TrendingUp className="h-4 w-4" />;
      case "text_block":
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getQuestionTypeLabel = (type: Question["type"]) => {
    switch (type) {
      case "free_text":
        return "Free Text";
      case "likert":
        return "Likert Scale";
      case "task":
        return "Task";
      case "table":
        return "Table";
      case "graph_2d":
        return "2D Graph";
      case "text_block":
        return "Text Block";
      default:
        return "Unknown";
    }
  };

  const getQuestionTypeBadgeColor = (type: Question["type"]) => {
    switch (type) {
      case "free_text":
        return "bg-blue-100 text-blue-800";
      case "likert":
        return "bg-green-100 text-green-800";
      case "task":
        return "bg-purple-100 text-purple-800";
      case "table":
        return "bg-orange-100 text-orange-800";
      case "graph_2d":
        return "bg-red-100 text-red-800";
      case "text_block":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading.exercises) {
    return <LoadingSpinner />;
  }

  if (!exercise) {
    return (
      <div className="space-y-6">
        <PageHeader
          title="Exercise Not Found"
          description="The requested exercise could not be found"
        />
        <Card className="p-6 text-center">
          <p className="text-gray-500">
            The exercise you're looking for doesn't exist or you don't have
            permission to view it.
          </p>
          <Button
            onClick={() => navigate("/dashboard/exercises")}
            className="mt-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Exercises
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={exercise.name}
        description="View exercise details and questions"
        actionButton={{
          label: "Edit Exercise",
          onClick: () => navigate(`/dashboard/exercises/edit/${exercise.id}`),
        }}
      />

      {error && <ErrorMessage message={error} />}

      {/* Back Button */}
      <Button
        variant="outline"
        onClick={() => navigate("/dashboard/exercises")}
        className="mb-4"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Exercises
      </Button>

      {/* Exercise Overview */}
      <Card>
        <CardHeader title="Exercise Overview" />
        <div className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">
                Description
              </h3>
              {exercise.description ? (
                <RichTextDisplay
                  content={exercise.description}
                  className="text-gray-900"
                />
              ) : (
                <p className="text-gray-900">No description provided</p>
              )}
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Exercise ID
                </h3>
                <p className="text-gray-900 font-mono text-sm">{exercise.id}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Created
                </h3>
                <div className="flex items-center text-gray-900">
                  <Calendar className="h-4 w-4 mr-2" />
                  {new Date(exercise.createdAt).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">
                  Created By
                </h3>
                <div className="flex items-center text-gray-900">
                  <User className="h-4 w-4 mr-2" />
                  {exercise.createdBy}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Exercise Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Total Questions
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {exercise.questions.length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <CheckSquare className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Required Questions
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {exercise.questions.filter((q) => q.required).length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Question Types
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {new Set(exercise.questions.map((q) => q.type)).size}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Questions List */}
      <Card>
        <CardHeader title="Questions" />
        <div className="p-6 pt-0">
          {exercise.questions.length === 0 ? (
            <div className="text-center py-8">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">
                No questions have been added to this exercise yet.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {exercise.questions.map((question, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="inline-flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-600 text-xs font-medium rounded-full">
                        {index + 1}
                      </span>
                      <span
                        className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${getQuestionTypeBadgeColor(
                          question.type
                        )}`}
                      >
                        {getQuestionTypeIcon(question.type)}
                        <span className="ml-1">
                          {getQuestionTypeLabel(question.type)}
                        </span>
                      </span>
                      {question.type !== "text_block" &&
                        (question as any).required && (
                          <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                            Required
                          </span>
                        )}
                    </div>
                  </div>

                  {question.type === "text_block" ? (
                    <>
                      {(question as any).title && (
                        <div className="mb-2">
                          <h4 className="text-sm font-medium text-gray-900 mb-1">
                            Title
                          </h4>
                          <p className="text-gray-700">
                            {(question as any).title}
                          </p>
                        </div>
                      )}
                      <div className="mb-2">
                        <h4 className="text-sm font-medium text-gray-900 mb-1">
                          Content
                        </h4>
                        <RichTextDisplay
                          content={(question as any).content || ""}
                          className="prose max-w-none"
                        />
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="mb-2">
                        <h4 className="text-sm font-medium text-gray-900 mb-1">
                          Prompt
                        </h4>
                        <p className="text-gray-700">
                          {(question as any).prompt}
                        </p>
                      </div>

                      {(question as any).description && (
                        <div className="mb-2">
                          <h4 className="text-sm font-medium text-gray-900 mb-1">
                            Description
                          </h4>
                          <RichTextDisplay
                            content={(question as any).description}
                            className="text-gray-600 text-sm"
                          />
                        </div>
                      )}
                    </>
                  )}

                  {/* Question-specific details */}
                  {question.type === "free_text" && question.placeholder && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium text-gray-900 mb-1">
                        Placeholder
                      </h4>
                      <p className="text-gray-600 text-sm italic">
                        "{question.placeholder}"
                      </p>
                    </div>
                  )}

                  {question.type === "likert" && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium text-gray-900 mb-1">
                        Scale
                      </h4>
                      <p className="text-gray-600 text-sm">
                        {question.scaleMin} to {question.scaleMax}
                      </p>
                      {Object.keys(question.labels).length > 0 && (
                        <div className="mt-1">
                          <h5 className="text-xs font-medium text-gray-700 mb-1">
                            Labels:
                          </h5>
                          <div className="flex flex-wrap gap-1">
                            {Object.entries(question.labels).map(
                              ([value, label]) => (
                                <span
                                  key={value}
                                  className="inline-flex items-center px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                                >
                                  {value}: {label}
                                </span>
                              )
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {question.type === "table" && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium text-gray-900 mb-1">
                        Columns
                      </h4>
                      <div className="space-y-1">
                        {question.columns.map((column, colIndex) => (
                          <div key={colIndex} className="text-sm text-gray-600">
                            <span className="font-medium">{column.header}</span>
                            <span className="text-gray-500 ml-2">
                              ({column.columnType})
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {question.type === "graph_2d" && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium text-gray-900 mb-1">
                        Axes
                      </h4>
                      <div className="text-sm text-gray-600">
                        <p>X-Axis: {question.xAxisLabel}</p>
                        <p>Y-Axis: {question.yAxisLabel}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <Button
          variant="outline"
          onClick={() => navigate("/dashboard/exercises")}
        >
          Back to Exercises
        </Button>
        <Button
          onClick={() => navigate(`/dashboard/exercises/edit/${exercise.id}`)}
        >
          <Edit className="h-4 w-4 mr-2" />
          Edit Exercise
        </Button>
      </div>
    </div>
  );
};

export default AdminViewExercise;
