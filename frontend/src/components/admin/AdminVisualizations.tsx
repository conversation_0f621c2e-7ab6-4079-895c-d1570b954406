import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAdmin } from "../../context/AdminContext";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { DataTable } from "../ui/DataTable";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Button } from "../ui/Button";
import { Column } from "../../types/components.types";
import { Visualization } from "../../types/api/visualizations.types";
import { Eye, Edit, Calendar, Volume2, FileText, Trash2 } from "lucide-react";

const AdminVisualizations = () => {
  const navigate = useNavigate();
  const {
    allVisualizations,
    loading,
    error,
    fetchAllVisualizations,
    deleteVisualizationById,
  } = useAdmin();

  useEffect(() => {
    fetchAllVisualizations();
  }, [fetchAllVisualizations]);

  const handleDeleteVisualization = async (visualization: Visualization) => {
    const confirmMessage = `Are you sure you want to delete "${visualization.title}"?\n\nThis will also delete all visualization assignments associated with this visualization. This action cannot be undone.`;

    if (window.confirm(confirmMessage)) {
      try {
        await deleteVisualizationById(visualization.id);
      } catch (error) {
        console.error("Failed to delete visualization:", error);
        // Error is already handled in the context
      }
    }
  };

  const columns: Column<Visualization>[] = [
    {
      header: "Title",
      accessorKey: "title",
      cell: (visualization) => (
        <div className="font-medium text-gray-900">{visualization.title}</div>
      ),
    },
    {
      header: "Description",
      accessorKey: "description",
      cell: (visualization) => (
        <div className="text-sm text-gray-500 max-w-xs truncate">
          {visualization.description}
        </div>
      ),
    },
    {
      header: "Audio",
      accessorKey: "audioUrl",
      cell: (visualization) => (
        <div className="flex items-center">
          {visualization.audioUrl ? (
            <div className="flex items-center text-green-600">
              <Volume2 className="h-4 w-4 mr-1" />
              <span className="text-sm">Available</span>
            </div>
          ) : (
            <div className="flex items-center text-gray-400">
              <FileText className="h-4 w-4 mr-1" />
              <span className="text-sm">Text only</span>
            </div>
          )}
        </div>
      ),
    },
    {
      header: "Created",
      accessorKey: "createdAt",
      cell: (visualization) => (
        <div className="text-sm text-gray-500">
          {new Date(visualization.createdAt).toLocaleDateString()}
        </div>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (visualization) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() =>
              navigate(`/dashboard/visualizations/${visualization.id}`)
            }
          >
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() =>
              navigate(`/dashboard/visualizations/edit/${visualization.id}`)
            }
          >
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleDeleteVisualization(visualization)}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Delete
          </Button>
        </div>
      ),
    },
  ];

  if (loading.visualizations) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Visualizations"
        description="Manage visualization exercises and content"
        actionButton={{
          label: "Create Visualization",
          onClick: () => navigate("/dashboard/visualizations/create"),
        }}
      />

      {error && <ErrorMessage message={error} />}

      {/* Visualization Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Total Visualizations
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allVisualizations.length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Volume2 className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">With Audio</p>
              <p className="text-lg font-semibold text-gray-900">
                {allVisualizations.filter((viz) => viz.audioUrl).length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Calendar className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">This Month</p>
              <p className="text-lg font-semibold text-gray-900">
                {
                  allVisualizations.filter((visualization) => {
                    const created = new Date(visualization.createdAt);
                    const now = new Date();
                    return (
                      created.getMonth() === now.getMonth() &&
                      created.getFullYear() === now.getFullYear()
                    );
                  }).length
                }
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Visualizations Table */}
      <Card>
        <CardHeader title="All Visualizations" />
        <div className="p-6 pt-0">
          <DataTable
            columns={columns}
            data={allVisualizations}
            className="w-full"
          />
        </div>
      </Card>
    </div>
  );
};

export default AdminVisualizations;
