import { useState } from "react";
import { PageHeader } from "../ui/PageHeader";
import TextInput from "../ui/input/TextInput";
import { Button } from "../ui/Button";
import { createOrganization } from "../../api/organizations";

const AdminCreateOrganizations = () => {
  const [name, setName] = useState("");

  const handleCreateOrganization = async () => {
    if (!name) {
      alert("Please fill in all fields.");
      return;
    }

    try {
      const organizationData = {
        name,
      };
      const response = await createOrganization(organizationData);
      if (response) {
        alert("Organization created successfully!");
        // Optionally, redirect or reset form
        setName("");
      } else {
        alert("Failed to create organization.");
      }
    } catch (error) {
      console.error("Error creating organization:", error);
      alert("An error occurred while creating the organization.");
    }
  };

  return (
    <div className="flex flex-col h-full">
      <PageHeader
        title="Create Organization"
        description="Fill in the details to create a new organization."
      />
      <div>
        <TextInput
          label="Organization Name"
          placeholder="Enter organization name"
          value={name}
          onChange={(value) => setName(value)}
          required
        />

        <Button
          variant="primary"
          className="mt-4"
          onClick={handleCreateOrganization}
        >
          Create Organization
        </Button>
      </div>
    </div>
  );
};

export default AdminCreateOrganizations;
