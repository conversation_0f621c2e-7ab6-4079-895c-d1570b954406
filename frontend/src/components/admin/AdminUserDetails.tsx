import {
  Table,
  TableDataCell,
  TableHeader,
  TableHeaderCell,
  TableRow,
} from "../ui/Table";
import { useAdmin } from "../../context/AdminContext";
import { Button } from "../ui/Button";
import { useNavigate, useParams } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { useEffect, useState } from "react";
import SelectInput from "../ui/input/SelectInput";
import { Brain, TrendingUp } from "lucide-react";
import EvaluationChart from "../coach/EvaluationChart";
import EvaluationSummary from "../coach/EvaluationSummary";
import EvaluationRadarChart from "../coach/EvaluationRadarChart";
import {
  EvaluationTrend,
  getLatestEvaluation,
  Evaluation,
} from "../../api/evaluations";

const AdminUserDetails = () => {
  const { id } = useParams<{ id: string }>();
  const {
    userDetails,
    allOrganizations,
    fetchUserDetails,
    fetchAllOrganizations,
    getEvaluationTrendsForCoachee,
  } = useAdmin();

  const [evaluationTrends, setEvaluationTrends] = useState<EvaluationTrend[]>(
    []
  );
  const [latestEvaluation, setLatestEvaluation] = useState<Evaluation | null>(
    null
  );
  const [loadingEvaluations, setLoadingEvaluations] = useState(false);
  const [showRadarChart, setShowRadarChart] = useState(true);

  useEffect(() => {
    if (id) {
      fetchUserDetails(id);
      // If this is a coachee, fetch their evaluation data
      fetchEvaluationData(id);
    }
    if (!allOrganizations || allOrganizations.length === 0) {
      fetchAllOrganizations();
    }
  }, [id]);

  const fetchEvaluationData = async (userId: string) => {
    setLoadingEvaluations(true);
    try {
      const [trends, latest] = await Promise.all([
        getEvaluationTrendsForCoachee(userId),
        getLatestEvaluation(userId).catch(() => null), // Handle case where no evaluations exist
      ]);
      setEvaluationTrends(trends);
      setLatestEvaluation(latest);
    } catch (error) {
      console.error("Error fetching evaluation data:", error);
      setEvaluationTrends([]);
      setLatestEvaluation(null);
    } finally {
      setLoadingEvaluations(false);
    }
  };

  return (
    <div className="w-full">
      <PageHeader
        title={`User Details for ${userDetails?.firstName} ${userDetails?.lastName}`}
        description="Modify an existing visualization exercise."
      />
      {/* Show the user details in key value pair format */}
      <div className="space-y-4 p-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHeaderCell>Field</TableHeaderCell>
              <TableHeaderCell>Value</TableHeaderCell>
            </TableRow>
          </TableHeader>
          <tbody className="bg-white divide-y divide-gray-200">
            {userDetails && (
              <>
                <TableRow>
                  <TableDataCell>First Name</TableDataCell>
                  <TableDataCell>{userDetails.firstName}</TableDataCell>
                </TableRow>
                <TableRow>
                  <TableDataCell>Last Name</TableDataCell>
                  <TableDataCell>{userDetails.lastName}</TableDataCell>
                </TableRow>
                <TableRow>
                  <TableDataCell>Email</TableDataCell>
                  <TableDataCell>{userDetails.email}</TableDataCell>
                </TableRow>
                <TableRow>
                  <TableDataCell>Role</TableDataCell>
                  <TableDataCell>{userDetails.role}</TableDataCell>
                </TableRow>
              </>
            )}
          </tbody>
        </Table>
        {userDetails?.coachRelationships &&
          userDetails.coachRelationships.length > 0 && (
            <div className="mt-6">
              <h2 className="text-lg font-semibold mb-4">
                Coach Relationships
              </h2>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHeaderCell>Coachee</TableHeaderCell>
                    <TableHeaderCell>Coachee Email</TableHeaderCell>
                    <TableHeaderCell>Notes</TableHeaderCell>
                  </TableRow>
                </TableHeader>
                <tbody className="bg-white divide-y divide-gray-200">
                  {userDetails.coachRelationships.map((relationship) => (
                    <TableRow key={relationship.coachee.id}>
                      <TableDataCell>
                        {relationship.coachee.firstName}{" "}
                        {relationship.coachee.lastName}
                      </TableDataCell>
                      <TableDataCell>
                        {relationship.coachee.email}
                      </TableDataCell>
                      <TableDataCell>
                        {relationship.coachNotes || "No notes available"}
                      </TableDataCell>
                    </TableRow>
                  ))}
                </tbody>
              </Table>
            </div>
          )}

        {userDetails?.coacheeRelationships &&
          userDetails.coacheeRelationships.length > 0 && (
            <div className="mt-6">
              <h2 className="text-lg font-semibold mb-4">
                Coachee Relationships
              </h2>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHeaderCell>Coach</TableHeaderCell>
                    <TableHeaderCell>Coach Email</TableHeaderCell>
                  </TableRow>
                </TableHeader>
                <tbody className="bg-white divide-y divide-gray-200">
                  {userDetails.coacheeRelationships.map((relationship) => (
                    <TableRow key={relationship.coach.id}>
                      <TableDataCell>
                        {relationship.coach.firstName}{" "}
                        {relationship.coach.lastName}
                      </TableDataCell>
                      <TableDataCell>{relationship.coach.email}</TableDataCell>
                    </TableRow>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        <div className="mt-6">
          <h2 className="text-lg font-semibold mb-4">
            Organization Memberships
          </h2>
          {userDetails?.organizationMemberships &&
          userDetails.organizationMemberships.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHeaderCell>Organization Name</TableHeaderCell>
                </TableRow>
              </TableHeader>
              <tbody className="bg-white divide-y divide-gray-200">
                {userDetails.organizationMemberships.map((membership) => (
                  <TableRow key={membership.organization.id}>
                    <TableDataCell>
                      {membership.organization.name}
                    </TableDataCell>
                  </TableRow>
                ))}
              </tbody>
            </Table>
          ) : (
            <>
              <p className="text-gray-500">
                No organization memberships found.
              </p>

              <div className="flex items-end gap-2">
                <SelectInput
                  label="Assign Organization"
                  options={[]}
                  placeholder="Select an organization"
                  onChange={(value) => {
                    // Handle organization assignment logic here
                  }}
                />
                <Button
                  variant="primary"
                  size="sm"
                  className="mb-2 w-64"
                  onClick={() => {
                    // Handle organization assignment logic here
                  }}
                >
                  Assign Organization
                </Button>
              </div>
            </>
          )}
        </div>

        {/* B3-5C Evaluations Section - Only show for coachees */}
        {userDetails?.role === "COACHEE" && (
          <div className="mt-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-blue-600" />
                <h2 className="text-lg font-semibold">
                  B3-5C Mental Toughness Evaluations
                </h2>
              </div>
              {latestEvaluation && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowRadarChart(true)}
                    className={`px-3 py-1 text-sm rounded-md ${
                      showRadarChart
                        ? "bg-blue-100 text-blue-700"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    }`}
                  >
                    Radar Chart
                  </button>
                  <button
                    onClick={() => setShowRadarChart(false)}
                    className={`px-3 py-1 text-sm rounded-md ${
                      !showRadarChart
                        ? "bg-blue-100 text-blue-700"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    }`}
                  >
                    Detailed View
                  </button>
                </div>
              )}
            </div>

            {loadingEvaluations ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-500">
                  Loading evaluations...
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Latest Evaluation */}
                {latestEvaluation ? (
                  <div>
                    <h3 className="text-md font-medium text-gray-900 mb-3">
                      Latest Evaluation
                    </h3>
                    {showRadarChart ? (
                      <div className="bg-white border rounded-lg p-6">
                        <div className="text-center mb-4">
                          <h4 className="text-lg font-medium text-gray-900">
                            {latestEvaluation.title ||
                              "B3-5C Mental Toughness Evaluation"}
                          </h4>
                          <p className="text-sm text-gray-600">
                            Created on{" "}
                            {new Date(
                              latestEvaluation.createdAt
                            ).toLocaleDateString()}
                            {" by "}
                            {latestEvaluation.coach.firstName}{" "}
                            {latestEvaluation.coach.lastName}
                          </p>
                        </div>
                        <EvaluationRadarChart
                          evaluation={latestEvaluation}
                          size={300}
                          showLabels={true}
                          showValues={true}
                        />
                      </div>
                    ) : (
                      <EvaluationSummary
                        evaluation={latestEvaluation}
                        showCoachInfo={true}
                        compact={false}
                      />
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <Brain className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      No evaluations found
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      This coachee has not been evaluated yet.
                    </p>
                  </div>
                )}

                {/* Evaluation Trends Chart */}
                {evaluationTrends.length > 0 && (
                  <div>
                    <div className="flex items-center space-x-2 mb-3">
                      <TrendingUp className="h-5 w-5 text-blue-600" />
                      <h3 className="text-md font-medium text-gray-900">
                        Progress Over Time
                      </h3>
                    </div>
                    <div className="bg-white border rounded-lg p-4">
                      <EvaluationChart
                        trends={evaluationTrends}
                        height={300}
                        width={700}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminUserDetails;
