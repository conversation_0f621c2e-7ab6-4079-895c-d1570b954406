import React, { useState } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical } from "lucide-react";

interface DraggableItemProps {
  id: string;
  children: React.ReactNode;
  isDragOverlay?: boolean;
}

const DraggableItem: React.FC<DraggableItemProps> = ({
  id,
  children,
  isDragOverlay = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.3 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`relative ${isDragOverlay ? "z-50" : ""} ${
        isDragging ? "scale-105" : ""
      } transition-transform duration-200`}
    >
      <div
        className={`flex items-start space-x-2 ${
          isDragging ? "bg-blue-50 border-blue-200 border-2 rounded-lg p-2" : ""
        }`}
      >
        <div
          {...attributes}
          {...listeners}
          className="flex-shrink-0 mt-2 p-1 cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600 transition-colors rounded hover:bg-gray-100"
          aria-label="Drag to reorder"
        >
          <GripVertical className="h-4 w-4" />
        </div>
        <div className="flex-1 min-w-0">{children}</div>
      </div>
    </div>
  );
};

interface DraggableListProps<T> {
  items: T[];
  onReorder: (items: T[]) => void;
  renderItem: (item: T, index: number) => React.ReactNode;
  getItemId: (item: T, index: number) => string;
  className?: string;
}

function DraggableList<T>({
  items,
  onReorder,
  renderItem,
  getItemId,
  className = "",
}: DraggableListProps<T>) {
  const [activeId, setActiveId] = useState<string | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex(
        (_, index) => getItemId(items[index], index) === active.id
      );
      const newIndex = items.findIndex(
        (_, index) => getItemId(items[index], index) === over.id
      );

      if (oldIndex !== -1 && newIndex !== -1) {
        const newItems = arrayMove(items, oldIndex, newIndex);
        onReorder(newItems);
      }
    }

    setActiveId(null);
  };

  const itemIds = items.map((item, index) => getItemId(item, index));

  const activeItem = activeId
    ? items.find((_, index) => getItemId(items[index], index) === activeId)
    : null;
  const activeIndex = activeId
    ? items.findIndex((_, index) => getItemId(items[index], index) === activeId)
    : -1;

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <SortableContext items={itemIds} strategy={verticalListSortingStrategy}>
        <div className={`space-y-4 ${className}`}>
          {items.map((item, index) => {
            const id = getItemId(item, index);
            return (
              <DraggableItem key={id} id={id}>
                {renderItem(item, index)}
              </DraggableItem>
            );
          })}
        </div>
      </SortableContext>
      <DragOverlay>
        {activeItem && activeIndex !== -1 ? (
          <div className="bg-white border-2 border-blue-500 rounded-lg shadow-lg opacity-95">
            <DraggableItem id={activeId!} isDragOverlay>
              {renderItem(activeItem, activeIndex)}
            </DraggableItem>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}

export default DraggableList;
