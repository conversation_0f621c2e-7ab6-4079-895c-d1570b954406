type LoadingSpinnerProps = {
  size?: "sm" | "md" | "lg";
  fullScreen?: boolean;
};

export function LoadingSpinner({
  size = "md",
  fullScreen = true,
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4 border-b-1",
    md: "h-8 w-8 border-b-2",
    lg: "h-12 w-12 border-b-3",
  };

  return (
    <div
      className={`flex items-center justify-center ${
        fullScreen ? "h-screen" : ""
      }`}
    >
      <div
        className={`animate-spin rounded-full ${sizeClasses[size]} border-indigo-600`}
      ></div>
      {size !== "sm" && <span className="ml-2 text-gray-600">Loading...</span>}
    </div>
  );
}
