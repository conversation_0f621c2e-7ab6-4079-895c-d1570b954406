interface TextResponseProps {
  value: string;
  onChange: (value: string) => void;
  isCompleted: boolean;
  saving: boolean;
}

export function TextResponse({ value, onChange, isCompleted, saving }: TextResponseProps) {
  return (
    <div>
      {isCompleted ? (
        <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
          <p className="text-sm text-gray-800">
            {value || "No response provided"}
          </p>
        </div>
      ) : (
        <textarea
          className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
          rows={3}
          placeholder="Enter your response..."
          value={value}
          onChange={(e) => onChange(e.target.value)}
          disabled={saving}
        />
      )}
    </div>
  );
}
