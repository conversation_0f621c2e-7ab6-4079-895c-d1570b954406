import { TableColumn } from "../../../types/exercise.types";
import {
  Table,
  TableBody,
  TableHeader,
  TableHeaderCell,
  TableRow,
  TableDataCell,
} from "../Table";
import { Button } from "../Button";
import TextAreaInput from "../input/TextAreaInput";

interface TableResponseProps {
  columns: TableColumn[];
  value: Record<string, string | number>[];
  onChange: (value: Record<string, string | number>[]) => void;
  isCompleted: boolean;
  saving: boolean;
  rowMode: "dynamic" | "fixed";
  fixedRows?: number;
}

export function TableResponse({
  columns,
  value = [],
  onChange,
  isCompleted,
  saving,
  rowMode,
  fixedRows = 1,
}: TableResponseProps) {
  // For dynamic mode, ensure at least 1 row, for fixed mode use fixedRows
  const rows = rowMode === "fixed" ? fixedRows : Math.max(value.length, 1);

  const handleCellChange = (
    rowIndex: number,
    columnId: string,
    cellValue: string | number
  ) => {
    const newValue = [...value];
    if (!newValue[rowIndex]) {
      newValue[rowIndex] = {};
    }
    newValue[rowIndex] = { ...newValue[rowIndex], [columnId]: cellValue };
    onChange(newValue);
  };

  const handleAddRow = () => {
    onChange([...value, {}]);
  };

  const renderLikertCell = (column: TableColumn, rowIndex: number) => {
    if (column.columnType !== "likert") return null;

    const likertColumn = column;
    const currentValue = value[rowIndex]?.[column.id];
    const options = Array.from(
      { length: likertColumn.scaleMax - likertColumn.scaleMin + 1 },
      (_, i) => likertColumn.scaleMin + i
    );

    return (
      <div className="flex items-center w-full justify-center">
        {options.map((option) => (
          <div className="relative" key={option}>
            {likertColumn.labels?.[option] && (
              <span className="absolute -top-5 text-xs font-medium text-gray-700 whitespace-nowrap transform -translate-x-1/2 left-1/2">
                {likertColumn.labels[option]}
              </span>
            )}
            <Button
              variant={currentValue === option ? "primary" : "secondary"}
              size="xs"
              onClick={() => handleCellChange(rowIndex, column.id, option)}
              disabled={isCompleted || saving}
            >
              {option}
            </Button>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="mt-4 space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHeaderCell key={column.id}>{column.header}</TableHeaderCell>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <TableRow key={rowIndex}>
              {columns.map((column) => (
                <TableDataCell
                  key={`${rowIndex}-${column.id}`}
                  className="px-0 py-0"
                >
                  {isCompleted ? (
                    <span>{value[rowIndex]?.[column.id] || ""}</span>
                  ) : column.columnType === "free_text" ? (
                    <TextAreaInput
                      className="min-w-[100px]"
                      placeholder={column.placeholder}
                      value={(value[rowIndex]?.[column.id] as string) || ""}
                      onChange={(text) =>
                        handleCellChange(rowIndex, column.id, text)
                      }
                      disabled={saving}
                      rows={2}
                    />
                  ) : (
                    renderLikertCell(column, rowIndex)
                  )}
                </TableDataCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {rowMode === "dynamic" && !isCompleted && (
        <div className="flex justify-end">
          <Button
            variant="secondary"
            size="sm"
            onClick={handleAddRow}
            disabled={saving}
          >
            Add Row
          </Button>
        </div>
      )}
    </div>
  );
}
