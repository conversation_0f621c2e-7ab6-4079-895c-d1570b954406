import React, { useRef, useEffect, useState } from "react";

interface SliderWithValueProps {
  min: number;
  max: number;
  step?: number;
  value: number;
  onChange: (value: number) => void;
  disabled?: boolean;
  labels?: { [key: number]: string };
  label?: React.ReactNode;
}

export const SliderWithValue: React.FC<SliderWithValueProps> = ({
  min,
  max,
  step = 1,
  value,
  onChange,
  disabled = false,
  labels = {},
  label,
}) => {
  // Calculate thumb position as percentage
  const percent = ((value - min) / (max - min)) * 100;
  // For label positions
  const labelKeys = Object.keys(labels)
    .map(Number)
    .filter((k) => k >= min && k <= max)
    .sort((a, b) => a - b);

  return (
    <div className="relative w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
      )}
      {/* Value above thumb */}
      <span
        className="absolute text-lg font-semibold text-gray-900 select-none"
        style={{
          left: `calc(${percent}% - 18px)`,
          top: "52px",
          pointerEvents: "none",
          zIndex: 2,
          transition: "left 0.1s",
        }}
      >
        {value.toFixed(1)}
      </span>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(parseFloat(e.target.value))}
        className="w-full appearance-none bg-transparent focus:outline-none mt-6"
        disabled={disabled}
        style={{
          height: "32px",
          position: "relative",
          zIndex: 1,
        }}
      />
      {/* Captions positioned above their value */}
      {labelKeys.length > 0 && (
        <>
          {labelKeys.map((k) => {
            const percent = ((k - min) / (max - min)) * 100;
            const ref = useRef<HTMLSpanElement>(null);
            const [labelWidth, setLabelWidth] = useState(50); // default fallback

            useEffect(() => {
              if (ref.current) {
                setLabelWidth(ref.current.offsetWidth);
              }
            }, [labels[k]]);

            return (
              <span
                key={k}
                ref={ref}
                className="absolute text-xs text-gray-500 select-none"
                style={{
                  left: `calc(${percent}% - ${labelWidth / 2}px)`,
                  top: "42px",
                  maxWidth: "64px",
                  width: "64px",
                  textAlign: "center",
                  wordBreak: "break-word",
                  whiteSpace: "pre-line",
                  pointerEvents: "none",
                  zIndex: 3,
                }}
              >
                {labels[k]}
              </span>
            );
          })}
        </>
      )}
      {/* Custom slider styles for gray track and large thumb */}
      <style>{`
        input[type=range].appearance-none::-webkit-slider-runnable-track {
          height: 8px;
          background: #e5e7eb;
          border-radius: 4px;
        }
        input[type=range].appearance-none::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 28px;
          height: 28px;
          border-radius: 50%;
          background: #3B82F6;
          border: 3px solid #fff;
          box-shadow: 0 0 4px rgba(0,0,0,0.15);
          cursor: pointer;
          margin-top: -10px;
        }
        input[type=range].appearance-none:focus::-webkit-slider-thumb {
          outline: 2px solid #6366F1;
        }
        input[type=range].appearance-none::-ms-fill-lower,
        input[type=range].appearance-none::-ms-fill-upper {
          background: #e5e7eb;
        }
        input[type=range].appearance-none::-moz-range-track {
          height: 8px;
          background: #e5e7eb;
          border-radius: 4px;
        }
        input[type=range].appearance-none::-moz-range-thumb {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          background: #3B82F6;
          border: 3px solid #fff;
          box-shadow: 0 0 4px rgba(0,0,0,0.15);
          cursor: pointer;
        }
        input[type=range].appearance-none:focus::-moz-range-thumb {
          outline: 2px solid #6366F1;
        }
        input[type=range].appearance-none::-ms-thumb {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          background: #3B82F6;
          border: 3px solid #fff;
          box-shadow: 0 0 4px rgba(0,0,0,0.15);
          cursor: pointer;
        }
        input[type=range].appearance-none:focus::-ms-thumb {
          outline: 2px solid #6366F1;
        }
        input[type=range].appearance-none {
          background: transparent;
        }
      `}</style>
    </div>
  );
};
