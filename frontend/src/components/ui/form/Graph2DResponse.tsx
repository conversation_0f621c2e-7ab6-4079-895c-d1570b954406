import { GraphPoint } from "../../../types/exercise.types";
import { Button } from "../Button";

interface Graph2DResponseProps {
  xAxisLabel: string;
  yAxisLabel: string;
  xAxisMin?: number;
  xAxisMax?: number;
  yAxisMin?: number;
  yAxisMax?: number;
  value: GraphPoint[];
  onChange: (value: GraphPoint[]) => void;
  isCompleted: boolean;
  saving: boolean;
}

export function Graph2DResponse({
  xAxisLabel,
  yAxisLabel,
  xAxisMin = 0,
  xAxisMax = 10,
  yAxisMin = 0,
  yAxisMax = 10,
  value = [],
  onChange,
  isCompleted,
  saving,
}: Graph2DResponseProps) {
  const svgWidth = 600;
  const svgHeight = 500;
  const padding = 40;
  const graphWidth = svgWidth - padding * 2;
  const graphHeight = svgHeight - padding * 2;

  const scaleX = (x: number) => {
    return ((x - xAxisMin) / (xAxisMax - xAxisMin)) * graphWidth + padding;
  };

  const scaleY = (y: number) => {
    return (
      svgHeight -
      (((y - yAxisMin) / (yAxisMax - yAxisMin)) * graphHeight + padding)
    );
  };

  const unscaleX = (x: number) => {
    return ((x - padding) / graphWidth) * (xAxisMax - xAxisMin) + xAxisMin;
  };

  const unscaleY = (y: number) => {
    return (
      ((svgHeight - y - padding) / graphHeight) * (yAxisMax - yAxisMin) +
      yAxisMin
    );
  };

  const handleGraphClick = (e: React.MouseEvent<SVGSVGElement>) => {
    if (isCompleted || saving) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Only add point if within graph bounds
    if (
      x >= padding &&
      x <= svgWidth - padding &&
      y >= padding &&
      y <= svgHeight - padding
    ) {
      const newPoint = {
        x: Math.round(unscaleX(x)),
        y: Math.round(unscaleY(y)),
      };

      // do not add point if already exists
      if (
        value.some((point) => point.x === newPoint.x && point.y === newPoint.y)
      ) {
        return;
      }

      onChange([...value, newPoint]);
    }
  };

  const handlePointDelete = (index: number) => {
    const newValue = [...value];
    newValue.splice(index, 1);
    onChange(newValue);
  };

  // Generate grid marks
  const gridStep = 1; // One unit intervals
  const xGridLines = [];
  const yGridLines = [];

  for (let i = xAxisMin; i <= xAxisMax; i += gridStep) {
    xGridLines.push(i);
  }
  for (let i = yAxisMin; i <= yAxisMax; i += gridStep) {
    yGridLines.push(i);
  }

  return (
    <div className="flex mt-4">
      <div>
        <svg
          width={svgWidth}
          height={svgHeight}
          className="bg-white border rounded cursor-crosshair"
          onClick={handleGraphClick}
        >
          {/* Grid lines */}
          {xGridLines.map((x) => (
            <line
              key={`x-grid-${x}`}
              x1={scaleX(x)}
              y1={padding}
              x2={scaleX(x)}
              y2={svgHeight - padding}
              stroke="#e5e5e5"
              strokeWidth="1"
            />
          ))}
          {yGridLines.map((y) => (
            <line
              key={`y-grid-${y}`}
              x1={padding}
              y1={scaleY(y)}
              x2={svgWidth - padding}
              y2={scaleY(y)}
              stroke="#e5e5e5"
              strokeWidth="1"
            />
          ))}

          {/* X-axis */}
          <line
            x1={padding}
            y1={svgHeight - padding}
            x2={svgWidth - padding}
            y2={svgHeight - padding}
            stroke="black"
          />
          {/* Y-axis */}
          <line
            x1={padding}
            y1={padding}
            x2={padding}
            y2={svgHeight - padding}
            stroke="black"
          />

          {/* Axis labels */}
          <text
            x={svgWidth / 2}
            y={svgHeight - 5}
            textAnchor="middle"
            className="text-sm"
          >
            {xAxisLabel}
          </text>
          <text
            x={15}
            y={svgHeight / 2}
            textAnchor="middle"
            transform={`rotate(-90, 15, ${svgHeight / 2})`}
            className="text-sm"
          >
            {yAxisLabel}
          </text>

          {/* Axis values */}
          <text
            x={padding - 5}
            y={svgHeight - padding + 20}
            textAnchor="middle"
            className="text-xs"
          >
            {xAxisMin}
          </text>
          <text
            x={svgWidth - padding}
            y={svgHeight - padding + 20}
            textAnchor="middle"
            className="text-xs"
          >
            {xAxisMax}
          </text>
          <text
            x={padding - 20}
            y={svgHeight - padding}
            textAnchor="middle"
            className="text-xs"
          >
            {yAxisMin}
          </text>
          <text
            x={padding - 20}
            y={padding}
            textAnchor="middle"
            className="text-xs"
          >
            {yAxisMax}
          </text>

          {/* Plot points */}
          {value.map((point, index) => (
            <g key={index}>
              <circle
                cx={scaleX(point.x)}
                cy={scaleY(point.y)}
                r={4}
                fill="blue"
                className={isCompleted ? "" : "hover:fill-red-500"}
                onClick={(e) => {
                  if (!isCompleted) {
                    e.stopPropagation();
                    handlePointDelete(index);
                  }
                }}
              />
              {index > 0 && (
                <line
                  x1={scaleX(value[index - 1].x)}
                  y1={scaleY(value[index - 1].y)}
                  x2={scaleX(point.x)}
                  y2={scaleY(point.y)}
                  stroke="blue"
                  strokeWidth={1}
                />
              )}
            </g>
          ))}
        </svg>
      </div>
      <div className="ml-4">
        {!isCompleted && (
          <div className="flex gap-2    ">
            <p className="text-sm text-gray-500 mt-2">
              Click to add points. Click on a point to remove it.
            </p>
            <Button variant="secondary" size="sm" onClick={() => onChange([])}>
              Clear Points
            </Button>
          </div>
        )}

        {value.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Points:</h4>
            <div className="space-y-1">
              {value.map((point, index) => (
                <div key={index} className="text-sm">
                  Point {index + 1}: ({point.x}, {point.y})
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
