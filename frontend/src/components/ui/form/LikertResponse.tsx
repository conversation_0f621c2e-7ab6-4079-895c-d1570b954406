import { LikertLabelMap } from "../../../types/exercise.types";
import { Button } from "../Button";

interface LikertResponseProps {
  value: number | number[];
  responseMode?: "single" | "multi";
  scaleMin: number;
  scaleMax: number;
  labels: LikertLabelMap;
  multiResponseLabels?: string[];
  onChange: (value: number | number[]) => void;
  isCompleted: boolean;
  saving: boolean;
}

export function LikertResponse({
  value,
  responseMode = "single",
  scaleMin,
  scaleMax,
  labels,
  multiResponseLabels,
  onChange,
  isCompleted,
  saving,
}: LikertResponseProps) {
  const options = Array.from(
    { length: scaleMax - scaleMin + 1 },
    (_, i) => scaleMin + i
  );

  return (
    <div className="w-full flex flex-col gap-4">
      {/* Multi-response labels display */}
      {multiResponseLabels && (
        <div className="flex flex-wrap gap-4">
          {multiResponseLabels.map((label, index) => (
            <div key={index} className="flex items-center gap-2">
              <span className="text-sm text-gray-600">{label}:</span>
              <span className="text-sm font-bold text-gray-700">
                {value && Array.isArray(value) ? value[index] || "-" : "-"}
              </span>
            </div>
          ))}
        </div>
      )}

      {/* Scale labels above buttons */}
      <div className="flex items-end justify-between mb-2 min-h-[32px]">
        {options.map((option) => (
          <div key={option} className="flex-1 flex justify-center">
            {labels[option] && (
              <span className="text-xs text-gray-600 text-center max-w-[100px] leading-tight">
                {labels[option]}
              </span>
            )}
          </div>
        ))}
      </div>

      {/* Scale buttons */}
      <div className="flex items-center justify-between gap-1">
        {options.map((option) => (
          <div key={option} className="flex flex-col items-center gap-1">
            <Button
              variant={
                Array.isArray(value)
                  ? (value as number[]).includes(option)
                    ? "primary"
                    : "outline"
                  : value === option
                  ? "primary"
                  : "outline"
              }
              size="sm"
              disabled={isCompleted || saving}
              onClick={() => {
                if (isCompleted || saving) return;

                if (responseMode === "multi" && multiResponseLabels) {
                  const maxEntries = multiResponseLabels.length;
                  if (
                    Array.isArray(value) &&
                    (value as number[]).length >= maxEntries
                  ) {
                    onChange([option]); // Reset and set the first value
                    return;
                  }
                  const newValue = Array.isArray(value)
                    ? [...(value as number[]), option]
                    : [option];
                  onChange(newValue);
                } else {
                  onChange(option);
                }
              }}
              className="min-w-[40px] h-10"
            >
              {option}
            </Button>
          </div>
        ))}
      </div>

      {/* Completed state display */}
      {isCompleted && (
        <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
          <p className="text-sm text-gray-800">
            Selected: {Array.isArray(value) ? value.join(", ") : value}
          </p>
        </div>
      )}
    </div>
  );
}
