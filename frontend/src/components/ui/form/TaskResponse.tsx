interface TaskResponseProps {
  completed: boolean;
  onChange: (completed: boolean) => void;
  isCompleted: boolean;
  saving: boolean;
}

export function TaskResponse({ completed, onChange, isCompleted, saving }: TaskResponseProps) {
  return (
    <div className="flex items-center">
      {isCompleted ? (
        <div className="flex items-center">
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              completed ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
            }`}
          >
            {completed ? "Completed ✓" : "Not Completed ✗"}
          </span>
        </div>
      ) : (
        <>
          <input
            type="checkbox"
            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            checked={completed}
            onChange={(e) => onChange(e.target.checked)}
            disabled={saving}
          />
          <label className="ml-2 block text-sm text-gray-900">
            Mark as completed
          </label>
        </>
      )}
    </div>
  );
}
