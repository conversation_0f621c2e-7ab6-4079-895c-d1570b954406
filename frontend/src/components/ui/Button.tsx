import { Link, To } from "react-router-dom";
import { ButtonHTMLAttributes, ReactNode } from "react";

type ButtonVariant = "primary" | "secondary" | "outline" | "danger" | "link";
type ButtonSize = "xs" | "sm" | "md" | "lg";

interface BaseButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  className?: string;
  children: ReactNode;
}

type ButtonAsButtonProps = ButtonHTMLAttributes<HTMLButtonElement> &
  BaseButtonProps & {
    to?: never;
  };

type ButtonAsLinkProps = BaseButtonProps & {
  to: To;
  onClick?: never;
  type?: never;
};

type ButtonProps = ButtonAsButtonProps | ButtonAsLinkProps;

const variantStyles: Record<ButtonVariant, string> = {
  primary: "bg-indigo-600 text-white hover:bg-indigo-700 border-transparent",
  secondary: "bg-gray-100 text-gray-900 hover:bg-gray-200 border-gray-300",
  outline: "bg-white text-gray-700 hover:bg-gray-50 border-gray-300",
  danger: "bg-red-600 text-white hover:bg-red-700 border-transparent",
  link: "text-indigo-600 hover:text-indigo-700 border-transparent",
};

const sizeStyles: Record<ButtonSize, string> = {
  xs: "px-1 py-0.5 text-sm",
  sm: "px-3 py-1.5 text-sm",
  md: "px-4 py-2 text-sm",
  lg: "px-5 py-2.5 text-base",
};

export function Button({
  variant = "primary",
  size = "md",
  className = "",
  ...props
}: ButtonProps) {
  const baseStyles =
    "inline-flex items-center justify-center font-medium rounded-md border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed";
  const styles = `${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`;

  if ("to" in props && props.to) {
    const { to, ...rest } = props;
    return <Link to={to} className={styles} {...rest} />;
  }

  return <button className={styles} {...props} />;
}
