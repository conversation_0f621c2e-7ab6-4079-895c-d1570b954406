export const Table = ({ children }: { children: React.ReactNode }) => {
  return (
    <table className="min-w-full divide-y divide-gray-200 mt-4">
      {children}
    </table>
  );
};

export const TableHeader = ({ children }: { children: React.ReactNode }) => {
  return <thead className="bg-gray-50">{children}</thead>;
};

export const TableHeaderCell = ({
  children,
  className,
}: {
  children?: React.ReactNode;
  className?: string;
}) => {
  return (
    <th
      scope="col"
      className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${className}`}
    >
      {children}
    </th>
  );
};

export const TableBody = ({ children }: { children: React.ReactNode }) => {
  return (
    <tbody className="bg-white divide-y divide-gray-200">{children}</tbody>
  );
};

export const TableRow = ({ children }: { children: React.ReactNode }) => {
  return <tr>{children}</tr>;
};

export const TableDataCell = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <td
      className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${className}`}
    >
      {children}
    </td>
  );
};
