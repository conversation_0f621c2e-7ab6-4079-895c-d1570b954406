export type TabProps = {
  title: string;
  isActive: boolean;
  isDisabled?: boolean;
  onClick?: () => void;
};

const Tab = ({ title, isActive, isDisabled = false, onClick }: TabProps) => {
  return (
    <button
      className={`flex items-center px-4 py-2 text-sm font-medium text-gray-700 border-b-2 ${
        isActive
          ? "border-indigo-500 text-indigo-600"
          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
      } ${isDisabled ? "cursor-not-allowed" : ""}`}
      onClick={onClick}
      disabled={isDisabled}
    >
      {title}
    </button>
  );
};

export const TabContainer = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="border-b border-gray-200 mb-6">
      <nav className="-mb-px flex space-x-8">{children}</nav>
    </div>
  );
};

export default Tab;
