import { Link } from "react-router-dom";
import { Button } from "./Button";

interface PageHeaderProps {
  title: string;
  description?: string;
  actionButton?: {
    label: string;
    to?: string;
    onClick?: () => void;
  };
}

export function PageHeader({
  title,
  description,
  actionButton,
}: PageHeaderProps) {
  return (
    <div className="sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-2xl font-semibold text-gray-900">{title}</h1>
        {description && (
          <p className="mt-2 text-sm text-gray-700">{description}</p>
        )}
      </div>
      {actionButton && (
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          {actionButton.to ? (
            <Link
              to={actionButton.to}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              {actionButton.label}
            </Link>
          ) : actionButton.onClick ? (
            <Button onClick={actionButton.onClick}>{actionButton.label}</Button>
          ) : null}
        </div>
      )}
    </div>
  );
}
