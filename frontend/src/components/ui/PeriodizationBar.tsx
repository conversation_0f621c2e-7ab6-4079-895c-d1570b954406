import React, { useState, useRef, useEffect } from "react";

interface PeriodizationPhase {
  name: string;
  weeks: number;
  color: string;
  minWeeks: number;
}

interface PeriodizationBarProps {
  offSeasonStart: string;
  competitionEnd: string;
  onPhasesChange: (phases: {
    offSeasonWeeks: number;
    prepWeeks: number;
    preCompWeeks: number;
    competitionWeeks: number;
  }) => void;
}

export const PeriodizationBar: React.FC<PeriodizationBarProps> = ({
  offSeasonStart,
  competitionEnd,
  onPhasesChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<number | null>(null);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartWeeks, setDragStartWeeks] = useState<number[]>([]);

  // Calculate total weeks between start and end dates
  const totalWeeks = React.useMemo(() => {
    if (!offSeasonStart || !competitionEnd) return 0;
    const start = new Date(offSeasonStart);
    const end = new Date(competitionEnd);
    return Math.ceil((end.getTime() - start.getTime()) / (7 * 24 * 60 * 60 * 1000));
  }, [offSeasonStart, competitionEnd]);

  // Initialize phases with default distribution
  const [phases, setPhases] = useState<PeriodizationPhase[]>([
    { name: "Off Season", weeks: Math.max(4, Math.floor(totalWeeks * 0.4)), color: "bg-blue-500", minWeeks: 4 },
    { name: "Prep", weeks: Math.max(4, Math.floor(totalWeeks * 0.4)), color: "bg-green-500", minWeeks: 4 },
    { name: "Pre-Comp", weeks: Math.max(2, Math.floor(totalWeeks * 0.15)), color: "bg-yellow-500", minWeeks: 2 },
    { name: "Competition", weeks: Math.max(1, Math.floor(totalWeeks * 0.05)), color: "bg-red-500", minWeeks: 1 },
  ]);

  // Update phases when total weeks changes
  useEffect(() => {
    if (totalWeeks > 0) {
      const newPhases = [
        { name: "Off Season", weeks: Math.max(4, Math.floor(totalWeeks * 0.4)), color: "bg-blue-500", minWeeks: 4 },
        { name: "Prep", weeks: Math.max(4, Math.floor(totalWeeks * 0.4)), color: "bg-green-500", minWeeks: 4 },
        { name: "Pre-Comp", weeks: Math.max(2, Math.floor(totalWeeks * 0.15)), color: "bg-yellow-500", minWeeks: 2 },
        { name: "Competition", weeks: Math.max(1, Math.floor(totalWeeks * 0.05)), color: "bg-red-500", minWeeks: 1 },
      ];
      
      // Adjust to match total weeks exactly
      const currentTotal = newPhases.reduce((sum, phase) => sum + phase.weeks, 0);
      const diff = totalWeeks - currentTotal;
      if (diff !== 0) {
        // Add/subtract from the largest adjustable phase
        newPhases[0].weeks += diff;
      }
      
      setPhases(newPhases);
    }
  }, [totalWeeks]);

  // Notify parent of phase changes
  useEffect(() => {
    onPhasesChange({
      offSeasonWeeks: phases[0]?.weeks || 0,
      prepWeeks: phases[1]?.weeks || 0,
      preCompWeeks: phases[2]?.weeks || 0,
      competitionWeeks: phases[3]?.weeks || 0,
    });
  }, [phases, onPhasesChange]);

  const handleMouseDown = (e: React.MouseEvent, dividerIndex: number) => {
    e.preventDefault();
    setIsDragging(dividerIndex);
    setDragStartX(e.clientX);
    setDragStartWeeks(phases.map(p => p.weeks));
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging === null || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const containerWidth = rect.width;
    const deltaX = e.clientX - dragStartX;
    const deltaWeeks = Math.round((deltaX / containerWidth) * totalWeeks);

    const newPhases = [...phases];
    const leftPhaseIndex = isDragging;
    const rightPhaseIndex = isDragging + 1;

    // Calculate new weeks for left and right phases
    const leftNewWeeks = dragStartWeeks[leftPhaseIndex] + deltaWeeks;
    const rightNewWeeks = dragStartWeeks[rightPhaseIndex] - deltaWeeks;

    // Check constraints
    const leftMinWeeks = newPhases[leftPhaseIndex].minWeeks;
    const rightMinWeeks = newPhases[rightPhaseIndex].minWeeks;

    if (leftNewWeeks >= leftMinWeeks && rightNewWeeks >= rightMinWeeks) {
      newPhases[leftPhaseIndex].weeks = leftNewWeeks;
      newPhases[rightPhaseIndex].weeks = rightNewWeeks;
      setPhases(newPhases);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(null);
    setDragStartX(0);
    setDragStartWeeks([]);
  };

  useEffect(() => {
    if (isDragging !== null) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragStartX, dragStartWeeks, totalWeeks]);

  if (totalWeeks === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        Please select both off season start date and competition end date to see the periodization bar.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        Total Duration: {totalWeeks} weeks
      </div>
      
      <div 
        ref={containerRef}
        className="relative h-16 bg-gray-200 rounded-lg overflow-hidden cursor-pointer select-none"
      >
        {phases.map((phase, index) => {
          const widthPercentage = (phase.weeks / totalWeeks) * 100;
          const leftPercentage = phases.slice(0, index).reduce((sum, p) => sum + (p.weeks / totalWeeks) * 100, 0);
          
          return (
            <React.Fragment key={phase.name}>
              <div
                className={`absolute top-0 h-full ${phase.color} flex items-center justify-center text-white font-medium text-sm transition-all duration-200`}
                style={{
                  left: `${leftPercentage}%`,
                  width: `${widthPercentage}%`,
                }}
              >
                <div className="text-center">
                  <div>{phase.name}</div>
                  <div className="text-xs opacity-90">{phase.weeks}w</div>
                </div>
              </div>
              
              {/* Divider handle */}
              {index < phases.length - 1 && (
                <div
                  className="absolute top-0 h-full w-2 bg-white border-2 border-gray-300 cursor-col-resize hover:bg-gray-100 transition-colors duration-200 z-10"
                  style={{
                    left: `calc(${leftPercentage + widthPercentage}% - 4px)`,
                  }}
                  onMouseDown={(e) => handleMouseDown(e, index)}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>
      
      {/* Phase details */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        {phases.map((phase) => (
          <div key={phase.name} className="flex items-center space-x-2">
            <div className={`w-4 h-4 ${phase.color} rounded`}></div>
            <span className="font-medium">{phase.name}:</span>
            <span>{phase.weeks} weeks</span>
          </div>
        ))}
      </div>
    </div>
  );
};
