import { ReactNode } from "react";
import { X } from "lucide-react";

interface ErrorMessageProps {
  message: ReactNode;
  className?: string;
  onDismiss?: () => void;
}

export function ErrorMessage({
  message,
  className = "",
  onDismiss,
}: ErrorMessageProps) {
  return (
    <div
      className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}
    >
      <div className="flex items-start">
        <div className="flex-1">
          <div className="text-sm font-medium text-red-800">
            <span>Error: </span>
            {typeof message === "string" ? message : message}
          </div>
        </div>
        {onDismiss && (
          <button
            type="button"
            onClick={onDismiss}
            className="ml-3 flex-shrink-0 text-red-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-red-50 rounded-md"
          >
            <span className="sr-only">Dismiss</span>
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
}
