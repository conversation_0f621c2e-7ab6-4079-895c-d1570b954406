import { useEffect, useState } from "react";

export interface NumberInputProps {
  label?: string;
  placeholder?: string;
  value?: number;
  onChange?: (value: number) => void;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  className?: string;
  min?: number;
  max?: number;
  step?: number;
}

const NumberInput = ({
  label,
  placeholder,
  value,
  onChange,
  required = false,
  disabled = false,
  error,
  className = "",
  min,
  max,
  step,
}: NumberInputProps) => {
  // internal state to handle the input value
  const [inputValue, setInputValue] = useState<string | undefined>(
    value?.toString()
  );

  useEffect(() => {
    // update the internal state when the value prop changes
    if (value !== undefined) {
      setInputValue(value.toString());
    }
  }, [value]);

  return (
    <div className="flex flex-col w-full my-2">
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-1 shrink-0">
          {label}
        </label>
      )}
      <input
        type="number"
        placeholder={placeholder}
        value={inputValue ?? ""}
        onChange={(e) => {
          setInputValue(e.target.value);
        }}
        onBlur={(e) => {
          if (e.target.value === "") {
            setInputValue("0");
            onChange?.(0);
          } else {
            onChange?.(Number(e.target.value));
          }
        }}
        required={required}
        disabled={disabled}
        min={min}
        max={max}
        step={step}
        className={`border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 w-full px-2 py-1.5 ${className}`}
      />
      {error && <span className="text-red-500 text-sm mt-1">{error}</span>}
    </div>
  );
};

export default NumberInput;
