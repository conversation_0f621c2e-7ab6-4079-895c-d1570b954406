import React, { useState, useEffect } from "react";

interface AutocompleteDropdownProps {
  options: string[];
  onSelect: (selected: string) => void;
}

function useDebounce(value: string, delay: number) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function AutocompleteDropdown({
  options,
  onSelect,
}: AutocompleteDropdownProps) {
  const [inputValue, setInputValue] = useState("");
  const [filteredOptions, setFilteredOptions] = useState<string[]>([]);
  const debouncedSearchTerm = useDebounce(inputValue, 300);

  useEffect(() => {
    if (debouncedSearchTerm) {
      setFilteredOptions(
        options.filter((option) =>
          option.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        )
      );
    } else {
      setFilteredOptions([]);
    }
  }, [debouncedSearchTerm, options]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleSelect = (option: string) => {
    setInputValue(option);
    setFilteredOptions([]);
    onSelect(option);
  };

  return (
    <div className="relative w-full">
      <input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        placeholder="Search..."
        className="w-full px-2 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
      {filteredOptions.length > 0 && (
        <ul className="absolute w-full max-h-[200px] overflow-y-auto border border-t-0 border-gray-300 bg-white z-50 list-none p-0 m-0">
          {filteredOptions.map((option, index) => (
            <li
              key={index}
              onClick={() => handleSelect(option)}
              className="px-2 py-2 cursor-pointer hover:bg-gray-100"
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
