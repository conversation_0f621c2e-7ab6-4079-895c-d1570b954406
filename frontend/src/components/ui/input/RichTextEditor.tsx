import { useMemo, useRef, useEffect, useCallback } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

// Suppress ReactQuill findDOMNode warning - this is a known issue with ReactQuill v2.0.0
// The warning doesn't affect functionality and will be resolved in future ReactQuill updates
const suppressReactQuillWarnings = () => {
  const originalError = console.error;
  console.error = (...args) => {
    if (
      typeof args[0] === "string" &&
      args[0].includes("findDOMNode is deprecated")
    ) {
      return;
    }
    originalError(...args);
  };
};

export interface RichTextEditorProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  className?: string;
  height?: string;
  enableImages?: boolean; // Enable/disable image support
  onImageUpload?: (file: File) => Promise<string>; // Custom image upload handler
}

const RichTextEditor = ({
  label,
  placeholder,
  value = "",
  onChange,
  required = false,
  disabled = false,
  error,
  className = "",
  height = "200px",
  enableImages = false,
  onImageUpload,
}: RichTextEditorProps) => {
  const quillRef = useRef<ReactQuill>(null);

  // Suppress ReactQuill warnings on component mount
  useEffect(() => {
    suppressReactQuillWarnings();
    console.log("RichTextEditor mounted with props:", {
      label,
      placeholder,
      value: value?.length || 0,
      disabled,
      height,
      enableImages,
      hasImageUpload: !!onImageUpload,
    });
  }, []);

  // Log when image-related props change
  useEffect(() => {
    console.log("RichTextEditor image props changed:", {
      label,
      enableImages,
      hasImageUpload: !!onImageUpload,
    });
  }, [enableImages, onImageUpload, label]);

  // React-friendly image handler that doesn't break the component
  const imageHandler = useCallback(() => {
    if (!enableImages || !onImageUpload) {
      return;
    }

    // Create input element using React pattern
    const input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("accept", "image/*");

    // Handle file selection
    input.onchange = async () => {
      const file = input.files?.[0];
      if (!file) return;

      try {
        // Get current editor instance and selection
        const editor = quillRef.current?.getEditor();
        if (!editor) return;

        const range = editor.getSelection(true);
        const index = range ? range.index : 0;

        // Show loading state
        editor.insertText(index, "Uploading image...", "user");

        // Upload image using provided handler
        const imageUrl = await onImageUpload(file);

        // Remove loading text and insert image
        editor.deleteText(index, "Uploading image...".length);
        editor.insertEmbed(index, "image", imageUrl, "user");

        // Move cursor after image
        editor.setSelection(index + 1, 0);
      } catch (error) {
        console.error("Image upload failed:", error);

        // Remove loading text on error
        const editor = quillRef.current?.getEditor();
        if (editor) {
          const range = editor.getSelection(true);
          if (range) {
            editor.deleteText(
              range.index - "Uploading image...".length,
              "Uploading image...".length
            );
          }
        }

        // You could show an error message here
        alert("Image upload failed. Please try again.");
      }
    };

    // Trigger file selection
    input.click();
  }, [enableImages, onImageUpload]);

  const modules = useMemo(() => {
    const toolbarConfig = [
      [{ header: [1, 2, 3, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ indent: "-1" }, { indent: "+1" }],
      ["link", "image"], // Temporarily always show image button for debugging
      ["clean"],
    ];

    const config: any = {
      toolbar: {
        container: toolbarConfig,
      },
    };

    // Add image handler if images are enabled
    if (enableImages && onImageUpload) {
      config.toolbar.handlers = {
        image: imageHandler,
      };
    }

    return config;
  }, [enableImages, onImageUpload, imageHandler]);

  const formats = useMemo(() => {
    const baseFormats = [
      "header",
      "bold",
      "italic",
      "underline",
      "strike",
      "list",
      "bullet",
      "indent",
      "link",
      "image", // Temporarily always include image for debugging
    ];

    return baseFormats;
  }, []);

  return (
    <div className={`flex flex-col w-full my-2 ${className}`}>
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-1 shrink-0">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <div
        className="rich-text-editor-wrapper"
        style={{ "--editor-height": height } as React.CSSProperties}
      >
        <ReactQuill
          ref={quillRef}
          theme="snow"
          value={value}
          onChange={(content) => {
            try {
              onChange?.(content);
            } catch (error) {
              console.error("RichTextEditor onChange error:", error);
            }
          }}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          readOnly={disabled}
        />
      </div>
      {error && <span className="text-red-500 text-sm mt-1">{error}</span>}
    </div>
  );
};

export default RichTextEditor;
