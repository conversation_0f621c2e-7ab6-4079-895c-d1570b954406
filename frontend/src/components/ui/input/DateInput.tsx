export interface DateInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  className?: string;
  min?: string;
  max?: string;
}

const DateInput = ({
  label,
  placeholder,
  value,
  onChange,
  required = false,
  disabled = false,
  error,
  className = "",
  min,
  max,
}: DateInputProps) => {
  return (
    <div className="flex flex-col w-full my-2">
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-1 shrink-0">
          {label}
        </label>
      )}

      <input
        type="date"
        placeholder={placeholder}
        value={value}
        min={min}
        max={max}
        onChange={(e) => onChange?.(e.target.value)}
        required={required}
        disabled={disabled}
        className={`border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 w-full px-2 py-1 ${className}`}
      />
      {error && <span className="text-red-500 text-sm mt-1">{error}</span>}
    </div>
  );
};

export default DateInput;
