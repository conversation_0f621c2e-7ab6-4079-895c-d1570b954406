export interface CheckboxProps {
  label?: string;
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  error?: string;
  className?: string;
  id?: string;
}

const Checkbox = ({
  label,
  checked = false,
  onChange,
  disabled = false,
  error,
  className = "",
  id,
}: CheckboxProps) => {
  return (
    <div className={`flex items-center my-2 ${className}`}>
      <input
        id={id}
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange?.(e.target.checked)}
        disabled={disabled}
        className={`h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 ${
          error ? "border-red-500" : ""
        }`}
      />
      {label && (
        <label
          htmlFor={id}
          className="ml-2 text-sm font-medium text-gray-700 shrink-0"
        >
          {label}
        </label>
      )}
      {error && <span className="text-red-500 text-sm ml-2">{error}</span>}
    </div>
  );
};

export default Checkbox;
