export interface TextAreaInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  className?: string;
  rows?: number;
}

const TextAreaInput = ({
  label,
  placeholder,
  value,
  onChange,
  required = false,
  disabled = false,
  error,
  className = "",
  rows = 4,
}: TextAreaInputProps) => {
  return (
    <div className="flex flex-col w-full my-2">
      {label && (
        <label className="text-sm font-medium text-gray-700 mb-1 shrink-0">
          {label}
        </label>
      )}
      <textarea
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        required={required}
        disabled={disabled}
        rows={rows}
        className={`border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 w-full px-2 py-1.5 ${className}`}
      />
      {error && <span className="text-red-500 text-sm mt-1">{error}</span>}
    </div>
  );
};

export default TextAreaInput;
