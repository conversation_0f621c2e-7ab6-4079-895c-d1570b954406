import React from "react";

interface LabelProps {
  icon?: React.ElementType;
  color?: string; // Accept any Tailwind color class or custom color
  text: string;
  iconClassName?: string;
  labelClassName?: string;
}

/**
 * Generic Label component that supports any Lucide icon, color, and text.
 * Pass Tailwind color classes (e.g., 'text-blue-600', 'bg-blue-100', 'text-blue-800')
 * via iconClassName and labelClassName for full flexibility.
 */
export const Label: React.FC<LabelProps> = ({
  icon: Icon,
  color = "blue", // fallback color
  text,
  iconClassName = "",
  labelClassName = "",
}) => {
  // Default Tailwind color classes for the label
  const iconColor = iconClassName || `text-${color}-600`;
  const labelBg = labelClassName || `bg-${color}-100 text-${color}-800`;

  return (
    <span className="inline-flex items-center">
      {Icon && <Icon className={`h-4 w-4 mr-2 ${iconColor}`} />}
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${labelBg}`}
      >
        {text}
      </span>
    </span>
  );
};
