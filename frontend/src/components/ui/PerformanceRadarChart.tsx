import { PerformanceProfileGoal, PerformanceCategory } from "../../types/api/performanceProfile.types";
import { Target, Zap, Brain, Users } from "lucide-react";

interface PerformanceRadarChartProps {
  goals: PerformanceProfileGoal[];
  category: PerformanceCategory;
  size?: number;
  showLabels?: boolean;
  showValues?: boolean;
  title?: string;
}

const PerformanceRadarChart = ({
  goals,
  category,
  size = 300,
  showLabels = true,
  showValues = true,
  title,
}: PerformanceRadarChartProps) => {
  const center = size / 2;
  const radius = (size - 120) / 2;
  const maxScore = 10;

  // Category colors and icons
  const categoryConfig = {
    [PerformanceCategory.PHYSICAL]: {
      color: "#EF4444",
      icon: Zap,
      label: "Physical",
    },
    [PerformanceCategory.TECHNICAL]: {
      color: "#3B82F6",
      icon: Target,
      label: "Technical",
    },
    [PerformanceCategory.TACTICAL]: {
      color: "#10B981",
      icon: Users,
      label: "Tactical",
    },
    [PerformanceCategory.MENTAL]: {
      color: "#8B5CF6",
      icon: Brain,
      label: "Mental",
    },
  };

  const config = categoryConfig[category];
  const Icon = config.icon;

  // Only show active goals
  const activeGoals = goals.filter(goal => goal.isActive);

  if (activeGoals.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8">
        <Icon className="h-12 w-12 text-gray-400 mb-4" />
        <p className="text-gray-500 text-center">
          No active goals in {config.label} category
        </p>
      </div>
    );
  }

  // Calculate angles for each goal (evenly distributed)
  const angleStep = 360 / activeGoals.length;
  const goalsWithAngles = activeGoals.map((goal, index) => ({
    ...goal,
    angle: index * angleStep,
  }));

  // Convert angle to radians and calculate x,y coordinates
  const getCoordinates = (angle: number, distance: number) => {
    const radians = ((angle - 90) * Math.PI) / 180; // -90 to start from top
    return {
      x: center + distance * Math.cos(radians),
      y: center + distance * Math.sin(radians),
    };
  };

  // Generate grid circles (score levels)
  const gridLevels = [2, 4, 6, 8, 10];

  // Generate axis lines
  const axisLines = goalsWithAngles.map((goal) => {
    const end = getCoordinates(goal.angle, radius);
    return {
      ...goal,
      endX: end.x,
      endY: end.y,
    };
  });

  // Generate data polygon points
  const dataPoints = goalsWithAngles.map((goal) => {
    const distance = (goal.currentRating / maxScore) * radius;
    return getCoordinates(goal.angle, distance);
  });

  // Generate target polygon points
  const targetPoints = goalsWithAngles.map((goal) => {
    const distance = (goal.targetRating / maxScore) * radius;
    return getCoordinates(goal.angle, distance);
  });

  const dataPolygonPoints = dataPoints.map(p => `${p.x},${p.y}`).join(' ');
  const targetPolygonPoints = targetPoints.map(p => `${p.x},${p.y}`).join(' ');

  return (
    <div className="flex flex-col items-center space-y-4">
      {title && (
        <div className="flex items-center space-x-2">
          <Icon className="h-5 w-5" style={{ color: config.color }} />
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
      )}

      <div className="relative">
        <svg width={size} height={size} className="overflow-visible">
          {/* Grid circles */}
          {gridLevels.map((level) => (
            <circle
              key={level}
              cx={center}
              cy={center}
              r={(level / maxScore) * radius}
              fill="none"
              stroke="#E5E7EB"
              strokeWidth="1"
            />
          ))}

          {/* Axis lines */}
          {axisLines.map((line, index) => (
            <line
              key={index}
              x1={center}
              y1={center}
              x2={line.endX}
              y2={line.endY}
              stroke="#E5E7EB"
              strokeWidth="1"
            />
          ))}

          {/* Target polygon (dashed) */}
          {targetPoints.length > 2 && (
            <polygon
              points={targetPolygonPoints}
              fill={config.color}
              fillOpacity="0.1"
              stroke={config.color}
              strokeWidth="2"
              strokeDasharray="5,5"
            />
          )}

          {/* Current data polygon */}
          {dataPoints.length > 2 && (
            <polygon
              points={dataPolygonPoints}
              fill={config.color}
              fillOpacity="0.3"
              stroke={config.color}
              strokeWidth="2"
            />
          )}

          {/* Data points */}
          {dataPoints.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              fill={config.color}
              stroke="white"
              strokeWidth="2"
            />
          ))}

          {/* Target points */}
          {targetPoints.map((point, index) => (
            <circle
              key={`target-${index}`}
              cx={point.x}
              cy={point.y}
              r="3"
              fill="none"
              stroke={config.color}
              strokeWidth="2"
              strokeDasharray="2,2"
            />
          ))}

          {/* Goal labels */}
          {showLabels && goalsWithAngles.map((goal, index) => {
            const labelDistance = radius + 40;
            const labelPos = getCoordinates(goal.angle, labelDistance);
            
            return (
              <g key={index}>
                <text
                  x={labelPos.x}
                  y={labelPos.y}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className="text-xs font-medium fill-gray-700"
                  style={{ maxWidth: "80px" }}
                >
                  {goal.goalName.length > 12 
                    ? `${goal.goalName.substring(0, 12)}...` 
                    : goal.goalName}
                </text>
                {showValues && (
                  <text
                    x={labelPos.x}
                    y={labelPos.y + 12}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    className="text-xs fill-gray-500"
                  >
                    {goal.currentRating}/{goal.targetRating}
                  </text>
                )}
              </g>
            );
          })}

          {/* Grid level labels */}
          {gridLevels.map((level) => (
            <text
              key={level}
              x={center + 5}
              y={center - (level / maxScore) * radius}
              className="text-xs fill-gray-400"
              dominantBaseline="middle"
            >
              {level}
            </text>
          ))}
        </svg>
      </div>

      {/* Legend */}
      <div className="flex items-center space-x-4 text-sm">
        <div className="flex items-center space-x-2">
          <div 
            className="w-4 h-4 rounded border-2"
            style={{ 
              backgroundColor: `${config.color}30`,
              borderColor: config.color 
            }}
          />
          <span className="text-gray-600">Current</span>
        </div>
        <div className="flex items-center space-x-2">
          <div 
            className="w-4 h-4 rounded border-2 border-dashed"
            style={{ 
              backgroundColor: `${config.color}10`,
              borderColor: config.color 
            }}
          />
          <span className="text-gray-600">Target</span>
        </div>
      </div>
    </div>
  );
};

export default PerformanceRadarChart;
