import React, { useState, useRef, useEffect } from "react";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  RotateCcw,
  Download,
} from "lucide-react";
import { Button } from "./Button";
import { getVisualizationAudioUrl } from "../../api/audio";

interface AudioPlayerProps {
  audioUrl: string;
  title?: string;
  className?: string;
  onError?: (error: string) => void;
  visualizationId?: string; // Optional: for refreshing expired URLs
}

export const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioUrl,
  title = "Audio",
  className = "",
  onError,
  visualizationId,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentAudioUrl, setCurrentAudioUrl] = useState(audioUrl);
  const [isRefreshingUrl, setIsRefreshingUrl] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Function to refresh the audio URL when it expires
  const refreshAudioUrl = async () => {
    if (!visualizationId) {
      console.warn("Cannot refresh audio URL: visualizationId not provided");
      return false;
    }

    try {
      setIsRefreshingUrl(true);
      const response = await getVisualizationAudioUrl(visualizationId);
      setCurrentAudioUrl(response.audioUrl);
      console.log("Audio URL refreshed successfully");
      return true;
    } catch (error) {
      console.error("Failed to refresh audio URL:", error);
      onError?.("Failed to refresh audio URL. Please try again.");
      return false;
    } finally {
      setIsRefreshingUrl(false);
    }
  };

  // Update currentAudioUrl when audioUrl prop changes
  useEffect(() => {
    setCurrentAudioUrl(audioUrl);
  }, [audioUrl]);

  useEffect(() => {
    const audio = new Audio(currentAudioUrl);
    audioRef.current = audio;

    const handleLoadStart = () => {
      console.log("Audio loading started");
      setIsLoading(true);
    };

    const handleCanPlay = () => {
      console.log("Audio can start playing");
      setIsLoading(false);
    };

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = async (e: Event) => {
      console.error("Audio playback error:", e);
      console.error("Audio error details:", audio.error);
      setIsLoading(false);
      setIsPlaying(false);

      // Check if this might be a 403/access denied error and try to refresh URL
      if (
        audio.error &&
        (audio.error.code === 4 || audio.error.message.includes("403"))
      ) {
        console.log(
          "Detected potential 403 error, attempting to refresh audio URL..."
        );
        const refreshed = await refreshAudioUrl();
        if (refreshed) {
          // Don't show error message if we successfully refreshed
          return;
        }
      }

      const errorMessage = audio.error
        ? `Audio error: ${audio.error.message}`
        : "Failed to load audio file";
      onError?.(errorMessage);
    };

    audio.addEventListener("loadstart", handleLoadStart);
    audio.addEventListener("canplay", handleCanPlay);
    audio.addEventListener("loadedmetadata", handleLoadedMetadata);
    audio.addEventListener("timeupdate", handleTimeUpdate);
    audio.addEventListener("ended", handleEnded);
    audio.addEventListener("error", handleError);

    audio.volume = volume;

    return () => {
      audio.removeEventListener("loadstart", handleLoadStart);
      audio.removeEventListener("canplay", handleCanPlay);
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
      audio.removeEventListener("timeupdate", handleTimeUpdate);
      audio.removeEventListener("ended", handleEnded);
      audio.removeEventListener("error", handleError);
      audio.pause();
    };
  }, [currentAudioUrl, volume, onError, visualizationId]);

  const togglePlayPause = async () => {
    if (!audioRef.current) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error("Error playing audio:", error);
      onError?.(
        `Failed to play audio: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      setIsPlaying(false);
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!audioRef.current) return;
    const newTime = parseFloat(e.target.value);
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    if (!audioRef.current) return;

    if (isMuted) {
      audioRef.current.volume = volume;
      setIsMuted(false);
    } else {
      audioRef.current.volume = 0;
      setIsMuted(true);
    }
  };

  const restart = () => {
    if (!audioRef.current) return;
    audioRef.current.currentTime = 0;
    setCurrentTime(0);
  };

  const downloadAudio = () => {
    const link = document.createElement("a");
    link.href = currentAudioUrl;
    link.download = `${title.replace(/[^a-z0-9]/gi, "_").toLowerCase()}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatTime = (time: number) => {
    if (isNaN(time)) return "0:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div
      className={`bg-white border border-gray-200 rounded-lg p-4 space-y-3 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-900 truncate">{title}</h4>
        <Button
          variant="secondary"
          size="xs"
          onClick={downloadAudio}
          className="h-8 w-8 p-0"
          title="Download audio"
        >
          <Download className="h-4 w-4" />
        </Button>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="relative">
          <input
            type="range"
            min="0"
            max={duration || 0}
            value={currentTime}
            onChange={handleSeek}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            disabled={!duration || isLoading}
          />
          <div
            className="absolute top-0 left-0 h-2 bg-blue-600 rounded-lg pointer-events-none"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>

        {/* Time Display */}
        <div className="flex justify-between text-xs text-gray-500">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* Play/Pause Button */}
          <Button
            variant="outline"
            size="xs"
            onClick={togglePlayPause}
            disabled={isLoading}
            className="h-8 w-8 p-0"
          >
            {isLoading ? (
              <div className="h-4 w-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
            ) : isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>

          {/* Restart Button */}
          <Button
            variant="secondary"
            size="xs"
            onClick={restart}
            disabled={isLoading}
            className="h-8 w-8 p-0"
            title="Restart"
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>

        {/* Volume Controls */}
        <div className="flex items-center space-x-2">
          <Button
            variant="secondary"
            size="xs"
            onClick={toggleMute}
            className="h-8 w-8 p-0"
            title={isMuted ? "Unmute" : "Mute"}
          >
            {isMuted ? (
              <VolumeX className="h-4 w-4" />
            ) : (
              <Volume2 className="h-4 w-4" />
            )}
          </Button>

          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={isMuted ? 0 : volume}
            onChange={handleVolumeChange}
            className="w-16 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            title="Volume"
          />
        </div>
      </div>

      {/* Loading State */}
      {(isLoading || isRefreshingUrl) && (
        <div className="text-xs text-gray-500 text-center">
          {isRefreshingUrl ? "Refreshing audio..." : "Loading audio..."}
        </div>
      )}
    </div>
  );
};
