import { Column } from "../../types/components.types";

interface DataTableProps<T> {
  columns: Column<T>[];
  data: T[];
  className?: string;
}

const getAlignmentClass = (align: "left" | "center" | "right" = "left") => {
  switch (align) {
    case "center":
      return "text-center";
    case "right":
      return "text-right";
    default:
      return "text-left";
  }
};

export function DataTable<T>({
  columns,
  data,
  className = "",
}: DataTableProps<T>) {
  return (
    <div className="mt-8 flex flex-col">
      <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
          <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table
              className={`min-w-full divide-y divide-gray-300 ${className}`}
            >
              <thead className="bg-gray-50">
                <tr>
                  {columns.map((column, index) => (
                    <th
                      key={String(column.header) + String(column.accessorKey)}
                      scope="col"
                      className={`py-3.5 text-sm font-semibold text-gray-900 ${getAlignmentClass(
                        column.align
                      )} ${index === 0 ? "pl-4 pr-3 sm:pl-6" : "px-3"}`}
                    >
                      {column.header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {data.map((item, rowIndex) => (
                  <tr key={rowIndex}>
                    {columns.map((column, colIndex) => (
                      <td
                        key={String(column.header) + String(column.accessorKey)}
                        className={`whitespace-nowrap text-sm ${getAlignmentClass(
                          column.align
                        )} ${
                          colIndex === 0
                            ? "py-4 pl-4 pr-3 font-medium text-gray-900 sm:pl-6"
                            : "px-3 py-4 text-gray-500"
                        }`}
                      >
                        {column.cell
                          ? column.cell(item)
                          : String(item[column.accessorKey] ?? "")}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
