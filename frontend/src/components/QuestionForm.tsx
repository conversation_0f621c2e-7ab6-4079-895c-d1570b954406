import { useState } from "react";
import { Button } from "./ui/Button";
import { Card } from "./ui/Card";
import { ErrorMessage } from "./ui/ErrorMessage";
import { LoadingSpinner } from "./ui/LoadingSpinner";

type QuestionFormProps = {
  exerciseId: string;
  question?: any;
  onSave: (
    exerciseId: string,
    questionText: string,
    questionType: "text" | "task" | "likert",
    likertScaleMax: number | null,
    likertScaleMin: number | null,
    likertCaptions: string | null,
    order: number
  ) => Promise<{ success: boolean; error?: string }>;
  onCancel: () => void;
  isLoading: boolean;
  questionsCount: number;
};

export function QuestionForm({
  exerciseId,
  question,
  onSave,
  onCancel,
  isLoading,
  questionsCount,
}: QuestionFormProps) {
  const [questionText, setQuestionText] = useState(
    question?.question_text || ""
  );
  const [questionType, setQuestionType] = useState<"text" | "task" | "likert">(
    question?.question_type || "text"
  );
  const [likertScaleMax, setLikertScaleMax] = useState<number>(
    question?.likert_scale_max || 5
  );
  const [likertScaleMin, setLikertScaleMin] = useState<number>(
    question?.likert_scale_min || 1
  );
  const [likertCaptions, setLikertCaptions] = useState<string>(
    question?.likert_captions || ""
  );
  const [error, setError] = useState<string>("");

  const handleCaptionsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLikertCaptions(e.target.value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!questionText.trim()) {
      setError("Question text is required");
      return;
    }

    if (questionType === "likert") {
      if (likertScaleMin >= likertScaleMax) {
        setError("Minimum value must be less than maximum value");
        return;
      }

      if (likertScaleMax - likertScaleMin + 1 < 2) {
        setError("Scale range must be at least 2 values");
        return;
      }

      try {
        JSON.parse(likertCaptions || "[]");
      } catch {
        setError("Captions must be a valid JSON array");
        return;
      }
    }

    try {
      const order = question ? question.order : questionsCount + 1;

      const result = await onSave(
        exerciseId,
        questionText,
        questionType,
        questionType === "likert" ? likertScaleMax : null,
        questionType === "likert" ? likertScaleMin : null,
        questionType === "likert" ? likertCaptions : null,
        order
      );

      if (!result.success) {
        setError(result.error || "Failed to save question");
      }
    } catch (err: any) {
      setError(err.message || "An error occurred");
    }
  };

  return (
    <Card className="p-6 mt-4">
      <h3 className="text-lg font-medium mb-4">
        {question ? "Edit Question" : "Add New Question"}
      </h3>

      {error && <ErrorMessage message={error} className="mb-4" />}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="questionText"
            className="block text-sm font-medium text-gray-700"
          >
            Question Text
          </label>
          <textarea
            id="questionText"
            value={questionText}
            onChange={(e) => setQuestionText(e.target.value)}
            rows={3}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={isLoading}
            required
          />
        </div>

        <div>
          <label
            htmlFor="questionType"
            className="block text-sm font-medium text-gray-700"
          >
            Question Type
          </label>
          <select
            id="questionType"
            value={questionType}
            onChange={(e) =>
              setQuestionType(e.target.value as "text" | "task" | "likert")
            }
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={isLoading}
          >
            <option value="text">Free Text Response</option>
            <option value="task">Task Completion (Checkbox)</option>
            <option value="likert">Likert Scale Rating</option>
          </select>
        </div>

        {questionType === "likert" && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Likert Scale Range
              </label>
              <div className="flex gap-4">
                <div className="flex-1">
                  <label htmlFor="likertScaleMin" className="sr-only">
                    Minimum Value
                  </label>
                  <input
                    id="likertScaleMin"
                    type="number"
                    min="0"
                    max={likertScaleMax - 1}
                    value={likertScaleMin}
                    onChange={(e) =>
                      setLikertScaleMin(parseInt(e.target.value))
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    disabled={isLoading}
                    placeholder="Min"
                  />
                </div>
                <div className="flex-1">
                  <label htmlFor="likertScaleMax" className="sr-only">
                    Maximum Value
                  </label>
                  <input
                    id="likertScaleMax"
                    type="number"
                    min={likertScaleMin + 1}
                    max="10"
                    value={likertScaleMax}
                    onChange={(e) =>
                      setLikertScaleMax(parseInt(e.target.value))
                    }
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    disabled={isLoading}
                    placeholder="Max"
                  />
                </div>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Scale will be from {likertScaleMin} to {likertScaleMax}
              </p>
            </div>

            <div>
              <label
                htmlFor="likertCaptions"
                className="block text-sm font-medium text-gray-700"
              >
                Likert Scale Captions
              </label>
              <input
                id="likertCaptions"
                type="text"
                value={likertCaptions}
                onChange={handleCaptionsChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                disabled={isLoading}
                placeholder="Enter captions as JSON array, e.g. ['Strongly Disagree', 'Disagree']"
              />
              <p className="mt-1 text-xs text-gray-500">
                Captions for each scale value (JSON array)
              </p>
            </div>
          </>
        )}

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button type="submit" variant="primary" disabled={isLoading}>
            {isLoading ? (
              <LoadingSpinner size="sm" />
            ) : question ? (
              "Update Question"
            ) : (
              "Add Question"
            )}
          </Button>
        </div>
      </form>
    </Card>
  );
}
