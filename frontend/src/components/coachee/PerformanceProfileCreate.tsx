import { useNavigate } from "react-router-dom";
import CreatePerformanceProfile from "./CreatePerformanceProfile";

const PerformanceProfileCreate = () => {
  const navigate = useNavigate();

  const handleSuccess = () => {
    navigate("/dashboard/performance-profile");
  };

  const handleCancel = () => {
    navigate("/dashboard/performance-profile");
  };

  return (
    <CreatePerformanceProfile
      onSuccess={handleSuccess}
      onCancel={handleCancel}
    />
  );
};

export default PerformanceProfileCreate;
