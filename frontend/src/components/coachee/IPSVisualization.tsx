import React, { useState, useEffect } from "react";
import { <PERSON>, CardHeader } from "../ui/Card";
import { PageHeader } from "../ui/PageHeader";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Button } from "../ui/Button";
import { LikertResponse } from "../ui/form/LikertResponse";
import {
  Calendar,
  Trophy,
  AlertCircle,
  Edit,
  Trash2,
  X,
  Save,
} from "lucide-react";
import {
  IPSChartData,
  IPSChartDataPoint,
  IPSRecord,
  UpdateIPSRecordRequest,
} from "../../types/api/ips.types";
import {
  getMyIPSTrends,
  getMyIPSRecords,
  updateIPSRecord,
  deleteIPSRecord,
} from "../../api/ips";
import { formatDateTimeLocal, formatDateTime } from "../../utils/dateUtils";
import {
  Line<PERSON>hart,
  Line,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";

// Quadratic regression for best fit curve (inverted U)
const getQuadraticFit = (dataPoints: IPSChartDataPoint[]) => {
  if (!dataPoints || dataPoints.length < 3) return [];
  // Least squares fit for y = ax^2 + bx + c
  const n = dataPoints.length;
  let sumX = 0,
    sumX2 = 0,
    sumX3 = 0,
    sumX4 = 0;
  let sumY = 0,
    sumXY = 0,
    sumX2Y = 0;
  for (const pt of dataPoints) {
    const x = pt.arousal;
    const y = pt.performance;
    sumX += x;
    sumX2 += x * x;
    sumX3 += x * x * x;
    sumX4 += x * x * x * x;
    sumY += y;
    sumXY += x * y;
    sumX2Y += x * x * y;
  }
  const A = [
    [n, sumX, sumX2],
    [sumX, sumX2, sumX3],
    [sumX2, sumX3, sumX4],
  ];
  const B = [sumY, sumXY, sumX2Y];
  function solve(A: number[][], B: number[]): number[] {
    const m = A.length;
    for (let k = 0; k < m; k++) {
      let maxRow = k;
      for (let i = k + 1; i < m; i++) {
        if (Math.abs(A[i][k]) > Math.abs(A[maxRow][k])) maxRow = i;
      }
      [A[k], A[maxRow]] = [A[maxRow], A[k]];
      [B[k], B[maxRow]] = [B[maxRow], B[k]];
      for (let i = k + 1; i < m; i++) {
        const f = A[i][k] / A[k][k];
        for (let j = k; j < m; j++) {
          A[i][j] -= f * A[k][j];
        }
        B[i] -= f * B[k];
      }
    }
    const x = Array(m).fill(0);
    for (let i = m - 1; i >= 0; i--) {
      x[i] = B[i];
      for (let j = i + 1; j < m; j++) {
        x[i] -= A[i][j] * x[j];
      }
      x[i] /= A[i][i];
    }
    return x;
  }
  const [c, b, a] = solve(A, B);
  // Generate curve points for arousal 0-10
  const curve = [];
  for (let x = 0; x <= 10; x += 0.2) {
    const y = a * x * x + b * x + c;
    curve.push({ arousal: x, performance: y });
  }
  return curve;
};

// Get magic number range (arousal range for optimal performance)
const getMagicNumberRange = (dataPoints: IPSChartDataPoint[]) => {
  if (!dataPoints || dataPoints.length < 3) return null;

  // Calculate standard deviation of arousal scores
  const arousalValues = dataPoints.map((pt) => pt.arousal);
  const arousalMean =
    arousalValues.reduce((sum, val) => sum + val, 0) / arousalValues.length;
  const arousalVariance =
    arousalValues.reduce(
      (sum, val) => sum + Math.pow(val - arousalMean, 2),
      0
    ) / arousalValues.length;
  const arousalStdDev = Math.sqrt(arousalVariance);

  // Least squares fit for y = ax^2 + bx + c
  const n = dataPoints.length;
  let sumX = 0,
    sumX2 = 0,
    sumX3 = 0,
    sumX4 = 0;
  let sumY = 0,
    sumXY = 0,
    sumX2Y = 0;
  for (const pt of dataPoints) {
    const x = pt.arousal;
    const y = pt.performance;
    sumX += x;
    sumX2 += x * x;
    sumX3 += x * x * x;
    sumX4 += x * x * x * x;
    sumY += y;
    sumXY += x * y;
    sumX2Y += x * x * y;
  }
  const A = [
    [n, sumX, sumX2],
    [sumX, sumX2, sumX3],
    [sumX2, sumX3, sumX4],
  ];
  const B = [sumY, sumXY, sumX2Y];
  function solve(A: number[][], B: number[]): number[] {
    const m = A.length;
    for (let k = 0; k < m; k++) {
      let maxRow = k;
      for (let i = k + 1; i < m; i++) {
        if (Math.abs(A[i][k]) > Math.abs(A[maxRow][k])) maxRow = i;
      }
      [A[k], A[maxRow]] = [A[maxRow], A[k]];
      [B[k], B[maxRow]] = [B[maxRow], B[k]];
      for (let i = k + 1; i < m; i++) {
        const f = A[i][k] / A[k][k];
        for (let j = k; j < m; j++) {
          A[i][j] -= f * A[k][j];
        }
        B[i] -= f * B[k];
      }
    }
    const x = Array(m).fill(0);
    for (let i = m - 1; i >= 0; i--) {
      x[i] = B[i];
      for (let j = i + 1; j < m; j++) {
        x[i] -= A[i][j] * x[j];
      }
      x[i] /= A[i][i];
    }
    return x;
  }
  const [_c, b, a] = solve(A, B);
  if (a === 0) return null;

  // Find the optimal arousal point (vertex of parabola)
  let optimalArousal = -b / (2 * a);

  // Calculate range using 0.1 standard deviations
  const rangeOffset = 0.1 * arousalStdDev;
  let lowerBound = optimalArousal - rangeOffset;
  let upperBound = optimalArousal + rangeOffset;

  // Clamp to [0, 10] range
  lowerBound = Math.max(0, Math.min(10, lowerBound));
  upperBound = Math.max(0, Math.min(10, upperBound));
  optimalArousal = Math.max(0, Math.min(10, optimalArousal));

  return {
    optimal: optimalArousal,
    lowerBound,
    upperBound,
    range: `${lowerBound.toFixed(1)}-${upperBound.toFixed(1)}`,
    display: `${optimalArousal.toFixed(2)} / ${lowerBound.toFixed(
      1
    )}-${upperBound.toFixed(1)}`,
  };
};

const IPSVisualization = () => {
  const [chartData, setChartData] = useState<IPSChartData | null>(null);
  const [ipsRecords, setIpsRecords] = useState<IPSRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    startDate: "",
    endDate: "",
  });
  const [editingRecord, setEditingRecord] = useState<IPSRecord | null>(null);
  const [editFormData, setEditFormData] = useState({
    competitionDateTime: new Date(),
    competitionName: "",
    performanceScore: 5,
    arousalScore: 5,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchIPSData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch both trends data and full records
      const [trendsData, recordsData] = await Promise.all([
        getMyIPSTrends(
          dateRange.startDate || undefined,
          dateRange.endDate || undefined
        ),
        getMyIPSRecords(100, 0), // Get up to 100 records
      ]);

      setIpsRecords(recordsData.ipsRecords);

      // Transform data for charts
      const dataPoints: IPSChartDataPoint[] = trendsData.map((record) => ({
        date: new Date(record.competitionDateTime).toLocaleDateString(),
        competitionName: record.competitionName,
        performance: record.performanceScore,
        arousal: record.arousalScore,
        timestamp: new Date(record.competitionDateTime).getTime(),
      }));

      // Sort by timestamp
      dataPoints.sort((a, b) => a.timestamp - b.timestamp);

      // Calculate averages
      const performanceAverage =
        dataPoints.length > 0
          ? dataPoints.reduce((sum, point) => sum + point.performance, 0) /
            dataPoints.length
          : 0;

      const arousalAverage =
        dataPoints.length > 0
          ? dataPoints.reduce((sum, point) => sum + point.arousal, 0) /
            dataPoints.length
          : 0;

      setChartData({
        dataPoints,
        performanceAverage,
        arousalAverage,
        totalRecords: dataPoints.length,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchIPSData();
  }, []);

  const handleDateRangeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateRange((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const applyDateFilter = () => {
    fetchIPSData();
  };

  const clearDateFilter = () => {
    setDateRange({ startDate: "", endDate: "" });
    setTimeout(() => fetchIPSData(), 0);
  };

  const handleEditRecord = (record: IPSRecord) => {
    setEditingRecord(record);
    setEditFormData({
      competitionDateTime: new Date(record.competitionDateTime),
      competitionName: record.competitionName,
      performanceScore: record.performanceScore,
      arousalScore: record.arousalScore,
    });
  };

  const handleCancelEdit = () => {
    setEditingRecord(null);
    setEditFormData({
      competitionDateTime: new Date(),
      competitionName: "",
      performanceScore: 5,
      arousalScore: 5,
    });
  };

  const handleSaveEdit = async () => {
    if (!editingRecord) return;

    try {
      setIsSubmitting(true);
      setError(null);

      const updateData: UpdateIPSRecordRequest = {
        competitionDateTime: editFormData.competitionDateTime.toISOString(),
        competitionName: editFormData.competitionName.trim(),
        performanceScore: editFormData.performanceScore,
        arousalScore: editFormData.arousalScore,
      };

      await updateIPSRecord(editingRecord.id, updateData);

      // Refresh data
      await fetchIPSData();
      handleCancelEdit();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to update record");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteRecord = async (record: IPSRecord) => {
    const confirmMessage = `Are you sure you want to delete the IPS record for "${record.competitionName}"?\n\nThis action cannot be undone.`;

    if (window.confirm(confirmMessage)) {
      try {
        setError(null);
        await deleteIPSRecord(record.id);
        await fetchIPSData(); // Refresh data
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to delete record"
        );
      }
    }
  };

  const handleEditFormChange = (field: string, value: any) => {
    setEditFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold">{data.competitionName}</p>
          <p className="text-sm text-gray-600">{label}</p>
          <p className="text-blue-600">Performance: {data.performance}/10</p>
          <p className="text-red-600">Arousal: {data.arousal}/10</p>
        </div>
      );
    }
    return null;
  };

  const CustomScatterTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold">{data.competitionName}</p>
          <p className="text-sm text-gray-600">{data.date}</p>
          <p className="text-blue-600">Performance: {data.performance}/10</p>
          <p className="text-red-600">Arousal: {data.arousal}/10</p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="IPS Visualization"
        description="Analyze your Ideal Performance State trends over time"
      />

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Date Range Filter */}
      <Card>
        <CardHeader title="Filter by Date Range" />
        <div className="p-6 pt-0">
          <div className="flex flex-wrap items-end gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                name="startDate"
                value={dateRange.startDate}
                onChange={handleDateRangeChange}
                className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                name="endDate"
                value={dateRange.endDate}
                onChange={handleDateRangeChange}
                className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <button
              onClick={applyDateFilter}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              Apply Filter
            </button>
            <button
              onClick={clearDateFilter}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Clear Filter
            </button>
          </div>
        </div>
      </Card>

      {chartData && chartData.totalRecords > 0 ? (
        <>
          {/* Summary Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Magic Number Card */}
            {(() => {
              const magicRange = getMagicNumberRange(chartData.dataPoints);
              return (
                <>
                  <Card className="p-6">
                    <div className="flex items-center">
                      <Trophy className="h-8 w-8 text-yellow-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">
                          Magic Number
                        </p>
                        <p className="text-2xl font-semibold text-gray-900">
                          {magicRange !== null
                            ? magicRange.optimal?.toFixed(2)
                            : "-"}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Optimal arousal
                        </p>
                      </div>
                    </div>
                  </Card>
                  <Card className="p-6">
                    <div className="flex items-center">
                      <Trophy className="h-8 w-8 text-yellow-500" />
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">
                          IZOF
                        </p>
                        <p className="text-2xl font-semibold text-gray-900">
                          {magicRange !== null ? magicRange.range : "-"}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Individual Zone of Optimal Functioning
                        </p>
                      </div>
                    </div>
                  </Card>
                </>
              );
            })()}

            <Card className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Total Records
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {chartData.totalRecords}
                  </p>
                </div>
              </div>
            </Card>
          </div>
          {/* Scatter Plot - Performance vs Arousal */}
          <Card>
            <CardHeader title="Arousal vs Performance Relationship" />
            <div className="p-6 pt-0">
              <ResponsiveContainer width="100%" height={400}>
                <ScatterChart data={chartData.dataPoints}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="arousal"
                    type="number"
                    tickCount={11}
                    domain={[0, 10]}
                    name="Arousal Score"
                    label={{
                      value: "Arousal Score",
                      position: "insideBottom",
                      offset: -10,
                    }}
                  />
                  <YAxis
                    dataKey="performance"
                    allowDataOverflow
                    domain={[0, 10]}
                    tickCount={6}
                    tickFormatter={(t) => t.toFixed(2)}
                    name="Performance Score"
                    label={{
                      value: "Performance Score",
                      angle: -90,
                      position: "insideLeft",
                    }}
                  />
                  <Tooltip content={<CustomScatterTooltip />} />
                  <Scatter
                    data={chartData.dataPoints}
                    fill="#8884d8"
                    name="Competitions"
                  />
                  {/* Quadratic best fit line */}
                  {chartData.dataPoints.length >= 3 && (
                    <Line
                      type="monotone"
                      data={getQuadraticFit(chartData.dataPoints)}
                      dataKey="performance"
                      dot={false}
                      stroke="#F59E42"
                      strokeWidth={3}
                      name="Quadratic Fit"
                      legendType="none"
                      isAnimationActive={false}
                    />
                  )}
                </ScatterChart>
              </ResponsiveContainer>
              <p className="text-sm text-gray-600 mt-4">
                This chart shows the relationship between your arousal and
                performance levels. Look for patterns that indicate your optimal
                arousal zone for peak performance.
              </p>
            </div>
          </Card>

          {/* Line Chart - Trends Over Time */}
          <Card>
            <CardHeader title="Performance & Arousal Trends" />
            <div className="p-6 pt-0">
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={chartData.dataPoints}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis domain={[0, 10]} />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="performance"
                    stroke="#3B82F6"
                    strokeWidth={2}
                    name="Performance Score"
                    dot={{ fill: "#3B82F6", strokeWidth: 2, r: 4 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="arousal"
                    stroke="#EF4444"
                    strokeWidth={2}
                    name="Arousal Score"
                    dot={{ fill: "#EF4444", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>

          {/* IPS Records List */}
          <Card>
            <CardHeader title="All IPS Records" />
            <div className="p-6 pt-0">
              {ipsRecords.length > 0 ? (
                <div className="space-y-4">
                  {ipsRecords
                    .sort(
                      (a, b) =>
                        new Date(b.competitionDateTime).getTime() -
                        new Date(a.competitionDateTime).getTime()
                    )
                    .map((record) => (
                      <div
                        key={record.id}
                        className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                      >
                        {editingRecord?.id === record.id ? (
                          // Edit Mode
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <h4 className="text-lg font-medium text-gray-900">
                                Edit IPS Record
                              </h4>
                              <div className="flex items-center space-x-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={handleCancelEdit}
                                  disabled={isSubmitting}
                                >
                                  <X className="h-4 w-4 mr-1" />
                                  Cancel
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={handleSaveEdit}
                                  disabled={isSubmitting}
                                >
                                  <Save className="h-4 w-4 mr-1" />
                                  {isSubmitting ? "Saving..." : "Save"}
                                </Button>
                              </div>
                            </div>

                            {/* Competition Date & Time */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Competition Date & Time
                              </label>
                              <input
                                type="datetime-local"
                                value={formatDateTimeLocal(
                                  editFormData.competitionDateTime
                                )}
                                onChange={(e) =>
                                  handleEditFormChange(
                                    "competitionDateTime",
                                    new Date(e.target.value)
                                  )
                                }
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                disabled={isSubmitting}
                              />
                            </div>

                            {/* Competition Name */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Competition Name
                              </label>
                              <input
                                type="text"
                                value={editFormData.competitionName}
                                onChange={(e) =>
                                  handleEditFormChange(
                                    "competitionName",
                                    e.target.value
                                  )
                                }
                                placeholder="e.g., Regional Championship, Practice Match"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                disabled={isSubmitting}
                              />
                            </div>

                            {/* Performance Score */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Performance Score (0-10)
                              </label>
                              <p className="text-sm text-gray-500 mb-3">
                                Rate your overall performance level during the
                                competition
                              </p>
                              <LikertResponse
                                value={editFormData.performanceScore}
                                scaleMin={0}
                                scaleMax={10}
                                labels={{
                                  0: "Very Poor",
                                  2: "Poor",
                                  4: "Below Average",
                                  5: "Average",
                                  6: "Above Average",
                                  8: "Good",
                                  10: "Excellent",
                                }}
                                onChange={(value) =>
                                  handleEditFormChange(
                                    "performanceScore",
                                    value as number
                                  )
                                }
                                isCompleted={false}
                                saving={isSubmitting}
                              />
                            </div>

                            {/* Arousal Score */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Arousal Score (0-10)
                              </label>
                              <p className="text-sm text-gray-500 mb-3">
                                Rate your energy/activation level during the
                                competition
                              </p>
                              <LikertResponse
                                value={editFormData.arousalScore}
                                scaleMin={0}
                                scaleMax={10}
                                labels={{
                                  0: "Very Low",
                                  2: "Low",
                                  4: "Below Average",
                                  5: "Average",
                                  6: "Above Average",
                                  8: "High",
                                  10: "Very High",
                                }}
                                onChange={(value) =>
                                  handleEditFormChange(
                                    "arousalScore",
                                    value as number
                                  )
                                }
                                isCompleted={false}
                                saving={isSubmitting}
                              />
                            </div>
                          </div>
                        ) : (
                          // View Mode
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-4 mb-2">
                                <h4 className="text-lg font-medium text-gray-900">
                                  {record.competitionName}
                                </h4>
                                <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                  Performance: {record.performanceScore}/10
                                </span>
                                <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                  Arousal: {record.arousalScore}/10
                                </span>
                              </div>
                              <div className="text-sm text-gray-600 space-y-1">
                                <p>
                                  <Calendar className="inline h-4 w-4 mr-1" />
                                  Competition:{" "}
                                  {formatDateTime(record.competitionDateTime)}
                                </p>
                                <p>
                                  <Trophy className="inline h-4 w-4 mr-1" />
                                  Recorded: {formatDateTime(record.dateTime)}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2 ml-4">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditRecord(record)}
                              >
                                <Edit className="h-4 w-4 mr-1" />
                                Edit
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDeleteRecord(record)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4 mr-1" />
                                Delete
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No IPS Records Found
                  </h3>
                  <p className="text-gray-600">
                    Create your first IPS record to start tracking your
                    performance.
                  </p>
                </div>
              )}
            </div>
          </Card>
        </>
      ) : (
        <Card className="p-8 text-center">
          <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No IPS Records Found
          </h3>
          <p className="text-gray-600">
            Start creating IPS records to see your performance trends and
            analysis here.
          </p>
        </Card>
      )}
    </div>
  );
};

export default IPSVisualization;
