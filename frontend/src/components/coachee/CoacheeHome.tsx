import { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { useCoachee } from "../../context/CoacheeContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Card, CardHeader } from "../ui/Card";
import { PageHeader } from "../ui/PageHeader";
import { 
  BookOpen, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Calendar,
  TrendingUp
} from "lucide-react";

const CoacheeHome = () => {
  const {
    myAssignments,
    loading,
    fetchMyAssignments,
  } = useCoachee();

  useEffect(() => {
    fetchMyAssignments();
  }, []);

  if (loading.assignments) {
    return <LoadingSpinner />;
  }

  // Calculate statistics
  const totalAssignments = myAssignments.length;
  const pendingAssignments = myAssignments.filter(a => a.status === 'PENDING').length;
  const inProgressAssignments = myAssignments.filter(a => a.status === 'IN_PROGRESS').length;
  const completedAssignments = myAssignments.filter(a => a.status === 'COMPLETED').length;
  const overdueAssignments = myAssignments.filter(a => a.status === 'OVERDUE').length;

  // Get upcoming assignments (due within 7 days)
  const upcomingAssignments = myAssignments.filter(assignment => {
    if (!assignment.dueDate) return false;
    const dueDate = new Date(assignment.dueDate);
    const now = new Date();
    const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    return dueDate >= now && dueDate <= sevenDaysFromNow && assignment.status !== 'COMPLETED';
  });

  return (
    <div className="space-y-6">
      <PageHeader
        title="My Dashboard"
        description="Track your assignments and progress"
      />

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Assignments</p>
              <p className="text-2xl font-semibold text-gray-900">{totalAssignments}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <Clock className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-semibold text-gray-900">{pendingAssignments + inProgressAssignments}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-semibold text-gray-900">{completedAssignments}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Overdue</p>
              <p className="text-2xl font-semibold text-gray-900">{overdueAssignments}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <div className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link
              to="/dashboard/assignments"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <BookOpen className="h-6 w-6 text-blue-600 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-gray-900">View All Assignments</h3>
                <p className="text-sm text-gray-500">See all your assignments</p>
              </div>
            </Link>

            <Link
              to="/dashboard/progress"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <TrendingUp className="h-6 w-6 text-green-600 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-gray-900">View Progress</h3>
                <p className="text-sm text-gray-500">Track your learning progress</p>
              </div>
            </Link>
          </div>
        </div>
      </Card>

      {/* Upcoming Assignments */}
      {upcomingAssignments.length > 0 && (
        <Card>
          <CardHeader title="Upcoming Assignments" />
          <div className="p-6 pt-0">
            <div className="space-y-3">
              {upcomingAssignments.slice(0, 5).map((assignment) => (
                <div
                  key={assignment.id}
                  className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                >
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-yellow-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {assignment.exercise.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        Due: {assignment.dueDate ? new Date(assignment.dueDate).toLocaleDateString() : 'No due date'}
                      </p>
                    </div>
                  </div>
                  <Link
                    to={`/dashboard/assignments/${assignment.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Start
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}

      {/* Recent Assignments */}
      <Card>
        <CardHeader title="Recent Assignments" />
        <div className="p-6 pt-0">
          {myAssignments.length === 0 ? (
            <p className="text-gray-500 text-center py-8">
              No assignments yet. Your coach will assign exercises for you to complete.
            </p>
          ) : (
            <div className="space-y-3">
              {myAssignments.slice(0, 5).map((assignment) => (
                <div
                  key={assignment.id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                >
                  <div className="flex items-center">
                    <div className={`h-3 w-3 rounded-full mr-3 ${
                      assignment.status === 'COMPLETED' ? 'bg-green-500' :
                      assignment.status === 'OVERDUE' ? 'bg-red-500' :
                      assignment.status === 'IN_PROGRESS' ? 'bg-blue-500' :
                      'bg-gray-400'
                    }`} />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {assignment.exercise.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        Status: {assignment.status.replace('_', ' ').toLowerCase()}
                      </p>
                    </div>
                  </div>
                  <Link
                    to={`/dashboard/assignments/${assignment.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    {assignment.status === 'COMPLETED' ? 'View' : 'Continue'}
                  </Link>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default CoacheeHome;
