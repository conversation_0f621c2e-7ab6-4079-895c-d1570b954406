import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useCoachee } from "../../context/CoacheeContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Card, CardHeader } from "../ui/Card";
import { PageHeader } from "../ui/PageHeader";
import { Button } from "../ui/Button";
import { DataTable } from "../ui/DataTable";
import { Column } from "../../types/components.types";
import { Assignment } from "../../types/api/assignments.types";
import {
  Calendar,
  CheckCircle,
  Clock,
  AlertTriangle,
  BookOpen,
  Filter,
} from "lucide-react";

type FilterStatus = "ALL" | "PENDING" | "IN_PROGRESS" | "COMPLETED" | "OVERDUE";

const CoacheeAssignments = () => {
  const { myAssignments, loading, fetchMyAssignments } = useCoachee();
  const [filter, setFilter] = useState<FilterStatus>("ALL");

  useEffect(() => {
    fetchMyAssignments();
  }, []);

  if (loading.assignments) {
    return <LoadingSpinner />;
  }

  // Filter assignments based on selected filter
  const filteredAssignments = myAssignments.filter((assignment) => {
    if (filter === "ALL") return true;
    return assignment.status === filter;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "OVERDUE":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "IN_PROGRESS":
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses =
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    switch (status) {
      case "COMPLETED":
        return `${baseClasses} bg-green-100 text-green-800`;
      case "OVERDUE":
        return `${baseClasses} bg-red-100 text-red-800`;
      case "IN_PROGRESS":
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case "PENDING":
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const columns: Column<Assignment>[] = [
    {
      header: "Exercise",
      accessorKey: "exercise",
      cell: (assignment) => (
        <div className="flex items-center">
          <BookOpen className="h-4 w-4 text-gray-400 mr-2" />
          <div>
            <p className="text-sm font-medium text-gray-900">
              {assignment.exercise.name}
            </p>
            <div className="text-xs text-gray-500">
              {assignment.exercise.description
                ? (() => {
                    // Strip HTML tags for table preview
                    const plainText = assignment.exercise.description.replace(
                      /<[^>]*>/g,
                      ""
                    );
                    return plainText.length > 60
                      ? `${plainText.substring(0, 60)}...`
                      : plainText;
                  })()
                : "No description"}
            </div>
          </div>
        </div>
      ),
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: (assignment) => (
        <div className="flex items-center">
          {getStatusIcon(assignment.status)}
          <span className={`ml-2 ${getStatusBadge(assignment.status)}`}>
            {assignment.status.replace("_", " ")}
          </span>
        </div>
      ),
    },
    {
      header: "Due Date",
      accessorKey: "dueDate",
      cell: (assignment) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm text-gray-900">
            {assignment.dueDate
              ? new Date(assignment.dueDate).toLocaleDateString()
              : "No due date"}
          </span>
        </div>
      ),
    },
    {
      header: "Created",
      accessorKey: "createdAt",
      cell: (assignment) => (
        <span className="text-sm text-gray-500">
          {new Date(assignment.createdAt).toLocaleDateString()}
        </span>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (assignment) => (
        <Link to={`/dashboard/assignments/${assignment.id}`}>
          <Button size="sm" variant="outline">
            {assignment.status === "COMPLETED" ? "View" : "Start"}
          </Button>
        </Link>
      ),
    },
  ];

  const filterButtons: { label: string; value: FilterStatus; count: number }[] =
    [
      { label: "All", value: "ALL", count: myAssignments.length },
      {
        label: "Pending",
        value: "PENDING",
        count: myAssignments.filter((a) => a.status === "PENDING").length,
      },
      {
        label: "In Progress",
        value: "IN_PROGRESS",
        count: myAssignments.filter((a) => a.status === "IN_PROGRESS").length,
      },
      {
        label: "Completed",
        value: "COMPLETED",
        count: myAssignments.filter((a) => a.status === "COMPLETED").length,
      },
      {
        label: "Overdue",
        value: "OVERDUE",
        count: myAssignments.filter((a) => a.status === "OVERDUE").length,
      },
    ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="My Assignments"
        description="View and complete your assigned exercises"
      />

      {/* Filter Buttons */}
      <Card>
        <CardHeader title="Filter Assignments" />
        <div className="p-6 pt-0">
          <div className="flex flex-wrap gap-2">
            {filterButtons.map((button) => (
              <Button
                key={button.value}
                variant={filter === button.value ? "primary" : "outline"}
                size="sm"
                onClick={() => setFilter(button.value)}
                className="flex items-center"
              >
                <Filter className="h-3 w-3 mr-1" />
                {button.label} ({button.count})
              </Button>
            ))}
          </div>
        </div>
      </Card>

      {/* Assignments Table */}
      <Card>
        <CardHeader
          title={`${
            filter === "ALL" ? "All" : filter.replace("_", " ")
          } Assignments`}
        />
        <div className="p-6 pt-0">
          {filteredAssignments.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {filter === "ALL"
                  ? "No assignments yet"
                  : `No ${filter.toLowerCase().replace("_", " ")} assignments`}
              </h3>
              <p className="text-gray-500">
                {filter === "ALL"
                  ? "Your coach will assign exercises for you to complete."
                  : `You don't have any ${filter
                      .toLowerCase()
                      .replace("_", " ")} assignments at the moment.`}
              </p>
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={filteredAssignments}
              className="w-full"
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default CoacheeAssignments;
