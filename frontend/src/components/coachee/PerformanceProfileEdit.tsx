import { useNavigate, useParams } from "react-router-dom";
import EditPerformanceProfile from "./EditPerformanceProfile";

const PerformanceProfileEdit = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const handleSuccess = () => {
    navigate("/dashboard/performance-profile");
  };

  const handleCancel = () => {
    navigate("/dashboard/performance-profile");
  };

  if (!id) {
    navigate("/dashboard/performance-profile");
    return null;
  }

  return (
    <EditPerformanceProfile
      profileId={id}
      onSuccess={handleSuccess}
      onCancel={handleCancel}
    />
  );
};

export default PerformanceProfileEdit;
