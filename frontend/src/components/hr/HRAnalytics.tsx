import { useEffect, useState } from "react";
import { useHR } from "../../context/HRContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Card, CardHeader } from "../ui/Card";
import { PageHeader } from "../ui/PageHeader";
import { Button } from "../ui/Button";
import { DataTable } from "../ui/DataTable";
import { Column } from "../../types/components.types";
import { Assignment } from "../../types/api/assignments.types";
import SelectInput from "../ui/input/SelectInput";
import {
  BarChart3,
  TrendingUp,
  Users,
  CheckCircle,
  Clock,
  AlertTriangle,
  Calendar,
  Download,
  Filter,
} from "lucide-react";

type TimeFrame = "week" | "month" | "quarter" | "year";

const HRAnalytics = () => {
  const { organization, assignments, assignmentStats, loading, refreshData } =
    useHR();

  const [timeFrame, setTimeFrame] = useState<TimeFrame>("month");
  const [filterStatus, setFilterStatus] = useState<string>("ALL");

  useEffect(() => {
    refreshData();
  }, []);

  if (loading.organization) {
    return <LoadingSpinner />;
  }

  // Filter assignments based on status
  const filteredAssignments = assignments.filter((assignment) => {
    if (filterStatus === "ALL") return true;
    return assignment.status === filterStatus;
  });

  // Calculate coach performance metrics
  const coachPerformance = assignments.reduce((acc, assignment) => {
    const coachId = assignment.coachId;
    if (!acc[coachId]) {
      acc[coachId] = {
        coachId,
        coachName: `${assignment.coachee?.firstName || ""} ${
          assignment.coachee?.lastName || ""
        }`,
        totalAssignments: 0,
        completedAssignments: 0,
        completionRate: 0,
      };
    }
    acc[coachId].totalAssignments++;
    if (assignment.status === "COMPLETED") {
      acc[coachId].completedAssignments++;
    }
    acc[coachId].completionRate = Math.round(
      (acc[coachId].completedAssignments / acc[coachId].totalAssignments) * 100
    );
    return acc;
  }, {} as Record<string, any>);

  const coachPerformanceData = Object.values(coachPerformance);

  const assignmentColumns: Column<Assignment>[] = [
    {
      header: "Exercise",
      accessorKey: "exercise",
      cell: (assignment) => (
        <div>
          <p className="text-sm font-medium text-gray-900">
            {assignment.exercise.name}
          </p>
          <p className="text-xs text-gray-500">
            {assignment.exercise.description}
          </p>
        </div>
      ),
    },
    {
      header: "Coachee",
      accessorKey: "coachee",
      cell: (assignment) => (
        <div>
          <p className="text-sm text-gray-900">
            {assignment.coachee?.firstName} {assignment.coachee?.lastName}
          </p>
          <p className="text-xs text-gray-500">{assignment.coachee?.email}</p>
        </div>
      ),
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: (assignment) => {
        const statusConfig = {
          PENDING: { color: "yellow", icon: Clock },
          IN_PROGRESS: { color: "blue", icon: TrendingUp },
          COMPLETED: { color: "green", icon: CheckCircle },
          OVERDUE: { color: "red", icon: AlertTriangle },
        };
        const config =
          statusConfig[assignment.status as keyof typeof statusConfig];
        const Icon = config?.icon || Clock;

        return (
          <div className="flex items-center">
            <Icon className={`h-4 w-4 text-${config?.color}-600 mr-2`} />
            <span className={`text-sm text-${config?.color}-700`}>
              {assignment.status.replace("_", " ")}
            </span>
          </div>
        );
      },
    },
    {
      header: "Due Date",
      accessorKey: "dueDate",
      cell: (assignment) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm text-gray-500">
            {assignment.dueDate
              ? new Date(assignment.dueDate).toLocaleDateString()
              : "No due date"}
          </span>
        </div>
      ),
    },
    {
      header: "Created",
      accessorKey: "createdAt",
      cell: (assignment) => (
        <span className="text-sm text-gray-500">
          {new Date(assignment.createdAt).toLocaleDateString()}
        </span>
      ),
    },
  ];

  const coachColumns: Column<any>[] = [
    {
      header: "Coach",
      accessorKey: "coachName",
      cell: (coach) => (
        <div className="flex items-center">
          <Users className="h-4 w-4 text-blue-600 mr-2" />
          <span className="text-sm font-medium text-gray-900">
            {coach.coachName}
          </span>
        </div>
      ),
    },
    {
      header: "Total Assignments",
      accessorKey: "totalAssignments",
      cell: (coach) => (
        <span className="text-sm text-gray-900">{coach.totalAssignments}</span>
      ),
    },
    {
      header: "Completed",
      accessorKey: "completedAssignments",
      cell: (coach) => (
        <span className="text-sm text-green-600">
          {coach.completedAssignments}
        </span>
      ),
    },
    {
      header: "Completion Rate",
      accessorKey: "completionRate",
      cell: (coach) => (
        <div className="flex items-center">
          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
            <div
              className="bg-green-600 h-2 rounded-full"
              style={{ width: `${coach.completionRate}%` }}
            ></div>
          </div>
          <span className="text-sm text-gray-900">{coach.completionRate}%</span>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title={
          organization
            ? `${organization.name} - Analytics`
            : "Analytics & Reports"
        }
        description="View assignment analytics and performance metrics"
      />

      {/* Filters and Export */}
      <Card>
        <CardHeader title="Filters & Export" />
        <div className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectInput
              label="Time Frame"
              options={[
                { value: "week", label: "Last Week" },
                { value: "month", label: "Last Month" },
                { value: "quarter", label: "Last Quarter" },
                { value: "year", label: "Last Year" },
              ]}
              value={timeFrame}
              onChange={(value) => setTimeFrame(value as TimeFrame)}
            />
            <div className="flex items-end">
              <Button className="w-full" disabled={!organization}>
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
            </div>
          </div>
        </div>
      </Card>

      {organization && (
        <>
          {/* Statistics Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="p-6">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Total Assignments
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {assignmentStats.total}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {assignmentStats.completed}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {assignmentStats.pending + assignmentStats.inProgress}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Completion Rate
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {assignmentStats.completionRate}%
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Coach Performance */}
          <Card>
            <CardHeader title="Coach Performance" />
            <div className="p-6 pt-0">
              {coachPerformanceData.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No performance data
                  </h3>
                  <p className="text-gray-500">
                    No assignments found for this organization.
                  </p>
                </div>
              ) : (
                <DataTable
                  columns={coachColumns}
                  data={coachPerformanceData}
                  className="w-full"
                />
              )}
            </div>
          </Card>

          {/* Assignment Details */}
          <Card>
            <CardHeader title="Assignment Details" />
            <div className="p-6 pt-0">
              <div className="flex justify-between items-center mb-4">
                <div className="flex space-x-2">
                  <Button
                    variant={filterStatus === "ALL" ? "primary" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus("ALL")}
                  >
                    <Filter className="h-3 w-3 mr-1" />
                    All ({assignments.length})
                  </Button>
                  <Button
                    variant={filterStatus === "PENDING" ? "primary" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus("PENDING")}
                  >
                    Pending ({assignmentStats.pending})
                  </Button>
                  <Button
                    variant={
                      filterStatus === "COMPLETED" ? "primary" : "outline"
                    }
                    size="sm"
                    onClick={() => setFilterStatus("COMPLETED")}
                  >
                    Completed ({assignmentStats.completed})
                  </Button>
                  <Button
                    variant={filterStatus === "OVERDUE" ? "primary" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus("OVERDUE")}
                  >
                    Overdue ({assignmentStats.overdue})
                  </Button>
                </div>
              </div>

              {filteredAssignments.length === 0 ? (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No assignments found
                  </h3>
                  <p className="text-gray-500">
                    No assignments match the selected filter.
                  </p>
                </div>
              ) : (
                <DataTable
                  columns={assignmentColumns}
                  data={filteredAssignments}
                  className="w-full"
                />
              )}
            </div>
          </Card>
        </>
      )}

      {!organization && (
        <Card>
          <div className="p-12 text-center">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Organization Assigned
            </h3>
            <p className="text-gray-500">
              You haven't been assigned to an organization yet. Contact your
              administrator.
            </p>
          </div>
        </Card>
      )}
    </div>
  );
};

export default HRAnalytics;
