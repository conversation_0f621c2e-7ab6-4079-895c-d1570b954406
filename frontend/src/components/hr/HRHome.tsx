import { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { useHR } from "../../context/HRContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Card, CardHeader } from "../ui/Card";
import { PageHeader } from "../ui/PageHeader";
import {
  Building2,
  Users,
  UserCheck,
  BookOpen,
  TrendingUp,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
} from "lucide-react";

const HRHome = () => {
  const {
    organization,
    coaches,
    coachees,
    assignmentStats,
    loading,
    refreshData,
  } = useHR();

  useEffect(() => {
    refreshData();
  }, []);

  if (loading.organization) {
    return <LoadingSpinner />;
  }

  // Calculate stats for the organization
  const totalCoaches = coaches.length;
  const totalCoachees = coachees.length;

  return (
    <div className="space-y-6">
      <PageHeader
        title={
          organization ? `${organization.name} - HR Dashboard` : "HR Dashboard"
        }
        description="Manage your organization's coaches and coachees"
      />

      {/* Organization Info */}
      {organization && (
        <Card>
          <CardHeader title="Organization Overview" />
          <div className="p-6 pt-0">
            <div className="flex items-center">
              <Building2 className="h-6 w-6 text-blue-600 mr-3" />
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  {organization.name}
                </h3>
                <p className="text-sm text-gray-500">
                  {organization.description || "No description available"}
                </p>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <UserCheck className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Coaches</p>
              <p className="text-2xl font-semibold text-gray-900">
                {totalCoaches}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Total Coachees
              </p>
              <p className="text-2xl font-semibold text-gray-900">
                {totalCoachees}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Total Assignments
              </p>
              <p className="text-2xl font-semibold text-gray-900">
                {assignmentStats.total}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Assignment Overview */}
      {assignmentStats.total > 0 && (
        <Card>
          <CardHeader title="Assignment Overview" />
          <div className="p-6 pt-0">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center p-3 bg-yellow-50 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-yellow-900">Pending</p>
                  <p className="text-lg font-semibold text-yellow-700">
                    {assignmentStats.pending}
                  </p>
                </div>
              </div>

              <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                <TrendingUp className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-blue-900">
                    In Progress
                  </p>
                  <p className="text-lg font-semibold text-blue-700">
                    {assignmentStats.inProgress}
                  </p>
                </div>
              </div>

              <div className="flex items-center p-3 bg-green-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-green-900">
                    Completed
                  </p>
                  <p className="text-lg font-semibold text-green-700">
                    {assignmentStats.completed}
                  </p>
                </div>
              </div>

              <div className="flex items-center p-3 bg-red-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-red-900">Overdue</p>
                  <p className="text-lg font-semibold text-red-700">
                    {assignmentStats.overdue}
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Completion Rate
                </span>
                <span className="text-lg font-semibold text-gray-900">
                  {assignmentStats.completionRate}%
                </span>
              </div>
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full"
                  style={{ width: `${assignmentStats.completionRate}%` }}
                ></div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <div className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link
              to="/dashboard/members"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Users className="h-6 w-6 text-green-600 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-gray-900">
                  Manage Members
                </h3>
                <p className="text-sm text-gray-500">
                  Add/remove coaches and coachees
                </p>
              </div>
            </Link>

            <Link
              to="/dashboard/analytics"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <BarChart3 className="h-6 w-6 text-purple-600 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-gray-900">
                  View Analytics
                </h3>
                <p className="text-sm text-gray-500">
                  Assignment and performance analytics
                </p>
              </div>
            </Link>
          </div>
        </div>
      </Card>

      {/* Recent Activity or Members Summary */}
      {organization && (
        <Card>
          <CardHeader title="Organization Summary" />
          <div className="p-6 pt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">
                  Coaches
                </h4>
                {coaches.length === 0 ? (
                  <p className="text-sm text-gray-500">
                    No coaches assigned yet
                  </p>
                ) : (
                  <div className="space-y-2">
                    {coaches.slice(0, 3).map((coach) => (
                      <div key={coach.id} className="flex items-center">
                        <UserCheck className="h-4 w-4 text-green-600 mr-2" />
                        <span className="text-sm text-gray-900">
                          {coach.firstName} {coach.lastName}
                        </span>
                      </div>
                    ))}
                    {coaches.length > 3 && (
                      <p className="text-xs text-gray-500">
                        +{coaches.length - 3} more coaches
                      </p>
                    )}
                  </div>
                )}
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">
                  Coachees
                </h4>
                {coachees.length === 0 ? (
                  <p className="text-sm text-gray-500">
                    No coachees assigned yet
                  </p>
                ) : (
                  <div className="space-y-2">
                    {coachees.slice(0, 3).map((coachee) => (
                      <div key={coachee.id} className="flex items-center">
                        <Users className="h-4 w-4 text-blue-600 mr-2" />
                        <span className="text-sm text-gray-900">
                          {coachee.firstName} {coachee.lastName}
                        </span>
                      </div>
                    ))}
                    {coachees.length > 3 && (
                      <p className="text-xs text-gray-500">
                        +{coachees.length - 3} more coachees
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>
      )}

      {!organization && (
        <Card>
          <div className="p-12 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No organization assigned
            </h3>
            <p className="text-gray-500">
              You haven't been assigned to an organization yet. Contact your
              administrator.
            </p>
          </div>
        </Card>
      )}
    </div>
  );
};

export default HRHome;
