import { useEffect, useState } from "react";
import { useHR } from "../../context/HRContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { Card, CardHeader } from "../ui/Card";
import { PageHeader } from "../ui/PageHeader";
import { Button } from "../ui/Button";
import { DataTable } from "../ui/DataTable";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Column } from "../../types/components.types";
import { User } from "../../types/api/user.types";
import { AutocompleteDropdown } from "../ui/input/AutocompleteDropdown";
import { 
  Users, 
  UserCheck, 
  UserPlus,
  UserMinus,
  Mail,
  Building2
} from "lucide-react";

const HRMembers = () => {
  const {
    organization,
    coaches,
    coachees,
    allUsers,
    loading,
    refreshData,
    addCoach,
    addCoachee,
    removeCoach,
    removeCoachee,
  } = useHR();

  const [showAddCoach, setShowAddCoach] = useState(false);
  const [showAddCoachee, setShowAddCoachee] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    refreshData();
  }, []);

  if (loading.organization) {
    return <LoadingSpinner />;
  }

  if (!organization) {
    return (
      <div className="text-center py-12">
        <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No organization assigned</h3>
        <p className="text-gray-500 mb-4">You haven't been assigned to an organization yet.</p>
      </div>
    );
  }

  // Get available users for adding
  const existingCoachIds = coaches.map(c => c.id);
  const existingCoacheeIds = coachees.map(c => c.id);
  
  const availableCoaches = allUsers.filter(user => 
    user.role === 'COACH' && !existingCoachIds.includes(user.id)
  );
  const availableCoachees = allUsers.filter(user => 
    user.role === 'COACHEE' && !existingCoacheeIds.includes(user.id)
  );

  const handleAddCoach = async () => {
    if (!selectedUser) return;
    
    setError(null);
    setSuccess(null);
    
    try {
      await addCoach(selectedUser);
      setSuccess("Coach added successfully!");
      setSelectedUser("");
      setShowAddCoach(false);
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to add coach");
    }
  };

  const handleAddCoachee = async () => {
    if (!selectedUser) return;
    
    setError(null);
    setSuccess(null);
    
    try {
      await addCoachee(selectedUser);
      setSuccess("Coachee added successfully!");
      setSelectedUser("");
      setShowAddCoachee(false);
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to add coachee");
    }
  };

  const handleRemoveCoach = async (userId: string) => {
    setError(null);
    setSuccess(null);
    
    try {
      await removeCoach(userId);
      setSuccess("Coach removed successfully!");
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to remove coach");
    }
  };

  const handleRemoveCoachee = async (userId: string) => {
    setError(null);
    setSuccess(null);
    
    try {
      await removeCoachee(userId);
      setSuccess("Coachee removed successfully!");
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to remove coachee");
    }
  };

  const coachColumns: Column<User>[] = [
    {
      header: "Coach",
      accessorKey: "firstName",
      cell: (user) => (
        <div className="flex items-center">
          <UserCheck className="h-4 w-4 text-green-600 mr-2" />
          <div>
            <p className="text-sm font-medium text-gray-900">
              {user.firstName} {user.lastName}
            </p>
            <p className="text-xs text-gray-500">{user.email}</p>
          </div>
        </div>
      ),
    },
    {
      header: "Email",
      accessorKey: "email",
      cell: (user) => (
        <div className="flex items-center">
          <Mail className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm text-gray-900">{user.email}</span>
        </div>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (user) => (
        <Button
          size="sm"
          variant="outline"
          onClick={() => handleRemoveCoach(user.id)}
          className="text-red-600 hover:text-red-800"
        >
          <UserMinus className="h-3 w-3 mr-1" />
          Remove
        </Button>
      ),
    },
  ];

  const coacheeColumns: Column<User>[] = [
    {
      header: "Coachee",
      accessorKey: "firstName",
      cell: (user) => (
        <div className="flex items-center">
          <Users className="h-4 w-4 text-blue-600 mr-2" />
          <div>
            <p className="text-sm font-medium text-gray-900">
              {user.firstName} {user.lastName}
            </p>
            <p className="text-xs text-gray-500">{user.email}</p>
          </div>
        </div>
      ),
    },
    {
      header: "Email",
      accessorKey: "email",
      cell: (user) => (
        <div className="flex items-center">
          <Mail className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm text-gray-900">{user.email}</span>
        </div>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (user) => (
        <Button
          size="sm"
          variant="outline"
          onClick={() => handleRemoveCoachee(user.id)}
          className="text-red-600 hover:text-red-800"
        >
          <UserMinus className="h-3 w-3 mr-1" />
          Remove
        </Button>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title={`${organization.name} - Members`}
        description="Manage coaches and coachees in your organization"
      />

      {/* Organization Info */}
      <Card>
        <CardHeader title="Organization Overview" />
        <div className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <Building2 className="h-4 w-4 text-gray-400 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Organization</p>
                <p className="text-sm text-gray-500">{organization.name}</p>
              </div>
            </div>

            <div className="flex items-center">
              <UserCheck className="h-4 w-4 text-gray-400 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Coaches</p>
                <p className="text-sm text-gray-500">{coaches.length}</p>
              </div>
            </div>

            <div className="flex items-center">
              <Users className="h-4 w-4 text-gray-400 mr-2" />
              <div>
                <p className="text-sm font-medium text-gray-900">Coachees</p>
                <p className="text-sm text-gray-500">{coachees.length}</p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Error/Success Messages */}
      {error && <ErrorMessage message={error} />}
      {success && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <span className="text-sm text-green-800">{success}</span>
        </div>
      )}

      {/* Coaches Section */}
      <Card>
        <CardHeader 
          title="Coaches" 
          subtitle={`${coaches.length} coaches in this organization`}
        />
        <div className="p-6 pt-0">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Manage Coaches</h3>
            <Button
              onClick={() => setShowAddCoach(!showAddCoach)}
              disabled={availableCoaches.length === 0}
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Add Coach
            </Button>
          </div>

          {showAddCoach && (
            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Add New Coach</h4>
              <div className="flex space-x-2">
                <div className="flex-1">
                  <AutocompleteDropdown
                    options={availableCoaches.map(user => `${user.firstName} ${user.lastName} (${user.email})`)}
                    onSelect={(selected) => {
                      const user = availableCoaches.find(u => 
                        selected.includes(u.email)
                      );
                      if (user) setSelectedUser(user.id);
                    }}
                  />
                </div>
                <Button onClick={handleAddCoach} disabled={!selectedUser}>
                  Add
                </Button>
                <Button variant="outline" onClick={() => setShowAddCoach(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}

          <DataTable
            columns={coachColumns}
            data={coaches}
            className="w-full"
          />
        </div>
      </Card>

      {/* Coachees Section */}
      <Card>
        <CardHeader 
          title="Coachees" 
          subtitle={`${coachees.length} coachees in this organization`}
        />
        <div className="p-6 pt-0">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Manage Coachees</h3>
            <Button
              onClick={() => setShowAddCoachee(!showAddCoachee)}
              disabled={availableCoachees.length === 0}
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Add Coachee
            </Button>
          </div>

          {showAddCoachee && (
            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Add New Coachee</h4>
              <div className="flex space-x-2">
                <div className="flex-1">
                  <AutocompleteDropdown
                    options={availableCoachees.map(user => `${user.firstName} ${user.lastName} (${user.email})`)}
                    onSelect={(selected) => {
                      const user = availableCoachees.find(u => 
                        selected.includes(u.email)
                      );
                      if (user) setSelectedUser(user.id);
                    }}
                  />
                </div>
                <Button onClick={handleAddCoachee} disabled={!selectedUser}>
                  Add
                </Button>
                <Button variant="outline" onClick={() => setShowAddCoachee(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}

          <DataTable
            columns={coacheeColumns}
            data={coachees}
            className="w-full"
          />
        </div>
      </Card>
    </div>
  );
};

export default HRMembers;
