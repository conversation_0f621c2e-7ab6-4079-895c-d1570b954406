import { useState, useEffect } from "react";
import { Button } from "./ui/Button";
import { Card } from "./ui/Card";
import { ErrorMessage } from "./ui/ErrorMessage";
import { LoadingSpinner } from "./ui/LoadingSpinner";
import { Volume } from "lucide-react";

type ExerciseFormProps = {
  exercise?: any;
  onSave: (
    title: string,
    description: string,
    exerciseType: "questionnaire" | "visualization",
    audioBlob?: Blob
  ) => Promise<{ success: boolean; error?: string; exercise?: any }>;
  onCancel: () => void;
  isLoading: boolean;
};

export function ExerciseForm({
  exercise,
  onSave,
  onCancel,
  isLoading,
}: ExerciseFormProps) {
  const [exerciseType, setExerciseType] = useState<
    "questionnaire" | "visualization"
  >("questionnaire");
  const [title, setTitle] = useState(exercise?.title || "");
  const [description, setDescription] = useState(exercise?.description || "");
  const [error, setError] = useState<string>("");
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [hasGeneratedAudio, setHasGeneratedAudio] = useState(false);
  const [isGeneratingAudio, setIsGeneratingAudio] = useState(false);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(
    null
  );
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  useEffect(() => {
    if (exercise) {
      setExerciseType(exercise.type);
      setTitle(exercise.title);
      setDescription(exercise.description);

      // If this is a visualization exercise with an audio URL, mark it as having generated audio
      if (
        exercise.type === "visualization" &&
        exercise.visualization_audio_url
      ) {
        setHasGeneratedAudio(true);
      }
    }
  }, [exercise]);

  // Clean up audio resources when component unmounts
  useEffect(() => {
    return () => {
      if (audioElement) {
        audioElement.pause();
        audioElement.onended = null;
        audioElement.onpause = null;
        audioElement.onplay = null;
        audioElement.onerror = null;
      }

      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioElement, audioUrl]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!title.trim()) {
      setError("Title is required");
      return;
    }

    if (!description.trim()) {
      setError("Description is required");
      return;
    }

    // For visualization exercises, ensure audio is generated before saving
    if (exerciseType === "visualization" && !hasGeneratedAudio) {
      setError("Please generate audio before saving the exercise");
      return;
    }

    // If audio is currently being generated, wait for it to complete
    if (isGeneratingAudio) {
      setError("Please wait for audio generation to complete before saving");
      return;
    }

    try {
      // Only pass the audioBlob if this is a visualization exercise and we have generated audio
      const blobToSave =
        exerciseType === "visualization" && hasGeneratedAudio && audioBlob
          ? audioBlob
          : undefined;
      const result = await onSave(title, description, exerciseType, blobToSave);
      if (!result.success) {
        setError(result.error || "Failed to save exercise");
      }
    } catch (err: any) {
      setError(err.message || "An error occurred");
    }
  };

  const handleTextToSpeech = async () => {
    // If already playing, pause the audio
    if (isPlaying && audioElement) {
      audioElement.pause();
      setIsPlaying(false);
      return;
    }

    // If we have already generated audio, play it again
    if (hasGeneratedAudio && audioBlob && audioUrl) {
      playAudio(audioUrl);
      return;
    }

    // Otherwise, generate new audio
    if (!description.trim() || isGeneratingAudio) return;

    try {
      setIsGeneratingAudio(true);
      setError("");

      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_API_URL}/api/tts`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ text: description }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to convert text to speech");
      }

      // Create a blob from the audio data
      const newAudioBlob = await response.blob();

      // Save the blob for later submission
      setAudioBlob(newAudioBlob);
      setHasGeneratedAudio(true);

      // Create a URL for the blob
      const newAudioUrl = URL.createObjectURL(newAudioBlob);
      setAudioUrl(newAudioUrl);

      // Play the audio
      playAudio(newAudioUrl);
      setIsGeneratingAudio(false);
    } catch (err: any) {
      setError(err.message || "Failed to play audio");
      setIsPlaying(false);
      setIsGeneratingAudio(false);
    }
  };

  const playAudio = (url: string) => {
    // If there's an existing audio element, clean it up
    if (audioElement) {
      audioElement.pause();
      audioElement.onended = null;
      audioElement.onpause = null;
      audioElement.onplay = null;
      audioElement.onerror = null;
    }

    const audio = new Audio(url);

    audio.onended = () => {
      setIsPlaying(false);
      setAudioElement(null);
    };

    audio.onpause = () => {
      setIsPlaying(false);
    };

    audio.onplay = () => {
      setIsPlaying(true);
    };

    audio.onerror = () => {
      setError("Failed to play audio");
      setIsPlaying(false);
      setAudioElement(null);
    };

    setAudioElement(audio);
    setIsPlaying(true);

    audio.play().catch((err) => {
      console.error("Error playing audio:", err);
      setError("Failed to play audio");
      setIsPlaying(false);
      setAudioElement(null);
    });
  };

  return (
    <Card className="p-6">
      <h2 className="text-xl font-bold mb-4">
        {exercise ? "Edit Exercise" : "Create New Exercise"}
      </h2>

      {error && <ErrorMessage message={error} className="mb-4" />}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Exercise Type */}
        <div>
          <label
            htmlFor="exerciseType"
            className="block text-sm font-medium text-gray-700"
          >
            Exercise Type
          </label>
          <select
            value={exerciseType}
            onChange={(e) =>
              setExerciseType(
                e.target.value as "questionnaire" | "visualization"
              )
            }
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={isLoading}
            required
          >
            <option value="questionnaire">Questionnaire</option>
            <option value="visualization">Visualization</option>
          </select>
        </div>
        <div>
          <label
            htmlFor="title"
            className="block text-sm font-medium text-gray-700"
          >
            Exercise Title
          </label>
          <input
            id="title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={isLoading}
            required
          />
        </div>

        <div>
          <div className="flex justify-between items-center">
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700"
            >
              Exercise Description
            </label>
            {exerciseType === "visualization" && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleTextToSpeech}
                disabled={
                  isLoading ||
                  isGeneratingAudio ||
                  (!description.trim() && !hasGeneratedAudio)
                }
              >
                <Volume size={16} />
                {isGeneratingAudio
                  ? "Generating..."
                  : isPlaying
                  ? "Pause"
                  : hasGeneratedAudio
                  ? "Play Audio"
                  : "Generate Audio"}
              </Button>
            )}
          </div>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={4}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={isLoading}
            required
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button type="submit" variant="primary" disabled={isLoading}>
            {isLoading ? (
              <LoadingSpinner size="sm" />
            ) : exercise ? (
              "Update Exercise"
            ) : (
              "Create Exercise"
            )}
          </Button>
        </div>
      </form>
    </Card>
  );
}
