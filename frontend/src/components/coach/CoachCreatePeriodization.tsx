import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card } from "../ui/Card";
import { Button } from "../ui/Button";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import {
  Calendar,
  CheckCircle,
  AlertCircle,
  BicepsFlexed,
  HeartPlus,
  Eye,
} from "lucide-react";
import { useCoach } from "../../context/CoachContext";
import { InteractivePeriodizationBar } from "../ui/InteractivePeriodizationBar";
import { PeriodBreakdownBar } from "../ui/PeriodBreakdownBar";
import { DateRangeBar } from "../ui/DateRangeBar";
import { DataTable } from "../ui/DataTable";
import { Column } from "../../types/components.types";
import { Label } from "../ui/Label";
import {
  generatePeriodizationPreview,
  createPeriodization,
  PeriodizationEntry,
  PeriodizationPreview,
} from "../../api/periodization";

interface Coachee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

const CoachCreatePeriodization: React.FC = () => {
  const navigate = useNavigate();

  // Default dates function (same as admin)
  const getDefaultDates = () => {
    const today = new Date();
    const offSeasonStart = new Date(today);
    offSeasonStart.setDate(today.getDate() + 7); // Start next week

    const prepStart = new Date(offSeasonStart);
    prepStart.setDate(offSeasonStart.getDate() + 12 * 7); // 12 weeks later

    const preCompStart = new Date(prepStart);
    preCompStart.setDate(prepStart.getDate() + 12 * 7); // 12 weeks later

    const competitionStart = new Date(preCompStart);
    competitionStart.setDate(preCompStart.getDate() + 4 * 7); // 4 weeks later

    const competitionEnd = new Date(competitionStart);
    competitionEnd.setDate(competitionStart.getDate() + 2 * 7); // 2 weeks later

    return {
      offSeasonStart: offSeasonStart.toISOString().split("T")[0],
      prepStart: prepStart.toISOString().split("T")[0],
      preCompStart: preCompStart.toISOString().split("T")[0],
      competitionStart: competitionStart.toISOString().split("T")[0],
      competitionEnd: competitionEnd.toISOString().split("T")[0],
    };
  };

  const { myCoachees, fetchMyCoachees } = useCoach();
  const [selectedCoachee, setSelectedCoachee] = useState<string>("");
  const [periodizationName, setPeriodizationName] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [preview, setPreview] = useState<PeriodizationPreview | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Date states with defaults
  const defaultDates = getDefaultDates();
  const [offSeasonStartDate, setOffSeasonStartDate] = useState<string>(
    defaultDates.offSeasonStart
  );
  const [prepStartDate, setPrepStartDate] = useState<string>(
    defaultDates.prepStart
  );
  const [preCompStartDate, setPreCompStartDate] = useState<string>(
    defaultDates.preCompStart
  );
  const [competitionStartDate, setCompetitionStartDate] = useState<string>(
    defaultDates.competitionStart
  );
  const [competitionEndDate, setCompetitionEndDate] = useState<string>(
    defaultDates.competitionEnd
  );

  // Options
  const [scheduleMentalToughness, setScheduleMentalToughness] =
    useState<boolean>(true);
  const [scheduleMentalWellness, setScheduleMentalWellness] =
    useState<boolean>(true);

  // Mental wellness date range (in weeks from start)
  const [mentalWellnessRange, setMentalWellnessRange] = useState({
    startWeek: 1,
    endWeek: 1,
  });

  // Calculated values
  const [totalWeeks, setTotalWeeks] = useState<number>(0);
  const [phaseDurations, setPhaseDurations] = useState({
    offSeasonWeeks: 0,
    prepWeeks: 0,
    preCompWeeks: 0,
    competitionWeeks: 0,
  });

  // Period breakdown for static display
  const [periodBreakdown, setPeriodBreakdown] = useState<{
    offSeasonWeeks: number;
    prepWeeks: number;
    preCompWeeks: number;
    competitionWeeks: number;
    totalWeeks: number;
  } | null>(null);

  useEffect(() => {
    fetchMyCoachees();
  }, []);

  // Calculate total weeks and phase dates
  useEffect(() => {
    if (offSeasonStartDate && competitionEndDate) {
      const start = new Date(offSeasonStartDate);
      const end = new Date(competitionEndDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffWeeks = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
      setTotalWeeks(diffWeeks);
    }
  }, [offSeasonStartDate, competitionEndDate]);

  // Initialize mental wellness range when total weeks change
  useEffect(() => {
    if (totalWeeks > 0) {
      setMentalWellnessRange({
        startWeek: 1,
        endWeek: totalWeeks,
      });
    }
  }, [totalWeeks]);

  // Calculate period breakdown when dates change
  useEffect(() => {
    if (
      offSeasonStartDate &&
      prepStartDate &&
      preCompStartDate &&
      competitionStartDate &&
      competitionEndDate
    ) {
      const startDate = new Date(offSeasonStartDate);
      const prepStart = new Date(prepStartDate);
      const preCompStart = new Date(preCompStartDate);
      const compStart = new Date(competitionStartDate);
      const compEnd = new Date(competitionEndDate);

      // Calculate weeks for each phase
      const offSeasonWeeks = Math.ceil(
        (prepStart.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7)
      );
      const prepWeeks = Math.ceil(
        (preCompStart.getTime() - prepStart.getTime()) /
          (1000 * 60 * 60 * 24 * 7)
      );
      const preCompWeeks = Math.ceil(
        (compStart.getTime() - preCompStart.getTime()) /
          (1000 * 60 * 60 * 24 * 7)
      );
      const competitionWeeks = Math.ceil(
        (compEnd.getTime() - compStart.getTime()) / (1000 * 60 * 60 * 24 * 7)
      );
      const totalWeeksCalc =
        offSeasonWeeks + prepWeeks + preCompWeeks + competitionWeeks;

      setPeriodBreakdown({
        offSeasonWeeks,
        prepWeeks,
        preCompWeeks,
        competitionWeeks,
        totalWeeks: totalWeeksCalc,
      });
    } else {
      setPeriodBreakdown(null);
    }
  }, [
    offSeasonStartDate,
    prepStartDate,
    preCompStartDate,
    competitionStartDate,
    competitionEndDate,
  ]);

  // Calculate phase end dates based on durations
  const calculatePhaseDates = () => {
    if (!offSeasonStartDate) return null;

    const start = new Date(offSeasonStartDate);

    const offSeasonEnd = new Date(start);
    offSeasonEnd.setDate(
      offSeasonEnd.getDate() + phaseDurations.offSeasonWeeks * 7 - 1
    );

    const prepStart = new Date(prepStartDate || offSeasonEnd);
    prepStart.setDate(prepStart.getDate() + 1);

    const prepEnd = new Date(prepStart);
    prepEnd.setDate(prepEnd.getDate() + phaseDurations.prepWeeks * 7 - 1);

    const preCompStart = new Date(preCompStartDate || prepEnd);
    preCompStart.setDate(preCompStart.getDate() + 1);

    const preCompEnd = new Date(preCompStart);
    preCompEnd.setDate(
      preCompEnd.getDate() + phaseDurations.preCompWeeks * 7 - 1
    );

    // Calculate mental wellness dates
    const mentalWellnessStartDate = new Date(start);
    mentalWellnessStartDate.setDate(
      start.getDate() + (mentalWellnessRange.startWeek - 1) * 7
    );

    const mentalWellnessEndDate = new Date(start);
    mentalWellnessEndDate.setDate(
      start.getDate() + (mentalWellnessRange.endWeek - 1) * 7 + 6
    );

    return {
      offSeasonStart: start,
      offSeasonEnd,
      prepStart: new Date(prepStartDate || prepStart),
      prepEnd: (() => {
        if (prepStartDate) {
          const date = new Date(prepStartDate);
          date.setDate(date.getDate() + phaseDurations.prepWeeks * 7 - 1);
          return date;
        }
        return prepEnd;
      })(),
      preCompStart: new Date(preCompStartDate || preCompStart),
      preCompEnd: (() => {
        if (preCompStartDate) {
          const date = new Date(preCompStartDate);
          date.setDate(date.getDate() + phaseDurations.preCompWeeks * 7 - 1);
          return date;
        }
        return preCompEnd;
      })(),
      compStart: new Date(competitionStartDate),
      compEnd: new Date(competitionEndDate),
      mentalWellnessStart: mentalWellnessStartDate,
      mentalWellnessEnd: mentalWellnessEndDate,
    };
  };

  const handleGeneratePreview = async () => {
    if (
      !selectedCoachee ||
      !offSeasonStartDate ||
      !prepStartDate ||
      !preCompStartDate ||
      !competitionStartDate ||
      !competitionEndDate
    ) {
      setErrors(["Please fill in all required fields"]);
      return;
    }

    setIsLoading(true);
    setErrors([]);

    try {
      const phaseDates = calculatePhaseDates();
      if (!phaseDates) {
        setErrors(["Invalid date configuration"]);
        return;
      }

      const previewData = await generatePeriodizationPreview({
        coacheeId: selectedCoachee,
        offSeasonStartDate: phaseDates.offSeasonStart.toISOString(),
        offSeasonEndDate: phaseDates.offSeasonEnd.toISOString(),
        prepStartDate: phaseDates.prepStart.toISOString(),
        prepEndDate: phaseDates.prepEnd.toISOString(),
        preCompStartDate: phaseDates.preCompStart.toISOString(),
        preCompEndDate: phaseDates.preCompEnd.toISOString(),
        competitionStartDate: phaseDates.compStart.toISOString(),
        competitionEndDate: phaseDates.compEnd.toISOString(),
        scheduleMentalToughness,
        scheduleMentalWellness,
        mentalWellnessStartDate: phaseDates.mentalWellnessStart?.toISOString(),
        mentalWellnessEndDate: phaseDates.mentalWellnessEnd?.toISOString(),
      });
      setPreview(previewData);
      setShowPreview(true);

      if (previewData.errors && previewData.errors.length > 0) {
        setErrors(previewData.errors);
      }
    } catch (error) {
      console.error("Error generating preview:", error);
      setErrors([
        error instanceof Error ? error.message : "Failed to generate preview",
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreatePeriodization = async () => {
    if (!periodizationName.trim()) {
      setErrors(["Please enter a name for the periodization"]);
      return;
    }

    setIsCreating(true);
    setErrors([]);

    try {
      const phaseDates = calculatePhaseDates();
      if (!phaseDates) {
        setErrors(["Invalid date configuration"]);
        return;
      }

      const result = await createPeriodization({
        coacheeId: selectedCoachee,
        name: periodizationName,
        offSeasonStartDate: phaseDates.offSeasonStart.toISOString(),
        offSeasonEndDate: phaseDates.offSeasonEnd.toISOString(),
        prepStartDate: phaseDates.prepStart.toISOString(),
        prepEndDate: phaseDates.prepEnd.toISOString(),
        preCompStartDate: phaseDates.preCompStart.toISOString(),
        preCompEndDate: phaseDates.preCompEnd.toISOString(),
        competitionStartDate: phaseDates.compStart.toISOString(),
        competitionEndDate: phaseDates.compEnd.toISOString(),
        scheduleMentalToughness,
        scheduleMentalWellness,
        mentalWellnessStartDate: phaseDates.mentalWellnessStart?.toISOString(),
        mentalWellnessEndDate: phaseDates.mentalWellnessEnd?.toISOString(),
      });

      // Reset form
      const createdForCoachee = selectedCoachee;
      setSelectedCoachee("");
      setPeriodizationName("");
      setOffSeasonStartDate(defaultDates.offSeasonStart);
      setPrepStartDate(defaultDates.prepStart);
      setPreCompStartDate(defaultDates.preCompStart);
      setCompetitionStartDate(defaultDates.competitionStart);
      setCompetitionEndDate(defaultDates.competitionEnd);
      setPreview(null);
      setShowPreview(false);

      // Show success message with option to view coachee
      const coacheeName =
        myCoachees?.find((c) => c.id === createdForCoachee)?.firstName ||
        "coachee";
      const viewCoachee = window.confirm(
        `Periodization created successfully! ${result.assignmentCount} assignments were created for ${coacheeName}.\n\nWould you like to view the coachee's assignments now?\n\nNote: If assignments don't appear immediately, use the "Refresh" button in the Assignments Summary section.`
      );

      if (viewCoachee) {
        // Navigate to coachees page - this will trigger a fresh fetch of assignments
        navigate("/dashboard/coachees");
      }
    } catch (error) {
      console.error("Error creating periodization:", error);
      setErrors([
        error instanceof Error
          ? error.message
          : "Failed to create periodization",
      ]);
    } finally {
      setIsCreating(false);
    }
  };

  const columns: Column<PeriodizationEntry>[] = [
    {
      header: "Week",
      accessorKey: "week",
      cell: (entry) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-blue-600 mr-2" />
          <span className="font-medium">Week {entry.week}</span>
        </div>
      ),
    },
    {
      header: "Assignment Date",
      accessorKey: "assignmentDate",
      cell: (entry) => (
        <span className="text-sm text-gray-900">
          {new Date(entry.assignmentDate).toLocaleDateString("en-US", {
            weekday: "short",
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </span>
      ),
    },
    {
      header: "Type",
      accessorKey: "type",
      cell: (entry) => (
        <div className="flex items-center">
          {entry.type === "MT" ? (
            <Label icon={BicepsFlexed} color="orange" text="MT Exercise" />
          ) : (
            <Label icon={HeartPlus} color="green" text="MW Exercise" />
          )}
        </div>
      ),
    },
    {
      header: "Exercise",
      accessorKey: "exerciseTitle",
      cell: (entry) => (
        <div>
          <span className="text-sm font-medium text-gray-900">
            {entry.exerciseTitle}
          </span>
          <p className="text-xs text-gray-500">ID: {entry.exerciseId}</p>
          {entry.skill && (
            <p className="text-xs text-blue-600">Skill: {entry.skill}</p>
          )}
        </div>
      ),
    },
    {
      header: "Phase",
      accessorKey: "phase",
      cell: (entry) => (
        <span
          className={`px-2 py-1 rounded text-xs font-medium ${
            entry.phase === "Off Season"
              ? "bg-blue-100 text-blue-800"
              : entry.phase === "Prep"
              ? "bg-green-100 text-green-800"
              : entry.phase === "Pre-Comp"
              ? "bg-purple-100 text-purple-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {entry.phase}
        </span>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <Calendar className="h-8 w-8 text-blue-600" />
        <h1 className="text-3xl font-bold text-gray-900">
          Create Periodization
        </h1>
      </div>

      {/* Error Display */}
      {errors.length > 0 && (
        <Card className="p-4 border-red-200 bg-red-50">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-red-800">
                Please fix the following errors:
              </h4>
              <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      )}

      {/* Form */}
      <Card className="p-6">
        <div className="space-y-6">
          {/* Coachee Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Coachee *
            </label>
            <select
              value={selectedCoachee}
              onChange={(e) => setSelectedCoachee(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="">Choose a coachee...</option>
              {myCoachees &&
                myCoachees.map((coachee: Coachee) => (
                  <option key={coachee.id} value={coachee.id}>
                    {coachee.firstName} {coachee.lastName} ({coachee.email})
                  </option>
                ))}
            </select>
          </div>

          {/* Periodization Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Periodization Name *
            </label>
            <input
              type="text"
              value={periodizationName}
              onChange={(e) => setPeriodizationName(e.target.value)}
              placeholder="e.g., 2024 Competition Season"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {/* Phase Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Off Season Start *
              </label>
              <input
                type="date"
                value={offSeasonStartDate}
                onChange={(e) => setOffSeasonStartDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Prep Start *
              </label>
              <input
                type="date"
                value={prepStartDate}
                onChange={(e) => setPrepStartDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pre-Comp Start *
              </label>
              <input
                type="date"
                value={preCompStartDate}
                onChange={(e) => setPreCompStartDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Competition Start *
              </label>
              <input
                type="date"
                value={competitionStartDate}
                onChange={(e) => setCompetitionStartDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Competition End *
              </label>
              <input
                type="date"
                value={competitionEndDate}
                onChange={(e) => setCompetitionEndDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          </div>

          {/* Period Breakdown Bar (Non-interactive) */}
          {periodBreakdown ? (
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">
                Period Breakdown
              </h4>
              <p className="text-sm text-gray-600 mb-4">
                This shows the breakdown of periods based on your selected
                dates.
              </p>
              <PeriodBreakdownBar
                offSeasonWeeks={periodBreakdown.offSeasonWeeks}
                prepWeeks={periodBreakdown.prepWeeks}
                preCompWeeks={periodBreakdown.preCompWeeks}
                competitionWeeks={periodBreakdown.competitionWeeks}
                totalWeeks={periodBreakdown.totalWeeks}
              />
            </div>
          ) : (
            offSeasonStartDate &&
            prepStartDate &&
            preCompStartDate &&
            competitionStartDate &&
            competitionEndDate && (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <h4 className="text-md font-medium text-yellow-800 mb-2">
                  Invalid Date Order
                </h4>
                <p className="text-sm text-yellow-700">
                  Please ensure dates are in chronological order: Off Season
                  Start → Prep Start → Pre-Comp Start → Competition Start →
                  Competition End
                </p>
              </div>
            )
          )}

          {/* Interactive Periodization Bar */}
          {totalWeeks > 0 && (
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">
                Phase Duration Adjustment
              </h4>
              <p className="text-sm text-gray-600 mb-4">
                Drag the dividers to adjust when phases start and end within the
                overall period.
              </p>
              <InteractivePeriodizationBar
                totalWeeks={totalWeeks}
                onPhasesChange={setPhaseDurations}
              />
            </div>
          )}

          {/* Mental Wellness Date Range */}
          {totalWeeks > 0 && (
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">
                Mental Wellness Schedule Range
              </h4>
              <p className="text-sm text-gray-600 mb-4">
                Drag the start and end handles to specify when mental wellness
                exercises should be scheduled.
              </p>
              <DateRangeBar
                totalStartDate={offSeasonStartDate}
                totalEndDate={competitionEndDate}
                rangeStartWeek={mentalWellnessRange.startWeek}
                rangeEndWeek={mentalWellnessRange.endWeek}
                onRangeChange={(startWeek, endWeek) =>
                  setMentalWellnessRange({ startWeek, endWeek })
                }
                title="Mental Wellness Range"
                color="bg-purple-500"
              />
            </div>
          )}

          {/* Options */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900">
              Scheduling Options
            </h4>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={scheduleMentalToughness}
                  onChange={(e) => setScheduleMentalToughness(e.target.checked)}
                  className="mr-2"
                />
                Schedule Mental Toughness exercises
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={scheduleMentalWellness}
                  onChange={(e) => setScheduleMentalWellness(e.target.checked)}
                  className="mr-2"
                />
                Schedule Mental Wellness exercises
              </label>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-4">
            <Button
              onClick={handleGeneratePreview}
              disabled={
                isLoading ||
                !selectedCoachee ||
                !offSeasonStartDate ||
                !competitionEndDate
              }
              className="flex items-center space-x-2"
            >
              {isLoading ? (
                <LoadingSpinner size="sm" />
              ) : (
                <Calendar className="h-4 w-4" />
              )}
              <span>Generate Preview</span>
            </Button>
          </div>
        </div>
      </Card>

      {/* Preview */}
      {showPreview && preview && (
        <Card className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                Periodization Preview
              </h3>
              <div className="text-sm text-gray-600">
                Total Assignments: {preview.totalAssignments}
              </div>
            </div>

            {/* Phase Breakdown */}
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center p-3 bg-blue-50 rounded">
                <div className="text-lg font-semibold text-blue-800">
                  {preview.phaseBreakdown.offSeason}
                </div>
                <div className="text-sm text-blue-600">Off Season</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded">
                <div className="text-lg font-semibold text-green-800">
                  {preview.phaseBreakdown.prep}
                </div>
                <div className="text-sm text-green-600">Prep</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded">
                <div className="text-lg font-semibold text-purple-800">
                  {preview.phaseBreakdown.preComp}
                </div>
                <div className="text-sm text-purple-600">Pre-Comp</div>
              </div>
            </div>

            {/* Preview Table */}
            <DataTable
              columns={columns}
              data={preview.entries}
              className="w-full"
            />

            {/* Confirm Button */}
            <div className="flex justify-end space-x-4 pt-4 border-t">
              <Button variant="outline" onClick={() => setShowPreview(false)}>
                Back to Edit
              </Button>
              <Button
                onClick={handleCreatePeriodization}
                disabled={isCreating || !periodizationName.trim()}
                className="flex items-center space-x-2"
              >
                {isCreating ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                <span>Create Periodization</span>
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default CoachCreatePeriodization;
