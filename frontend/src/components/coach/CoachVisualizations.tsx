import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import { useCoach } from "../../context/CoachContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import {
  Eye,
  Plus,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Edit,
  Copy,
  Calendar,
  User,
} from "lucide-react";
import { Visualization } from "../../types/api/visualizations.types";

const CoachVisualizations = () => {
  const { allVisualizations, loading, fetchAllVisualizations } = useCoach();

  const [audioElements, setAudioElements] = useState<{
    [key: string]: HTMLAudioElement;
  }>({});
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);

  useEffect(() => {
    fetchAllVisualizations();
  }, []);

  const handlePlayAudio = (visualization: Visualization) => {
    const audioId = visualization.id;

    // Stop any currently playing audio
    if (playingAudio && playingAudio !== audioId) {
      const currentAudio = audioElements[playingAudio];
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }
    }

    // Toggle current audio
    if (playingAudio === audioId) {
      const audio = audioElements[audioId];
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
      setPlayingAudio(null);
      return;
    }

    // Start new audio
    if (!visualization.audioUrl) return;

    let audio = audioElements[audioId];
    if (!audio) {
      audio = new Audio(visualization.audioUrl);
      audio.onended = () => setPlayingAudio(null);
      audio.onpause = () => setPlayingAudio(null);
      audio.onerror = () => {
        console.error("Error playing audio for visualization:", audioId);
        setPlayingAudio(null);
      };

      setAudioElements((prev) => ({ ...prev, [audioId]: audio }));
    }

    setPlayingAudio(audioId);
    audio.play().catch((error) => {
      console.error("Error playing audio:", error);
      setPlayingAudio(null);
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getCreatorName = (visualization: Visualization) => {
    if (visualization.creator) {
      return `${visualization.creator.firstName} ${visualization.creator.lastName}`;
    }
    return "Unknown";
  };

  if (loading.visualizations) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Visualizations"
        description="Manage visualization exercises for your coachees."
        actionButton={{
          label: "Create Visualization",
          to: "/dashboard/create-visualization",
        }}
      />

      {/* Quick Actions */}
      <Card
        header={
          <CardHeader
            title="Quick Actions"
            description="Common visualization tasks"
          />
        }
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Link
            to="/dashboard/create-visualization"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Plus className="h-6 w-6 text-green-600 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                Create New Visualization
              </h3>
              <p className="text-sm text-gray-500">Start from scratch</p>
            </div>
          </Link>

          <Link
            to="/dashboard/create-visualization-assignment"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Eye className="h-6 w-6 text-indigo-600 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                Assign Visualization
              </h3>
              <p className="text-sm text-gray-500">Assign to coachees</p>
            </div>
          </Link>
        </div>
      </Card>

      {/* Visualizations List */}
      <Card
        header={
          <CardHeader
            title="All Visualizations"
            description={`${allVisualizations.length} visualizations available`}
          />
        }
      >
        {allVisualizations.length === 0 ? (
          <div className="text-center py-8">
            <Eye className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No visualizations yet
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Create your first visualization to get started.
            </p>
            <div className="mt-6">
              <Button>
                <Link to="/dashboard/create-visualization">
                  Create Visualization
                </Link>
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {allVisualizations.map((visualization) => (
              <div
                key={visualization.id}
                className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900">
                        {visualization.title}
                      </h3>
                      {visualization.audioUrl && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePlayAudio(visualization)}
                          className="flex items-center space-x-1"
                        >
                          {playingAudio === visualization.id ? (
                            <Pause className="h-4 w-4" />
                          ) : (
                            <Play className="h-4 w-4" />
                          )}
                          <span>
                            {playingAudio === visualization.id
                              ? "Pause"
                              : "Play"}
                          </span>
                        </Button>
                      )}
                    </div>

                    <p className="text-sm text-gray-600 mb-3 line-clamp-3">
                      {visualization.description}
                    </p>

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3" />
                        <span>Created by {getCreatorName(visualization)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(visualization.createdAt)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        {visualization.audioUrl ? (
                          <Volume2 className="h-3 w-3 text-green-500" />
                        ) : (
                          <VolumeX className="h-3 w-3 text-gray-400" />
                        )}
                        <span>
                          {visualization.audioUrl
                            ? "Audio available"
                            : "No audio"}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <Link
                      to={`/dashboard/create-visualization?editId=${visualization.id}`}
                      className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors"
                    >
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Link>

                    <Link
                      to={`/dashboard/create-visualization?baseId=${visualization.id}`}
                      className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-purple-700 bg-purple-100 rounded-md hover:bg-purple-200 transition-colors"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy
                    </Link>

                    <Link
                      to={`/dashboard/create-visualization-assignment?visualizationId=${visualization.id}`}
                      className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200 transition-colors"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Assign
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default CoachVisualizations;
