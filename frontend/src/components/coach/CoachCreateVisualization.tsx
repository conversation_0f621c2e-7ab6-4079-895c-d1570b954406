import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import TextInput from "../ui/input/TextInput";
import TextAreaInput from "../ui/input/TextAreaInput";

import { useCoach } from "../../context/CoachContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { CheckCircle, Eye, Play, Pause, Volume2, Copy, Wand2 } from "lucide-react";
import { generateCustomAudio, getAvailableVoices } from "../../api/audio";
import { getVisualizationById } from "../../api/visualizations";
import { Visualization } from "../../types/api/visualizations.types";

const CoachCreateVisualization = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const baseVisualizationId = searchParams.get("baseId"); // For copying/modifying existing
  const editVisualizationId = searchParams.get("editId"); // For editing existing

  const {
    createNewVisualization,
    updateExistingVisualization,
  } = useCoach();

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    audioUrl: "",
  });

  const [baseVisualization, setBaseVisualization] = useState<Visualization | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGeneratingAudio, setIsGeneratingAudio] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [audioPreview, setAudioPreview] = useState<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [availableVoices, setAvailableVoices] = useState<string[]>([]);
  const [selectedVoice, setSelectedVoice] = useState("af_nicole");

  const isEditMode = !!editVisualizationId;
  const isCopyMode = !!baseVisualizationId;

  useEffect(() => {
    // Load available voices
    getAvailableVoices().then((response) => {
      setAvailableVoices(response.voices);
      setSelectedVoice(response.defaultVoice);
    }).catch(console.error);

    // Load base visualization for copying or editing
    if (baseVisualizationId || editVisualizationId) {
      const id = baseVisualizationId || editVisualizationId;
      getVisualizationById(id!).then((visualization) => {
        setBaseVisualization(visualization);
        setFormData({
          title: isCopyMode ? `Copy of ${visualization.title}` : visualization.title,
          description: visualization.description,
          audioUrl: visualization.audioUrl || "",
        });
      }).catch((error) => {
        setError("Failed to load visualization");
        console.error("Error loading visualization:", error);
      });
    }
  }, [baseVisualizationId, editVisualizationId, isCopyMode]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setError(null);
    setSuccess(null);
  };

  const handleGenerateAudio = async () => {
    if (!formData.description.trim()) {
      setError("Please enter a description to generate audio");
      return;
    }

    setIsGeneratingAudio(true);
    setError(null);

    try {
      const response = await generateCustomAudio(formData.description, {
        voice: selectedVoice,
        speed: 1.0,
        language: "en",
      });

      setFormData((prev) => ({ ...prev, audioUrl: response.audioUrl }));
      setSuccess("Audio generated successfully!");
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to generate audio");
    } finally {
      setIsGeneratingAudio(false);
    }
  };

  const handlePlayAudio = () => {
    if (!formData.audioUrl) return;

    if (audioPreview) {
      audioPreview.pause();
      setAudioPreview(null);
      setIsPlaying(false);
      return;
    }

    const audio = new Audio(formData.audioUrl);
    audio.onended = () => {
      setIsPlaying(false);
      setAudioPreview(null);
    };
    audio.onplay = () => setIsPlaying(true);
    audio.onpause = () => setIsPlaying(false);
    audio.onerror = () => {
      setError("Failed to play audio");
      setIsPlaying(false);
      setAudioPreview(null);
    };

    setAudioPreview(audio);
    audio.play();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.description.trim()) {
      setError("Please fill in both title and description");
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      if (isEditMode) {
        await updateExistingVisualization(editVisualizationId!, {
          title: formData.title,
          description: formData.description,
          audioUrl: formData.audioUrl,
        });
        setSuccess("Visualization updated successfully!");
      } else {
        await createNewVisualization({
          title: formData.title,
          description: formData.description,
          audioUrl: formData.audioUrl,
        });
        setSuccess("Visualization created successfully!");
      }

      // Reset form if creating new
      if (!isEditMode) {
        setFormData({
          title: "",
          description: "",
          audioUrl: "",
        });
      }

      // Redirect after a short delay
      setTimeout(() => {
        navigate("/dashboard/visualizations");
      }, 2000);
    } catch (error: any) {
      setError(error.response?.data?.message || `Failed to ${isEditMode ? 'update' : 'create'} visualization`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPageTitle = () => {
    if (isEditMode) return "Edit Visualization";
    if (isCopyMode) return "Copy Visualization";
    return "Create Visualization";
  };

  const getPageDescription = () => {
    if (isEditMode) return "Update the visualization content and audio.";
    if (isCopyMode) return "Create a customized version of an existing visualization.";
    return "Create a new visualization exercise for your coachees.";
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title={getPageTitle()}
        description={getPageDescription()}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Visualization Form */}
        <Card
          header={
            <CardHeader
              title="Visualization Details"
              description="Enter the content for your visualization exercise"
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Title */}
            <TextInput
              label="Title"
              value={formData.title}
              onChange={(value) => handleInputChange("title", value)}
              placeholder="Enter visualization title..."
              required
            />

            {/* Description */}
            <TextAreaInput
              label="Description / Script"
              value={formData.description}
              onChange={(value) => handleInputChange("description", value)}
              placeholder="Enter the visualization script that will be read to the coachee..."
              rows={8}
              required
            />

            {/* Voice Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Voice for Audio Generation
              </label>
              <select
                value={selectedVoice}
                onChange={(e) => setSelectedVoice(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {availableVoices.map((voice) => (
                  <option key={voice} value={voice}>
                    {voice.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </option>
                ))}
              </select>
            </div>

            {/* Audio Generation */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-gray-700">
                  Audio
                </label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleGenerateAudio}
                  disabled={isGeneratingAudio || !formData.description.trim()}
                  className="flex items-center space-x-1"
                >
                  <Wand2 className="h-4 w-4" />
                  <span>{isGeneratingAudio ? "Generating..." : "Generate Audio"}</span>
                </Button>
              </div>

              {formData.audioUrl && (
                <div className="flex items-center space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <Volume2 className="h-5 w-5 text-green-600" />
                  <span className="text-sm text-green-800 flex-1">Audio ready</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handlePlayAudio}
                    className="flex items-center space-x-1"
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    <span>{isPlaying ? "Pause" : "Preview"}</span>
                  </Button>
                </div>
              )}
            </div>

            {error && <ErrorMessage message={error} />}

            {success && (
              <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-sm text-green-800">{success}</span>
              </div>
            )}

            <div className="flex space-x-3">
              <Button
                type="submit"
                disabled={isSubmitting || !formData.title.trim() || !formData.description.trim()}
                className="flex-1"
              >
                {isSubmitting ? 
                  `${isEditMode ? 'Updating' : 'Creating'} Visualization...` : 
                  `${isEditMode ? 'Update' : 'Create'} Visualization`
                }
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate("/dashboard")}
              >
                Cancel
              </Button>
            </div>
          </form>
        </Card>

        {/* Preview */}
        <Card
          header={
            <CardHeader
              title="Preview"
              description="See how your visualization will appear to coachees"
            />
          }
        >
          <div className="space-y-4">
            {!formData.title && !formData.description ? (
              <div className="text-center py-8">
                <Eye className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No content yet
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Enter a title and description to see the preview.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {formData.title && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {formData.title}
                    </h3>
                  </div>
                )}
                
                {formData.description && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-700 whitespace-pre-wrap">
                      {formData.description}
                    </p>
                  </div>
                )}

                {formData.audioUrl && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Volume2 className="h-4 w-4" />
                    <span>Audio available for playback</span>
                  </div>
                )}

                {isCopyMode && baseVisualization && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Copy className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-900">
                        Based on: {baseVisualization.title}
                      </span>
                    </div>
                    <p className="text-xs text-blue-700 mt-1">
                      This is a customized version of an existing visualization
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CoachCreateVisualization;
