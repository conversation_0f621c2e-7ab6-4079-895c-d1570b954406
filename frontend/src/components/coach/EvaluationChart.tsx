import { useMemo } from "react";
import { EvaluationTrend } from "../../api/evaluations";
import { Brain, Target, Shield, Users, Heart, TrendingUp } from "lucide-react";

interface EvaluationChartProps {
  trends: EvaluationTrend[];
  selectedMetrics?: string[];
  showOverall?: boolean;
  height?: number;
  width?: number;
}

const EvaluationChart = ({
  trends,
  selectedMetrics = ["composure", "concentration", "confidence", "copeability", "cohesion"],
  showOverall = true,
  height = 400,
  width = 800,
}: EvaluationChartProps) => {
  const padding = 60;
  const graphWidth = width - padding * 2;
  const graphHeight = height - padding * 2;

  const b3c5Components = {
    composure: { label: "Composure", color: "#3B82F6", icon: Shield },
    concentration: { label: "Concentration", color: "#10B981", icon: Target },
    confidence: { label: "Confidence", color: "#EF4444", icon: Heart },
    copeability: { label: "Cope-ability", color: "#8B5CF6", icon: Brain },
    cohesion: { label: "Cohesion", color: "#F59E0B", icon: Users },
    overall: { label: "Overall", color: "#6B7280", icon: TrendingUp },
  };

  const { minDate, maxDate, dateRange } = useMemo(() => {
    if (trends.length === 0) return { minDate: new Date(), maxDate: new Date(), dateRange: 0 };
    
    const dates = trends.map(t => new Date(t.date));
    const min = new Date(Math.min(...dates.map(d => d.getTime())));
    const max = new Date(Math.max(...dates.map(d => d.getTime())));
    const range = max.getTime() - min.getTime();
    
    return { minDate: min, maxDate: max, dateRange: range };
  }, [trends]);

  const scaleX = (date: Date) => {
    if (dateRange === 0) return padding;
    return ((date.getTime() - minDate.getTime()) / dateRange) * graphWidth + padding;
  };

  const scaleY = (value: number) => {
    return height - (((value - 1) / 9) * graphHeight + padding); // Scale 1-10 to graph height
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", { 
      month: "short", 
      day: "numeric",
      year: trends.length > 1 && (maxDate.getTime() - minDate.getTime()) > 365 * 24 * 60 * 60 * 1000 ? "numeric" : undefined
    });
  };

  // Generate grid lines for Y-axis (scores 1-10)
  const yGridLines = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

  // Generate grid lines for X-axis (dates)
  const xGridLines = useMemo(() => {
    if (trends.length <= 1) return trends.map(t => new Date(t.date));
    
    const lines = [];
    const step = Math.max(1, Math.floor(trends.length / 6)); // Show max 6 date labels
    for (let i = 0; i < trends.length; i += step) {
      lines.push(new Date(trends[i].date));
    }
    if (lines[lines.length - 1].getTime() !== new Date(trends[trends.length - 1].date).getTime()) {
      lines.push(new Date(trends[trends.length - 1].date));
    }
    return lines;
  }, [trends]);

  if (trends.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <Brain className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No evaluation data</h3>
          <p className="mt-1 text-sm text-gray-500">
            Create evaluations to see trends over time.
          </p>
        </div>
      </div>
    );
  }

  const metricsToShow = showOverall 
    ? [...selectedMetrics, "overall"]
    : selectedMetrics;

  return (
    <div className="space-y-4">
      {/* Chart */}
      <div className="bg-white border rounded-lg p-4">
        <svg width={width} height={height} className="overflow-visible">
          {/* Grid lines - Y axis */}
          {yGridLines.map((y) => (
            <g key={`y-grid-${y}`}>
              <line
                x1={padding}
                y1={scaleY(y)}
                x2={width - padding}
                y2={scaleY(y)}
                stroke="#f3f4f6"
                strokeWidth="1"
              />
              <text
                x={padding - 10}
                y={scaleY(y)}
                textAnchor="end"
                dominantBaseline="middle"
                className="text-xs fill-gray-500"
              >
                {y}
              </text>
            </g>
          ))}

          {/* Grid lines - X axis */}
          {xGridLines.map((date, index) => (
            <g key={`x-grid-${index}`}>
              <line
                x1={scaleX(date)}
                y1={padding}
                x2={scaleX(date)}
                y2={height - padding}
                stroke="#f3f4f6"
                strokeWidth="1"
              />
              <text
                x={scaleX(date)}
                y={height - padding + 20}
                textAnchor="middle"
                className="text-xs fill-gray-500"
              >
                {formatDate(date)}
              </text>
            </g>
          ))}

          {/* Axes */}
          <line
            x1={padding}
            y1={height - padding}
            x2={width - padding}
            y2={height - padding}
            stroke="#374151"
            strokeWidth="2"
          />
          <line
            x1={padding}
            y1={padding}
            x2={padding}
            y2={height - padding}
            stroke="#374151"
            strokeWidth="2"
          />

          {/* Axis labels */}
          <text
            x={width / 2}
            y={height - 10}
            textAnchor="middle"
            className="text-sm fill-gray-700 font-medium"
          >
            Date
          </text>
          <text
            x={20}
            y={height / 2}
            textAnchor="middle"
            transform={`rotate(-90, 20, ${height / 2})`}
            className="text-sm fill-gray-700 font-medium"
          >
            Score (1-10)
          </text>

          {/* Plot lines and points for each metric */}
          {metricsToShow.map((metric) => {
            const component = b3c5Components[metric as keyof typeof b3c5Components];
            if (!component) return null;

            return (
              <g key={metric}>
                {/* Line */}
                {trends.length > 1 && (
                  <polyline
                    points={trends
                      .map((trend) => {
                        const value = trend[metric as keyof EvaluationTrend] as number;
                        return `${scaleX(new Date(trend.date))},${scaleY(value)}`;
                      })
                      .join(" ")}
                    fill="none"
                    stroke={component.color}
                    strokeWidth="2"
                    strokeLinejoin="round"
                  />
                )}

                {/* Points */}
                {trends.map((trend, index) => {
                  const value = trend[metric as keyof EvaluationTrend] as number;
                  return (
                    <circle
                      key={index}
                      cx={scaleX(new Date(trend.date))}
                      cy={scaleY(value)}
                      r="4"
                      fill={component.color}
                      stroke="white"
                      strokeWidth="2"
                    >
                      <title>
                        {component.label}: {value}/10 on {formatDate(new Date(trend.date))}
                      </title>
                    </circle>
                  );
                })}
              </g>
            );
          })}
        </svg>
      </div>

      {/* Legend */}
      <div className="flex flex-wrap gap-4 justify-center">
        {metricsToShow.map((metric) => {
          const component = b3c5Components[metric as keyof typeof b3c5Components];
          if (!component) return null;

          const Icon = component.icon;
          return (
            <div key={metric} className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: component.color }}
              />
              <Icon className="h-4 w-4" style={{ color: component.color }} />
              <span className="text-sm text-gray-700">{component.label}</span>
            </div>
          );
        })}
      </div>

      {/* Summary Stats */}
      {trends.length > 1 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mt-4">
          {metricsToShow.map((metric) => {
            const component = b3c5Components[metric as keyof typeof b3c5Components];
            if (!component) return null;

            const values = trends.map(t => t[metric as keyof EvaluationTrend] as number);
            const latest = values[values.length - 1];
            const previous = values[values.length - 2];
            const change = latest - previous;
            const average = values.reduce((sum, val) => sum + val, 0) / values.length;

            const Icon = component.icon;
            return (
              <div key={metric} className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Icon className="h-4 w-4" style={{ color: component.color }} />
                  <span className="text-xs font-medium text-gray-600">
                    {component.label}
                  </span>
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  {latest.toFixed(1)}
                </div>
                <div className="flex items-center space-x-1 text-xs">
                  <span className="text-gray-500">Avg: {average.toFixed(1)}</span>
                  {change !== 0 && (
                    <span
                      className={`font-medium ${
                        change > 0 ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {change > 0 ? "+" : ""}{change.toFixed(1)}
                    </span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default EvaluationChart;
