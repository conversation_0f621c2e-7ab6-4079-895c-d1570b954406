import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import SelectInput from "../ui/input/SelectInput";
import TextInput from "../ui/input/TextInput";
import TextAreaInput from "../ui/input/TextAreaInput";
import NumberInput from "../ui/input/NumberInput";

import { useCoach } from "../../context/CoachContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { CheckCircle, User, Brain, Target, Shield, Users, Heart } from "lucide-react";
import { createEvaluation } from "../../api/evaluations";

const CoachCreateEvaluation = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const preselectedCoacheeId = searchParams.get("coacheeId");

  const { myCoachees, loading, fetchMyCoachees } = useCoach();

  const [formData, setFormData] = useState({
    coacheeId: preselectedCoacheeId || "",
    title: "",
    notes: "",
    composure: 5,
    concentration: 5,
    confidence: 5,
    copeability: 5,
    cohesion: 5,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchMyCoachees();
  }, []);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setError(null);
    setSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.coacheeId) {
      setError("Please select a coachee");
      return;
    }

    // Validate scores are between 1-10
    const scores = [
      formData.composure,
      formData.concentration,
      formData.confidence,
      formData.copeability,
      formData.cohesion,
    ];

    if (scores.some((score) => score < 1 || score > 10)) {
      setError("All B3-5C scores must be between 1 and 10");
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      await createEvaluation({
        coacheeId: formData.coacheeId,
        title: formData.title || undefined,
        notes: formData.notes || undefined,
        composure: formData.composure,
        concentration: formData.concentration,
        confidence: formData.confidence,
        copeability: formData.copeability,
        cohesion: formData.cohesion,
      });

      const selectedCoachee = myCoachees.find((c) => c.id === formData.coacheeId);

      setSuccess(
        `Successfully created B3-5C evaluation for ${selectedCoachee?.firstName} ${selectedCoachee?.lastName}`
      );

      // Reset form
      setFormData({
        coacheeId: "",
        title: "",
        notes: "",
        composure: 5,
        concentration: 5,
        confidence: 5,
        copeability: 5,
        cohesion: 5,
      });

      // Redirect after a short delay
      setTimeout(() => {
        navigate("/dashboard/coachees");
      }, 2000);
    } catch (error: any) {
      setError(error.response?.data?.error || "Failed to create evaluation");
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedCoachee = myCoachees.find((c) => c.id === formData.coacheeId);
  const overallScore = Math.round(
    ((formData.composure +
      formData.concentration +
      formData.confidence +
      formData.copeability +
      formData.cohesion) /
      5) *
      10
  ) / 10;

  if (loading.coachees) {
    return <LoadingSpinner />;
  }

  const b3c5Components = [
    {
      key: "composure",
      label: "Composure",
      description: "Ability to remain calm under pressure",
      icon: Shield,
      color: "blue",
    },
    {
      key: "concentration",
      label: "Concentration",
      description: "Ability to focus and maintain attention",
      icon: Target,
      color: "green",
    },
    {
      key: "confidence",
      label: "Confidence",
      description: "Self-belief and positive mindset",
      icon: Heart,
      color: "red",
    },
    {
      key: "copeability",
      label: "Cope-ability",
      description: "Ability to handle stress and adversity",
      icon: Brain,
      color: "purple",
    },
    {
      key: "cohesion",
      label: "Cohesion",
      description: "Ability to work well with others and maintain team unity",
      icon: Users,
      color: "yellow",
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Create B3-5C Evaluation"
        description="Create a mental toughness evaluation for one of your coachees using the B3-5C model."
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Evaluation Form */}
        <Card
          header={
            <CardHeader
              title="Evaluation Details"
              description="Complete the B3-5C mental toughness assessment"
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Coachee Selection */}
            <div>
              <SelectInput
                label="Coachee"
                value={formData.coacheeId}
                onChange={(value) => handleInputChange("coacheeId", value)}
                options={[
                  { value: "", label: "Select a coachee..." },
                  ...myCoachees.map((coachee) => ({
                    value: coachee.id,
                    label: `${coachee.firstName} ${coachee.lastName} (${coachee.email})`,
                  })),
                ]}
                required
              />
              {selectedCoachee && (
                <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-green-900">
                      {selectedCoachee.firstName} {selectedCoachee.lastName}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-green-700">
                    {selectedCoachee.email}
                  </p>
                </div>
              )}
            </div>

            {/* Title */}
            <TextInput
              label="Title (Optional)"
              placeholder="e.g., Q1 2024 Assessment"
              value={formData.title}
              onChange={(value) => handleInputChange("title", value)}
            />

            {/* B3-5C Scores */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                B3-5C Mental Toughness Scores
              </h3>
              <p className="text-sm text-gray-600">
                Rate each component on a scale of 1-10 (1 = Very Poor, 10 = Excellent)
              </p>

              {b3c5Components.map(({ key, label, description, icon: Icon, color }) => (
                <div key={key} className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Icon className={`h-5 w-5 text-${color}-600`} />
                    <label className="text-sm font-medium text-gray-900">
                      {label}
                    </label>
                  </div>
                  <p className="text-xs text-gray-500 ml-7">{description}</p>
                  <div className="ml-7">
                    <NumberInput
                      value={formData[key as keyof typeof formData] as number}
                      onChange={(value) => handleInputChange(key, value)}
                      min={1}
                      max={10}
                      step={1}
                      required
                    />
                  </div>
                </div>
              ))}
            </div>

            {/* Notes */}
            <TextAreaInput
              label="Notes (Optional)"
              placeholder="Additional observations or comments about this evaluation..."
              value={formData.notes}
              onChange={(value) => handleInputChange("notes", value)}
              rows={4}
            />

            {error && <ErrorMessage message={error} />}

            {success && (
              <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-sm text-green-800">{success}</span>
              </div>
            )}

            <div className="flex space-x-3">
              <Button
                type="submit"
                disabled={isSubmitting || !formData.coacheeId}
                className="flex-1"
              >
                {isSubmitting ? "Creating Evaluation..." : "Create Evaluation"}
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate("/dashboard")}
              >
                Cancel
              </Button>
            </div>
          </form>
        </Card>

        {/* Evaluation Preview */}
        <Card
          header={
            <CardHeader
              title="Evaluation Preview"
              description="Review the evaluation details before creating"
            />
          }
        >
          {!formData.coacheeId ? (
            <div className="text-center py-8">
              <Brain className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No evaluation details
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Select a coachee to see the evaluation preview.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  Evaluation Summary
                </h4>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <User className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Coachee</p>
                      <p className="text-sm text-gray-600">
                        {selectedCoachee
                          ? `${selectedCoachee.firstName} ${selectedCoachee.lastName}`
                          : "No coachee selected"}
                      </p>
                    </div>
                  </div>

                  {formData.title && (
                    <div className="flex items-start space-x-3">
                      <Brain className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Title</p>
                        <p className="text-sm text-gray-600">{formData.title}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200">
                <h4 className="text-sm font-medium text-gray-900 mb-3">
                  B3-5C Scores
                </h4>
                <div className="space-y-2">
                  {b3c5Components.map(({ key, label, icon: Icon, color }) => (
                    <div key={key} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Icon className={`h-4 w-4 text-${color}-600`} />
                        <span className="text-sm text-gray-700">{label}</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">
                        {formData[key as keyof typeof formData]}/10
                      </span>
                    </div>
                  ))}
                  <div className="pt-2 border-t border-gray-100">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">
                        Overall Score
                      </span>
                      <span className="text-sm font-bold text-blue-600">
                        {overallScore}/10
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {formData.notes && (
                <div className="pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Notes</h4>
                  <p className="text-sm text-gray-600">{formData.notes}</p>
                </div>
              )}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default CoachCreateEvaluation;
