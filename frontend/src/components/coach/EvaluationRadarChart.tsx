import { Evaluation } from "../../api/evaluations";
import { Brain, Target, Shield, Users, Heart } from "lucide-react";

interface EvaluationRadarChartProps {
  evaluation: Evaluation;
  size?: number;
  showLabels?: boolean;
  showValues?: boolean;
}

const EvaluationRadarChart = ({
  evaluation,
  size = 300,
  showLabels = true,
  showValues = true,
}: EvaluationRadarChartProps) => {
  const center = size / 2;
  const radius = (size - 120) / 2; // Leave more space for wider labels
  const maxScore = 10;

  const b3c5Components = [
    {
      key: "composure",
      label: "Composure",
      value: evaluation.composure,
      icon: Shield,
      color: "#3B82F6",
      angle: 0, // Top
      width: 125,
    },
    {
      key: "concentration",
      label: "Concentration",
      value: evaluation.concentration,
      icon: Target,
      color: "#10B981",
      angle: 72, // 72 degrees clockwise
      width: 140,
    },
    {
      key: "confidence",
      label: "Confidence",
      value: evaluation.confidence,
      icon: Heart,
      color: "#EF4444",
      angle: 144, // 144 degrees clockwise
      width: 125,
    },
    {
      key: "copeability",
      label: "Cope-ability",
      value: evaluation.copeability,
      icon: Brain,
      color: "#8B5CF6",
      angle: 216, // 216 degrees clockwise
      width: 130,
    },
    {
      key: "cohesion",
      label: "Cohesion",
      value: evaluation.cohesion,
      icon: Users,
      color: "#F59E0B",
      angle: 288, // 288 degrees clockwise
      width: 115,
    },
  ];

  // Convert angle to radians and calculate x,y coordinates
  const getCoordinates = (angle: number, distance: number) => {
    const radians = ((angle - 90) * Math.PI) / 180; // -90 to start from top
    return {
      x: center + distance * Math.cos(radians),
      y: center + distance * Math.sin(radians),
    };
  };

  // Generate grid circles (score levels)
  const gridLevels = [2, 4, 6, 8, 10];

  // Generate axis lines
  const axisLines = b3c5Components.map((component) => {
    const end = getCoordinates(component.angle, radius);
    return {
      ...component,
      endX: end.x,
      endY: end.y,
    };
  });

  // Generate data polygon points
  const dataPoints = b3c5Components.map((component) => {
    const distance = (component.value / maxScore) * radius;
    return getCoordinates(component.angle, distance);
  });

  const polygonPoints = dataPoints
    .map((point) => `${point.x},${point.y}`)
    .join(" ");

  // Calculate label dimensions and positions
  const labelPositions = b3c5Components.map((component) => {
    const labelDistance = radius + 40; // Increased distance for wider labels
    const coords = getCoordinates(component.angle, labelDistance);

    // Calculate text width based on content (approximate)
    const labelText = showValues
      ? `${component.label} : ${component.value}`
      : component.label;
    const charWidth = 6; // Approximate character width in pixels for text-xs
    const iconWidth = 20; // Icon + spacing
    const padding = 16; // Left and right padding
    const textWidth = labelText.length * charWidth;
    const totalWidth = component.width ?? iconWidth + textWidth + padding;

    // Adjust position based on angle to prevent labels from going off-screen
    let adjustedX = coords.x;
    let textAnchor = "middle";

    // For labels on the right side, anchor to start and shift left
    if (component.angle >= 315 || component.angle <= 45) {
      // Top area - center the label
      adjustedX = coords.x;
      textAnchor = "middle";
    } else if (component.angle > 45 && component.angle < 135) {
      // Right side - anchor to start
      adjustedX = coords.x;
      textAnchor = "start";
    } else if (component.angle >= 135 && component.angle <= 225) {
      // Bottom area - center the label
      adjustedX = coords.x;
      textAnchor = "middle";
    } else {
      // Left side - anchor to end
      adjustedX = coords.x;
      textAnchor = "end";
    }

    return {
      ...component,
      x: adjustedX,
      y: coords.y,
      width: totalWidth,
      textAnchor,
      labelText,
    };
  });

  return (
    <div className="flex flex-col items-center space-y-4">
      <svg width={size} height={size} className="overflow-visible">
        {/* Grid circles */}
        {gridLevels.map((level) => (
          <circle
            key={level}
            cx={center}
            cy={center}
            r={(level / maxScore) * radius}
            fill="none"
            stroke="#e5e7eb"
            strokeWidth="1"
            strokeDasharray={level === 10 ? "none" : "2,2"}
          />
        ))}

        {/* Axis lines */}
        {axisLines.map((axis) => (
          <line
            key={axis.key}
            x1={center}
            y1={center}
            x2={axis.endX}
            y2={axis.endY}
            stroke="#d1d5db"
            strokeWidth="1"
          />
        ))}

        {/* Data polygon */}
        <polygon
          points={polygonPoints}
          fill="rgba(59, 130, 246, 0.2)"
          stroke="#3B82F6"
          strokeWidth="2"
          strokeLinejoin="round"
        />

        {/* Data points */}
        {dataPoints.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="4"
            fill="#3B82F6"
            stroke="white"
            strokeWidth="2"
          >
            <title>
              {b3c5Components[index].label}: {b3c5Components[index].value}/10
            </title>
          </circle>
        ))}

        {/* Grid level labels */}
        {gridLevels.map((level) => (
          <text
            key={level}
            x={center + 5}
            y={center - (level / maxScore) * radius + 4}
            className="text-xs fill-gray-400"
            textAnchor="start"
          >
            {level}
          </text>
        ))}

        {/* Component labels */}
        {showLabels &&
          labelPositions.map((label) => {
            const Icon = label.icon;

            // Calculate background rectangle position based on text anchor
            let rectX, iconX, textX;
            if (label.textAnchor === "start") {
              rectX = label.x;
              iconX = label.x + 8;
              textX = label.x + 28;
            } else if (label.textAnchor === "end") {
              rectX = label.x - label.width;
              iconX = label.x - label.width + 8;
              textX = label.x - label.width + 28;
            } else {
              // middle
              rectX = label.x - label.width / 2;
              iconX = label.x - label.width / 2 + 8;
              textX = label.x - label.width / 2 + 28;
            }

            return (
              <g key={label.key}>
                {/* Label background - sized to fit content */}
                <rect
                  x={rectX}
                  y={label.y - 12}
                  width={label.width}
                  height="24"
                  fill="white"
                  stroke="#e5e7eb"
                  strokeWidth="1"
                  rx="4"
                />

                {/* Icon */}
                <foreignObject x={iconX} y={label.y - 8} width="16" height="16">
                  <Icon className="h-4 w-4" style={{ color: label.color }} />
                </foreignObject>

                {/* Label text */}
                <text
                  x={textX}
                  y={label.y + 3}
                  className="text-xs fill-gray-700 font-medium"
                  textAnchor="start"
                >
                  {label.labelText}
                </text>
              </g>
            );
          })}

        {/* Center point */}
        <circle cx={center} cy={center} r="3" fill="#6b7280" />
      </svg>

      {/* Legend */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
        {b3c5Components.map((component) => {
          const Icon = component.icon;
          return (
            <div key={component.key} className="flex items-center space-x-2">
              <Icon className="h-4 w-4" style={{ color: component.color }} />
              <span className="text-gray-700">{component.label}</span>
              <span className="font-semibold text-gray-900">
                {component.value}/10
              </span>
            </div>
          );
        })}
      </div>

      {/* Overall Score */}
      <div className="text-center">
        <div className="inline-flex items-center space-x-2 bg-blue-50 rounded-lg px-4 py-2">
          <Brain className="h-5 w-5 text-blue-600" />
          <span className="text-sm text-blue-700">Overall Score:</span>
          <span className="text-lg font-bold text-blue-600">
            {Math.round(
              ((evaluation.composure +
                evaluation.concentration +
                evaluation.confidence +
                evaluation.copeability +
                evaluation.cohesion) /
                5) *
                10
            ) / 10}
            /10
          </span>
        </div>
      </div>
    </div>
  );
};

export default EvaluationRadarChart;
