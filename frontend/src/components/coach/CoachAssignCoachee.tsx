import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import TextInput from "../ui/input/TextInput";
import { useCoach } from "../../context/CoachContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { User, Mail, CheckCircle, AlertCircle } from "lucide-react";

const CoachAssignCoachee = () => {
  const navigate = useNavigate();
  const {
    availableUsers,
    myCoachees,
    loading,
    fetchAvailableUsers,
    assignCoacheeByEmail,
  } = useCoach();

  const [email, setEmail] = useState("");
  const [isAssigning, setIsAssigning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [filteredUsers, setFilteredUsers] = useState(availableUsers);

  useEffect(() => {
    fetchAvailableUsers();
  }, []);

  useEffect(() => {
    // Filter users based on email input and exclude already assigned coachees
    const assignedEmails = new Set(myCoachees.map((c) => c.email));
    const filtered = availableUsers.filter(
      (user) =>
        user.email.toLowerCase().includes(email.toLowerCase()) &&
        !assignedEmails.has(user.email)
    );
    setFilteredUsers(filtered);
  }, [email, availableUsers, myCoachees]);

  const handleAssignCoachee = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      setError("Please enter an email address");
      return;
    }

    setIsAssigning(true);
    setError(null);
    setSuccess(null);

    try {
      await assignCoacheeByEmail(email.trim());
      setSuccess(`Successfully assigned coachee: ${email}`);
      setEmail("");

      // Redirect to coachees page after a short delay
      setTimeout(() => {
        navigate("/dashboard/coachees");
      }, 2000);
    } catch (error: any) {
      setError(error.response?.data?.error || "Failed to assign coachee");
    } finally {
      setIsAssigning(false);
    }
  };

  const handleSelectUser = (userEmail: string) => {
    setEmail(userEmail);
    setError(null);
    setSuccess(null);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Assign New Coachee"
        description="Add a new coachee to your coaching list."
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Assignment Form */}
        <Card
          header={
            <CardHeader
              title="Assign by Email"
              description="Enter the email address of the coachee you want to assign"
            />
          }
        >
          <form onSubmit={handleAssignCoachee} className="space-y-4">
            <TextInput
              label="Coachee Email"
              value={email}
              onChange={(value) => setEmail(value)}
              placeholder="Enter coachee's email address"
              required
            />

            {error && <ErrorMessage message={error} />}

            {success && (
              <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-sm text-green-800">{success}</span>
              </div>
            )}

            <div className="flex space-x-3">
              <Button
                type="submit"
                disabled={isAssigning || !email.trim()}
                className="flex-1"
              >
                {isAssigning ? "Assigning..." : "Assign Coachee"}
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate("/dashboard/coachees")}
              >
                Cancel
              </Button>
            </div>
          </form>
        </Card>

        {/* Available Users */}
        <Card
          header={
            <CardHeader
              title="Available Coachees"
              description="Select from available users or search by typing an email"
            />
          }
        >
          {loading.users ? (
            <LoadingSpinner />
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {email &&
                filteredUsers.length === 0 &&
                availableUsers.length > 0 && (
                  <div className="text-center py-4">
                    <AlertCircle className="mx-auto h-8 w-8 text-yellow-500" />
                    <p className="mt-2 text-sm text-gray-600">
                      No users found matching "{email}"
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      You can still try to assign by email if the user exists
                    </p>
                  </div>
                )}

              {filteredUsers.length === 0 && !email && (
                <div className="text-center py-4">
                  <User className="mx-auto h-8 w-8 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-600">
                    No available coachees found
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    All users may already be assigned or there are no coachee
                    users
                  </p>
                </div>
              )}

              {filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleSelectUser(user.email)}
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-gray-600" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">
                        {user.firstName} {user.lastName}
                      </h3>
                      <div className="flex items-center text-sm text-gray-500">
                        <Mail className="h-3 w-3 mr-1" />
                        {user.email}
                      </div>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectUser(user.email);
                    }}
                  >
                    Select
                  </Button>
                </div>
              ))}
            </div>
          )}
        </Card>
      </div>

      {/* Instructions */}
      <Card
        header={
          <CardHeader
            title="Instructions"
            description="How to assign a new coachee"
          />
        }
      >
        <div className="space-y-3 text-sm text-gray-600">
          <div className="flex items-start space-x-2">
            <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
              1
            </span>
            <p>
              Enter the email address of the person you want to assign as your
              coachee.
            </p>
          </div>
          <div className="flex items-start space-x-2">
            <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
              2
            </span>
            <p>
              You can select from the list of available users or type any email
              address.
            </p>
          </div>
          <div className="flex items-start space-x-2">
            <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
              3
            </span>
            <p>
              The system will create the coaching relationship and notify the
              coachee.
            </p>
          </div>
          <div className="flex items-start space-x-2">
            <span className="flex-shrink-0 w-5 h-5 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
              4
            </span>
            <p>
              Once assigned, you can view and manage the coachee from your
              dashboard.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CoachAssignCoachee;
