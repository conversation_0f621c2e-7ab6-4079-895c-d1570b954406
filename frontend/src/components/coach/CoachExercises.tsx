import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import TextInput from "../ui/input/TextInput";
import RichTextDisplay from "../ui/RichTextDisplay";
import { useCoach } from "../../context/CoachContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { BookOpen, Plus, Search, Calendar } from "lucide-react";
import { Exercise } from "../../types/api/exercises.types";

const CoachExercises = () => {
  const { allExercises, loading, fetchAllExercises } = useCoach();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedExercise, setSelectedExercise] = useState<Exercise | null>(
    null
  );

  useEffect(() => {
    fetchAllExercises();
  }, []);

  const filteredExercises = allExercises.filter(
    (exercise) =>
      exercise.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (exercise.description &&
        exercise.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleExerciseSelect = (exercise: Exercise) => {
    setSelectedExercise(exercise);
  };

  if (loading.exercises) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Exercises"
        description="Browse and manage available exercises for your coachees."
        actionButton={{
          label: "Create Assignment",
          to: "/dashboard/create-assignment",
        }}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Exercises List */}
        <Card
          header={
            <CardHeader
              title="Available Exercises"
              description={`${filteredExercises.length} exercises available`}
            />
          }
        >
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <TextInput
                placeholder="Search exercises..."
                value={searchTerm}
                onChange={(value) => setSearchTerm(value)}
                className="pl-10"
              />
            </div>

            {/* Exercise List */}
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredExercises.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {searchTerm
                      ? "No exercises found"
                      : "No exercises available"}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm
                      ? `No exercises match "${searchTerm}"`
                      : "There are no exercises available at the moment."}
                  </p>
                </div>
              ) : (
                filteredExercises.map((exercise) => (
                  <div
                    key={exercise.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedExercise?.id === exercise.id
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:bg-gray-50"
                    }`}
                    onClick={() => handleExerciseSelect(exercise)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-sm font-medium text-gray-900">
                          {exercise.name}
                        </h3>
                        {exercise.description && (
                          <div className="mt-1 text-sm text-gray-500 line-clamp-2">
                            <RichTextDisplay
                              content={exercise.description}
                              className="text-sm text-gray-500"
                            />
                          </div>
                        )}
                        <div className="mt-2 flex items-center text-xs text-gray-500">
                          <Calendar className="h-3 w-3 mr-1" />
                          Created{" "}
                          {new Date(exercise.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="ml-4 flex-shrink-0">
                        <Link
                          to={`/dashboard/create-assignment?exerciseId=${exercise.id}`}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Button size="sm" variant="secondary">
                            <Plus className="h-3 w-3 mr-1" />
                            Assign
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </Card>

        {/* Exercise Details */}
        <Card
          header={
            <CardHeader
              title="Exercise Details"
              description={
                selectedExercise
                  ? "Detailed information about the selected exercise"
                  : "Select an exercise to view details"
              }
            />
          }
        >
          {!selectedExercise ? (
            <div className="text-center py-8">
              <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No exercise selected
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Click on an exercise from the list to view its details.
              </p>
            </div>
          ) : (
            <ExerciseDetailsView exercise={selectedExercise} />
          )}
        </Card>
      </div>
    </div>
  );
};

interface ExerciseDetailsViewProps {
  exercise: Exercise;
}

const ExerciseDetailsView = ({ exercise }: ExerciseDetailsViewProps) => {
  const questions = Array.isArray(exercise.questions) ? exercise.questions : [];

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-3">
          {exercise.name}
        </h4>
        {exercise.description && (
          <RichTextDisplay
            content={exercise.description}
            className="text-gray-600"
          />
        )}
      </div>

      {/* Exercise Metadata */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="text-sm font-medium text-gray-500">Created</label>
          <p className="text-sm text-gray-900">
            {new Date(exercise.createdAt).toLocaleDateString()}
          </p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500">Questions</label>
          <p className="text-sm text-gray-900">{questions.length} questions</p>
        </div>
      </div>

      {/* Questions Preview */}
      <div>
        <h5 className="text-md font-medium text-gray-900 mb-3">
          Questions Preview
        </h5>
        {questions.length === 0 ? (
          <p className="text-sm text-gray-500">No questions available</p>
        ) : (
          <div className="space-y-3">
            {questions.slice(0, 3).map((question: any, index: number) => (
              <div
                key={question.id || index}
                className="bg-gray-50 p-3 rounded-lg"
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs font-medium text-gray-500 uppercase">
                    {question.type || "Question"}
                  </span>
                  {question.required && (
                    <span className="text-xs text-red-500">Required</span>
                  )}
                </div>
                <p className="text-sm text-gray-900">
                  {question.prompt ||
                    question.text ||
                    "Question text not available"}
                </p>
              </div>
            ))}
            {questions.length > 3 && (
              <p className="text-sm text-gray-500 text-center">
                ... and {questions.length - 3} more questions
              </p>
            )}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex space-x-3 pt-4 border-t border-gray-200">
        <Link
          to={`/dashboard/create-assignment?exerciseId=${exercise.id}`}
          className="flex-1"
        >
          <Button className="w-full">
            <Plus className="h-4 w-4 mr-2" />
            Create Assignment
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default CoachExercises;
