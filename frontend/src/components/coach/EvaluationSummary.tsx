import { Evaluation } from "../../api/evaluations";
import {
  Brain,
  Target,
  Shield,
  Users,
  Heart,
  Calendar,
  User,
} from "lucide-react";
import { Card, CardHeader } from "../ui/Card";
import EvaluationRadarChart from "./EvaluationRadarChart";

interface EvaluationSummaryProps {
  evaluation: Evaluation;
  showCoacheeInfo?: boolean;
  showCoachInfo?: boolean;
  compact?: boolean;
}

const EvaluationSummary = ({
  evaluation,
  showCoacheeInfo = false,
  showCoachInfo = false,
  compact = false,
}: EvaluationSummaryProps) => {
  const b3c5Components = [
    {
      key: "composure",
      label: "Composure",
      description: "Ability to remain calm under pressure",
      icon: Shield,
      color: "blue",
      value: evaluation.composure,
    },
    {
      key: "concentration",
      label: "Concentration",
      description: "Ability to focus and maintain attention",
      icon: Target,
      color: "green",
      value: evaluation.concentration,
    },
    {
      key: "confidence",
      label: "Confidence",
      description: "Self-belief and positive mindset",
      icon: Heart,
      color: "red",
      value: evaluation.confidence,
    },
    {
      key: "copeability",
      label: "Cope-ability",
      description: "Ability to handle stress and adversity",
      icon: Brain,
      color: "purple",
      value: evaluation.copeability,
    },
    {
      key: "cohesion",
      label: "Cohesion",
      description: "Ability to work well with others and maintain team unity",
      icon: Users,
      color: "yellow",
      value: evaluation.cohesion,
    },
  ];

  const overallScore =
    Math.round(
      ((evaluation.composure +
        evaluation.concentration +
        evaluation.confidence +
        evaluation.copeability +
        evaluation.cohesion) /
        5) *
        10
    ) / 10;

  const getScoreColor = (score: number) => {
    if (score >= 8) return "text-green-600 bg-green-50";
    if (score >= 6) return "text-yellow-600 bg-yellow-50";
    if (score >= 4) return "text-orange-600 bg-orange-50";
    return "text-red-600 bg-red-50";
  };

  const getScoreLabel = (score: number) => {
    if (score >= 8) return "Excellent";
    if (score >= 6) return "Good";
    if (score >= 4) return "Fair";
    return "Needs Improvement";
  };

  if (compact) {
    return (
      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-gray-900">
              {evaluation.title || "B3-5C Evaluation"}
            </span>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-blue-600">
              {overallScore}/10
            </div>
            <div className="text-xs text-gray-500">Overall</div>
          </div>
        </div>

        <div className="grid grid-cols-5 gap-2">
          {b3c5Components.map(({ key, label, icon: Icon, color, value }) => (
            <div key={key} className="text-center">
              <Icon className={`h-4 w-4 text-${color}-600 mx-auto mb-1`} />
              <div className="text-sm font-medium text-gray-900">{value}</div>
              <div className="text-xs text-gray-500 truncate">{label}</div>
            </div>
          ))}
        </div>

        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3" />
              <span>{new Date(evaluation.createdAt).toLocaleDateString()}</span>
            </div>
            {showCoachInfo && (
              <div className="flex items-center space-x-1">
                <User className="h-3 w-3" />
                <span>
                  {evaluation.coach.firstName} {evaluation.coach.lastName}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card
      header={
        <CardHeader
          title={evaluation.title || "B3-5C Mental Toughness Evaluation"}
          description={`Created on ${new Date(
            evaluation.createdAt
          ).toLocaleDateString()}`}
        />
      }
    >
      <div className="space-y-6">
        {/* Coachee/Coach Info */}
        {(showCoacheeInfo || showCoachInfo) && (
          <div className="flex items-center justify-between">
            {showCoacheeInfo && (
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Coachee</p>
                  <p className="text-sm text-gray-600">
                    {evaluation.coachee.firstName} {evaluation.coachee.lastName}
                  </p>
                </div>
              </div>
            )}
            {showCoachInfo && (
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Coach</p>
                  <p className="text-sm text-gray-600">
                    {evaluation.coach.firstName} {evaluation.coach.lastName}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Overall Score */}
        <div className="text-center">
          <div className="inline-flex items-center space-x-3 bg-blue-50 rounded-lg p-4">
            <Brain className="h-8 w-8 text-blue-600" />
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {overallScore}/10
              </div>
              <div className="text-sm text-blue-700">Overall Score</div>
              <div className="text-xs text-blue-600">
                {getScoreLabel(overallScore)}
              </div>
            </div>
          </div>
        </div>

        {/* B3-5C Component Scores */}
        <div className="space-y-6">
          <h4 className="text-lg font-medium text-gray-900">
            Component Scores
          </h4>

          {/* Radar Chart */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h5 className="text-md font-medium text-gray-900 mb-4 text-center">
              B3-5C Radar Chart
            </h5>
            <EvaluationRadarChart
              evaluation={evaluation}
              size={280}
              showLabels={true}
              showValues={true}
            />
          </div>

          {/* Detailed Component Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {b3c5Components.map(
              ({ key, label, description, icon: Icon, color, value }) => (
                <div
                  key={key}
                  className={`p-4 rounded-lg border ${getScoreColor(value)}`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Icon className={`h-5 w-5 text-${color}-600`} />
                      <span className="font-medium text-gray-900">{label}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold">{value}/10</div>
                      <div className="text-xs">{getScoreLabel(value)}</div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">{description}</p>
                </div>
              )
            )}
          </div>
        </div>

        {/* Notes */}
        {evaluation.notes && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-900">Notes</h4>
            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-sm text-gray-700">{evaluation.notes}</p>
            </div>
          </div>
        )}

        {/* Metadata */}
        <div className="pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>
                Created: {new Date(evaluation.createdAt).toLocaleDateString()}
              </span>
            </div>
            {evaluation.updatedAt !== evaluation.createdAt && (
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>
                  Updated: {new Date(evaluation.updatedAt).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EvaluationSummary;
