import {
  Home,
  Users,
  UserPlus,
  BookOpen,
  Plus,
  Eye,
  LogOut,
  Calendar,
  ClipboardList,
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";

const CoachSidebar = () => {
  const location = useLocation();
  const { signOut } = useAuth();

  const navigation = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: Home,
      current:
        location.pathname === "/dashboard" ||
        location.pathname === "/dashboard/",
    },
    {
      name: "My Coachees",
      href: "/dashboard/coachees",
      icon: Users,
      current: location.pathname.startsWith("/dashboard/coachees"),
    },
    {
      name: "Assign Coachee",
      href: "/dashboard/assign-coachee",
      icon: UserPlus,
      current: location.pathname === "/dashboard/assign-coachee",
    },
    {
      name: "Exercises",
      href: "/dashboard/exercises",
      icon: BookOpen,
      current: location.pathname.startsWith("/dashboard/exercises"),
    },
    {
      name: "Create Assignment",
      href: "/dashboard/create-assignment",
      icon: Plus,
      current: location.pathname === "/dashboard/create-assignment",
    },
    {
      name: "Visualizations",
      href: "/dashboard/visualizations",
      icon: Eye,
      current: location.pathname === "/dashboard/visualizations",
    },
    {
      name: "Create Vis Assignment",
      href: "/dashboard/create-visualization-assignment",
      icon: Plus,
      current:
        location.pathname === "/dashboard/create-visualization-assignment",
    },
    {
      name: "Create Periodization",
      href: "/dashboard/create-periodization",
      icon: Calendar,
      current: location.pathname === "/dashboard/create-periodization",
    },
    {
      name: "Manage Assignments",
      href: "/dashboard/assignments",
      icon: ClipboardList,
      current: location.pathname === "/dashboard/assignments",
    },
  ];

  const handleLogout = () => {
    signOut();
  };

  return (
    <div className="flex flex-col w-64 bg-white border-r border-gray-200 h-full">
      {/* Logo */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-900">Coach Panel</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                item.current
                  ? "bg-blue-100 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              }`}
            >
              <Icon
                className={`mr-3 h-5 w-5 ${
                  item.current
                    ? "text-blue-500"
                    : "text-gray-400 group-hover:text-gray-500"
                }`}
              />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* User Actions */}
      <div className="px-4 py-4 border-t border-gray-200">
        <button
          onClick={handleLogout}
          className="group flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors"
        >
          <LogOut className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
          Sign Out
        </button>
      </div>
    </div>
  );
};

export default CoachSidebar;
