import { useState, useEffect } from "react";
import { Card, CardHeader } from "../ui/Card";
import PerformanceRadarChart from "../ui/PerformanceRadarChart";
import { 
  PerformanceProfile, 
  PerformanceCategory, 
  LatestActiveGoalsResponse 
} from "../../types/api/performanceProfile.types";
import { 
  getPerformanceProfiles, 
  getLatestActiveGoals 
} from "../../api/performanceProfile";
import { Calendar, Target, TrendingUp } from "lucide-react";
import { LoadingSpinner } from "../ui/LoadingSpinner";

interface CoacheePerformanceProfilingProps {
  coacheeId: string;
  coacheeName: string;
}

const CoacheePerformanceProfiling = ({ coacheeId, coacheeName }: CoacheePerformanceProfilingProps) => {
  const [profiles, setProfiles] = useState<PerformanceProfile[]>([]);
  const [latestGoals, setLatestGoals] = useState<LatestActiveGoalsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [profilesData, latestGoalsData] = await Promise.all([
        getPerformanceProfiles(coacheeId),
        getLatestActiveGoals(coacheeId),
      ]);
      
      setProfiles(profilesData);
      setLatestGoals(latestGoalsData);
    } catch (error: any) {
      console.error("Error fetching performance data:", error);
      setError("Failed to load performance data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [coacheeId]);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Performance Profiling - {coacheeName}
        </h3>
        <p className="text-sm text-gray-600">
          View performance goals and progress across different categories
        </p>
      </div>

      {/* Radar Charts Section */}
      {latestGoals ? (
        <div className="space-y-6">
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span>Current Performance Overview</span>
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Object.entries(PerformanceCategory).map(([key, category]) => {
                const categoryGoals = latestGoals.goalsByCategory[category] || [];
                
                return (
                  <Card key={category}>
                    <div className="p-6">
                      <PerformanceRadarChart
                        goals={categoryGoals}
                        category={category}
                        size={250}
                        showLabels={true}
                        showValues={true}
                        title={category.charAt(0) + category.slice(1).toLowerCase()}
                      />
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>
      ) : (
        <Card>
          <div className="p-8 text-center">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              No Active Performance Profile
            </h4>
            <p className="text-gray-500">
              This coachee hasn't created any performance profiles yet.
            </p>
          </div>
        </Card>
      )}

      {/* Performance Profile Records Table */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900 flex items-center space-x-2">
          <Calendar className="h-5 w-5 text-blue-600" />
          <span>Performance Profile History</span>
        </h4>
        
        {profiles.length === 0 ? (
          <Card>
            <div className="p-6 text-center">
              <p className="text-gray-500">No performance profiles found.</p>
            </div>
          </Card>
        ) : (
          <Card>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Timeline
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Goals Summary
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {profiles.map((profile) => {
                    const activeGoals = profile.goals.filter(goal => goal.isActive);
                    const goalsByCategory = activeGoals.reduce((acc, goal) => {
                      acc[goal.category] = (acc[goal.category] || 0) + 1;
                      return acc;
                    }, {} as Record<PerformanceCategory, number>);

                    // Calculate overall progress
                    const totalProgress = activeGoals.length > 0 
                      ? activeGoals.reduce((sum, goal) => sum + (goal.currentRating / goal.targetRating), 0) / activeGoals.length
                      : 0;

                    const daysRemaining = Math.ceil((new Date(profile.targetDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                    const isOverdue = daysRemaining < 0;

                    return (
                      <tr key={profile.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {new Date(profile.startDate).toLocaleDateString()} - {new Date(profile.targetDate).toLocaleDateString()}
                              </div>
                              <div className={`text-sm ${isOverdue ? 'text-red-600' : 'text-gray-500'}`}>
                                {isOverdue 
                                  ? `${Math.abs(daysRemaining)} days overdue`
                                  : `${daysRemaining} days remaining`
                                }
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="space-y-1">
                            {Object.entries(goalsByCategory).map(([category, count]) => (
                              <div key={category} className="flex items-center space-x-2">
                                <span className="text-xs px-2 py-1 bg-gray-100 rounded-full">
                                  {category}: {count} goals
                                </span>
                              </div>
                            ))}
                            {activeGoals.length === 0 && (
                              <span className="text-sm text-gray-500">No active goals</span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${Math.min(totalProgress * 100, 100)}%` }}
                              />
                            </div>
                            <span className="text-sm text-gray-600">
                              {Math.round(totalProgress * 100)}%
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(profile.createdAt).toLocaleDateString()}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default CoacheePerformanceProfiling;
