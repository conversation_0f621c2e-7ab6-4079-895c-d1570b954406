import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import SelectInput from "../ui/input/SelectInput";
import RichTextDisplay from "../ui/RichTextDisplay";

import { useCoach } from "../../context/CoachContext";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { CheckCircle, Calendar, User, BookOpen } from "lucide-react";
import DateInput from "../ui/input/DateInput";

const CoachCreateAssignment = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const preselectedExerciseId = searchParams.get("exerciseId");

  const {
    myCoachees,
    allExercises,
    loading,
    fetchMyCoachees,
    fetchAllExercises,
    createNewAssignment,
  } = useCoach();

  const [formData, setFormData] = useState({
    exerciseId: preselectedExerciseId || "",
    coacheeId: "",
    dueDate: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchMyCoachees();
    fetchAllExercises();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setError(null);
    setSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.exerciseId || !formData.coacheeId) {
      setError("Please select both an exercise and a coachee");
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      await createNewAssignment({
        exerciseId: formData.exerciseId,
        coacheeId: formData.coacheeId,
        dueDate: formData.dueDate || undefined,
      });

      const selectedExercise = allExercises.find(
        (e) => e.id === formData.exerciseId
      );
      const selectedCoachee = myCoachees.find(
        (c) => c.id === formData.coacheeId
      );

      setSuccess(
        `Successfully assigned "${selectedExercise?.name}" to ${selectedCoachee?.firstName} ${selectedCoachee?.lastName}`
      );

      // Reset form
      setFormData({
        exerciseId: "",
        coacheeId: "",
        dueDate: "",
      });

      // Redirect after a short delay
      setTimeout(() => {
        navigate("/dashboard/coachees");
      }, 2000);
    } catch (error: any) {
      setError(error.response?.data?.error || "Failed to create assignment");
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedExercise = allExercises.find(
    (e) => e.id === formData.exerciseId
  );
  const selectedCoachee = myCoachees.find((c) => c.id === formData.coacheeId);

  if (loading.exercises || loading.coachees) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Create Assignment"
        description="Assign an exercise to one of your coachees."
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Assignment Form */}
        <Card
          header={
            <CardHeader
              title="Assignment Details"
              description="Select the exercise and coachee for this assignment"
            />
          }
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Exercise Selection */}
            <div>
              <SelectInput
                label="Exercise"
                value={formData.exerciseId}
                onChange={(value) => handleInputChange("exerciseId", value)}
                options={[
                  { value: "", label: "Select an exercise..." },
                  ...allExercises.map((exercise) => ({
                    value: exercise.id,
                    label: exercise.name,
                  })),
                ]}
                required
              />
              {selectedExercise && (
                <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-blue-900">
                      {selectedExercise.name}
                    </span>
                  </div>
                  {selectedExercise.description && (
                    <p className="mt-1 text-sm text-blue-700 line-clamp-2">
                      {selectedExercise.description}
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Coachee Selection */}
            <div>
              <SelectInput
                label="Coachee"
                value={formData.coacheeId}
                onChange={(value) => handleInputChange("coacheeId", value)}
                options={[
                  { value: "", label: "Select a coachee..." },
                  ...myCoachees.map((coachee) => ({
                    value: coachee.id,
                    label: `${coachee.firstName} ${coachee.lastName} (${coachee.email})`,
                  })),
                ]}
                required
              />
              {selectedCoachee && (
                <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-green-900">
                      {selectedCoachee.firstName} {selectedCoachee.lastName}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-green-700">
                    {selectedCoachee.email}
                  </p>
                </div>
              )}
            </div>

            {/* Due Date */}
            <div>
              <div className="flex flex-col w-full my-2">
                <DateInput
                  label="Due Date (Optional)"
                  value={formData.dueDate}
                  onChange={(value) => handleInputChange("dueDate", value)}
                  min={new Date().toISOString().split("T")[0]}
                />
              </div>
              {formData.dueDate && (
                <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-yellow-600 mr-2" />
                    <span className="text-sm font-medium text-yellow-900">
                      Due: {new Date(formData.dueDate).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {error && <ErrorMessage message={error} />}

            {success && (
              <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-sm text-green-800">{success}</span>
              </div>
            )}

            <div className="flex space-x-3">
              <Button
                type="submit"
                disabled={
                  isSubmitting || !formData.exerciseId || !formData.coacheeId
                }
                className="flex-1"
              >
                {isSubmitting ? "Creating Assignment..." : "Create Assignment"}
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate("/dashboard")}
              >
                Cancel
              </Button>
            </div>
          </form>
        </Card>

        {/* Assignment Preview */}
        <Card
          header={
            <CardHeader
              title="Assignment Preview"
              description="Review the assignment details before creating"
            />
          }
        >
          {!formData.exerciseId && !formData.coacheeId ? (
            <div className="text-center py-8">
              <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No assignment details
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Select an exercise and coachee to see the assignment preview.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  Assignment Summary
                </h4>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <BookOpen className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Exercise
                      </p>
                      <p className="text-sm text-gray-600">
                        {selectedExercise?.name || "No exercise selected"}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <User className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Coachee
                      </p>
                      <p className="text-sm text-gray-600">
                        {selectedCoachee
                          ? `${selectedCoachee.firstName} ${selectedCoachee.lastName}`
                          : "No coachee selected"}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Calendar className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Due Date
                      </p>
                      <p className="text-sm text-gray-600">
                        {formData.dueDate
                          ? new Date(formData.dueDate).toLocaleDateString()
                          : "No due date set"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {selectedExercise && (
                <div className="pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    Exercise Details
                  </h4>
                  {selectedExercise.description && (
                    <RichTextDisplay
                      content={selectedExercise.description}
                      className="text-gray-600"
                    />
                  )}
                </div>
              )}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default CoachCreateAssignment;
