import React, { useState, useEffect } from "react";
import { Card } from "../ui/Card";
import { Button } from "../ui/Button";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { DataTable } from "../ui/DataTable";
import { Column } from "../../types/components.types";
import { Label } from "../ui/Label";
import {
  Calendar,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  BookOpen,
  Filter,
  RefreshCw,
} from "lucide-react";
import {
  getAllAssignments,
  updateAssignment,
  deleteAssignment,
} from "../../api/assignments";
import {
  Assignment,
  AssignmentStatus,
} from "../../types/api/assignments.types";

interface EditAssignmentData {
  id: string;
  dueDate: string;
  status: AssignmentStatus;
}

const CoachAssignments: React.FC = () => {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [filteredAssignments, setFilteredAssignments] = useState<Assignment[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingAssignment, setEditingAssignment] =
    useState<EditAssignmentData | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [selectedAssignments, setSelectedAssignments] = useState<Set<string>>(
    new Set()
  );
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);

  // Filters
  const [statusFilter, setStatusFilter] = useState<AssignmentStatus | "ALL">(
    "ALL"
  );
  const [coacheeFilter, setCoacheeFilter] = useState<string>("ALL");
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    fetchAssignments();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [assignments, statusFilter, coacheeFilter, searchTerm]);

  const fetchAssignments = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await getAllAssignments();
      setAssignments(data);
    } catch (err) {
      console.error("Error fetching assignments:", err);
      setError("Failed to fetch assignments");
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...assignments];

    // Status filter
    if (statusFilter !== "ALL") {
      filtered = filtered.filter(
        (assignment) => assignment.status === statusFilter
      );
    }

    // Coachee filter
    if (coacheeFilter !== "ALL") {
      filtered = filtered.filter(
        (assignment) => assignment.coacheeId === coacheeFilter
      );
    }

    // Search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (assignment) =>
          assignment.exercise.name.toLowerCase().includes(term) ||
          `${assignment.coachee?.firstName} ${assignment.coachee?.lastName}`
            .toLowerCase()
            .includes(term)
      );
    }

    setFilteredAssignments(filtered);
  };

  const handleEditAssignment = (assignment: Assignment) => {
    setEditingAssignment({
      id: assignment.id,
      dueDate: assignment.dueDate ? assignment.dueDate.split("T")[0] : "",
      status: assignment.status,
    });
  };

  const handleUpdateAssignment = async () => {
    if (!editingAssignment) return;

    try {
      setIsUpdating(true);
      await updateAssignment(editingAssignment.id, {
        dueDate: editingAssignment.dueDate || undefined,
        status: editingAssignment.status,
      });

      setEditingAssignment(null);
      await fetchAssignments(); // Refresh the list
    } catch (err) {
      console.error("Error updating assignment:", err);
      setError("Failed to update assignment");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteAssignment = async (assignmentId: string) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this assignment? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      setIsDeleting(assignmentId);
      await deleteAssignment(assignmentId);
      await fetchAssignments(); // Refresh the list
    } catch (err) {
      console.error("Error deleting assignment:", err);
      setError("Failed to delete assignment");
    } finally {
      setIsDeleting(null);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedAssignments.size === 0) return;

    if (
      !window.confirm(
        `Are you sure you want to delete ${selectedAssignments.size} assignment(s)? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      setIsBulkDeleting(true);
      // Delete assignments in parallel
      await Promise.all(
        Array.from(selectedAssignments).map((id) => deleteAssignment(id))
      );
      setSelectedAssignments(new Set());
      await fetchAssignments(); // Refresh the list
    } catch (err) {
      console.error("Error deleting assignments:", err);
      setError("Failed to delete some assignments");
    } finally {
      setIsBulkDeleting(false);
    }
  };

  const handleSelectAssignment = (assignmentId: string, checked: boolean) => {
    const newSelected = new Set(selectedAssignments);
    if (checked) {
      newSelected.add(assignmentId);
    } else {
      newSelected.delete(assignmentId);
    }
    setSelectedAssignments(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedAssignments(new Set(filteredAssignments.map((a) => a.id)));
    } else {
      setSelectedAssignments(new Set());
    }
  };

  const isAllSelected =
    filteredAssignments.length > 0 &&
    filteredAssignments.every((assignment) =>
      selectedAssignments.has(assignment.id)
    );
  const isIndeterminate = selectedAssignments.size > 0 && !isAllSelected;

  const getStatusIcon = (status: AssignmentStatus) => {
    switch (status) {
      case "PENDING":
        return Clock;
      case "IN_PROGRESS":
        return Edit;
      case "COMPLETED":
        return CheckCircle;
      case "OVERDUE":
        return AlertCircle;
      default:
        return Clock;
    }
  };

  const getStatusColor = (status: AssignmentStatus) => {
    switch (status) {
      case "PENDING":
        return "blue";
      case "IN_PROGRESS":
        return "yellow";
      case "COMPLETED":
        return "green";
      case "OVERDUE":
        return "red";
      default:
        return "gray";
    }
  };

  const columns: Column<Assignment>[] = [
    {
      header: "",
      accessorKey: "id",
      cell: (assignment) => (
        <input
          type="checkbox"
          checked={selectedAssignments.has(assignment.id)}
          onChange={(e) =>
            handleSelectAssignment(assignment.id, e.target.checked)
          }
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
      ),
    },
    {
      header: "Exercise",
      accessorKey: "exercise",
      cell: (assignment) => (
        <div className="flex items-center space-x-3">
          <BookOpen className="h-5 w-5 text-blue-600" />
          <div>
            <div className="font-medium text-gray-900">
              {assignment.exercise.name}
            </div>
            <div className="text-sm text-gray-500">
              ID: {assignment.exercise.id}
            </div>
          </div>
        </div>
      ),
    },
    {
      header: "Coachee",
      accessorKey: "coachee",
      cell: (assignment) => (
        <div className="flex items-center space-x-3">
          <User className="h-5 w-5 text-green-600" />
          <div>
            <div className="font-medium text-gray-900">
              {assignment.coachee?.firstName} {assignment.coachee?.lastName}
            </div>
            <div className="text-sm text-gray-500">
              {assignment.coachee?.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: (assignment) => (
        <Label
          icon={getStatusIcon(assignment.status)}
          color={getStatusColor(assignment.status)}
          text={assignment.status.replace("_", " ")}
        />
      ),
    },
    {
      header: "Due Date",
      accessorKey: "dueDate",
      cell: (assignment) => (
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-900">
            {assignment.dueDate
              ? new Date(assignment.dueDate).toLocaleDateString("en-US", {
                  weekday: "short",
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })
              : "No due date"}
          </span>
        </div>
      ),
    },
    {
      header: "Created",
      accessorKey: "createdAt",
      cell: (assignment) => (
        <span className="text-sm text-gray-500">
          {new Date(assignment.createdAt).toLocaleDateString()}
        </span>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (assignment) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEditAssignment(assignment)}
            className="flex items-center space-x-1"
          >
            <Edit className="h-4 w-4" />
            <span>Edit</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteAssignment(assignment.id)}
            disabled={isDeleting === assignment.id}
            className="flex items-center space-x-1 text-red-600 hover:text-red-700"
          >
            {isDeleting === assignment.id ? (
              <LoadingSpinner size="sm" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            <span>Delete</span>
          </Button>
        </div>
      ),
    },
  ];

  // Get unique coachees for filter
  const uniqueCoachees = Array.from(
    new Map(
      assignments.map((assignment) => [
        assignment.coacheeId,
        {
          id: assignment.coacheeId,
          name: `${assignment.coachee?.firstName} ${assignment.coachee?.lastName}`,
        },
      ])
    ).values()
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Calendar className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">
            Assignment Management
          </h1>
        </div>
        <Button
          onClick={fetchAssignments}
          className="flex items-center space-x-2"
          disabled={isLoading}
        >
          <RefreshCw className="h-4 w-4" />
          <span>Refresh</span>
        </Button>
      </div>

      {error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <div className="flex items-center space-x-3">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <p className="text-red-700">{error}</p>
          </div>
        </Card>
      )}

      {/* Filters */}
      <Card className="p-4">
        <div className="flex items-center space-x-4 mb-4">
          <Filter className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-medium text-gray-900">Filters</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) =>
                setStatusFilter(e.target.value as AssignmentStatus | "ALL")
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="ALL">All Statuses</option>
              <option value="PENDING">Pending</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="COMPLETED">Completed</option>
              <option value="OVERDUE">Overdue</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Coachee
            </label>
            <select
              value={coacheeFilter}
              onChange={(e) => setCoacheeFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="ALL">All Coachees</option>
              {uniqueCoachees.map((coachee) => (
                <option key={coachee.id} value={coachee.id}>
                  {coachee.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search exercises or coachees..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </Card>

      {/* Assignments Table */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-medium text-gray-900">
              Assignments ({filteredAssignments.length})
            </h3>
            {selectedAssignments.size > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  {selectedAssignments.size} selected
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkDelete}
                  disabled={isBulkDeleting}
                  className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                >
                  {isBulkDeleting ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                  <span>Delete Selected</span>
                </Button>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <label className="flex items-center space-x-2 text-sm text-gray-600">
              <input
                type="checkbox"
                checked={isAllSelected}
                ref={(input) => {
                  if (input) input.indeterminate = isIndeterminate;
                }}
                onChange={(e) => handleSelectAll(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span>Select All</span>
            </label>
          </div>
        </div>
        <DataTable columns={columns} data={filteredAssignments} />
      </Card>

      {/* Edit Assignment Modal */}
      {editingAssignment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Edit Assignment
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Due Date
                </label>
                <input
                  type="date"
                  value={editingAssignment.dueDate}
                  onChange={(e) =>
                    setEditingAssignment({
                      ...editingAssignment,
                      dueDate: e.target.value,
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={editingAssignment.status}
                  onChange={(e) =>
                    setEditingAssignment({
                      ...editingAssignment,
                      status: e.target.value as AssignmentStatus,
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="PENDING">Pending</option>
                  <option value="IN_PROGRESS">In Progress</option>
                  <option value="COMPLETED">Completed</option>
                  <option value="OVERDUE">Overdue</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setEditingAssignment(null)}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpdateAssignment}
                disabled={isUpdating}
                className="flex items-center space-x-2"
              >
                {isUpdating ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                <span>Update</span>
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default CoachAssignments;
