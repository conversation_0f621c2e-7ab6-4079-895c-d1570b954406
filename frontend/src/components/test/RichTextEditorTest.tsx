import { useState } from "react";
import RichTextEditor from "../ui/input/RichTextEditor";
import { uploadImageWithFallback } from "../../utils/imageUpload";

const RichTextEditorTest = () => {
  const [content1, setContent1] = useState("");
  const [content2, setContent2] = useState("");

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">RichTextEditor Test</h1>
      
      <div className="border p-4 rounded">
        <h2 className="text-lg font-semibold mb-4">Without Images</h2>
        <RichTextEditor
          label="Test Editor (No Images)"
          value={content1}
          onChange={setContent1}
          placeholder="Type something..."
          height="150px"
        />
      </div>

      <div className="border p-4 rounded">
        <h2 className="text-lg font-semibold mb-4">With Images</h2>
        <RichTextEditor
          label="Test Editor (With Images)"
          value={content2}
          onChange={setContent2}
          placeholder="Type something and try adding an image..."
          height="150px"
          enableImages={true}
          onImageUpload={uploadImageWithFallback}
        />
      </div>

      <div className="border p-4 rounded">
        <h3 className="text-lg font-semibold mb-2">Content Preview</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium">Without Images:</h4>
            <div className="bg-gray-100 p-2 rounded" dangerouslySetInnerHTML={{ __html: content1 }} />
          </div>
          <div>
            <h4 className="font-medium">With Images:</h4>
            <div className="bg-gray-100 p-2 rounded" dangerouslySetInnerHTML={{ __html: content2 }} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RichTextEditorTest;
