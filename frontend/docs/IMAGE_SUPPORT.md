# Image Support in Rich Text Descriptions

This document explains how to use the image functionality in rich text descriptions for exercises and questions.

## Features

### ✅ **Current Implementation**

- **Image Button**: Click the image icon in the rich text editor toolbar
- **File Upload**: Select images from your device (JPEG, PNG, GIF, WebP)
- **Base64 Storage**: Images are converted to base64 and stored inline
- **Responsive Display**: Images automatically resize to fit containers
- **Hover Effects**: Subtle shadow effects on image hover

### 🔄 **Available Options**

#### Option 1: URL Input (Default)

Users can insert images by providing a URL:

- Click the image button in the toolbar
- Enter the image URL when prompted
- Image will be embedded in the content

#### Option 2: File Upload (Current)

Users can upload files from their device:

- Click the image button in the toolbar
- Select a file from the file picker
- Image is converted to base64 and embedded

#### Option 3: Cloud Storage (Ready for Implementation)

For production use with cloud storage:

- Implement the `uploadToS3` function in `utils/imageUpload.ts`
- Images are uploaded to AWS S3 or similar service
- Only the URL is stored in the content

## Usage

### For Admins

#### Exercise Descriptions

```tsx
<RichTextEditor
  label="Description"
  value={description}
  onChange={setDescription}
  onImageUpload={handleImageUpload} // Optional custom handler
/>
```

#### Question Descriptions

```tsx
<RichTextEditor
  label="Question Description"
  value={questionDescription}
  onChange={setQuestionDescription}
  onImageUpload={handleImageUpload} // Optional custom handler
/>
```

### Custom Image Upload Handler

```tsx
const handleImageUpload = async (file: File): Promise<string> => {
  try {
    // Option 1: Base64 (current default)
    return await convertToBase64(file);

    // Option 2: Cloud upload (implement as needed)
    // return await uploadToS3(file);
  } catch (error) {
    console.error("Image upload failed:", error);
    throw error;
  }
};
```

## Implementation Details

### File Validation

- **Max Size**: 5MB (configurable)
- **Allowed Types**: JPEG, PNG, GIF, WebP
- **Compression**: Automatic compression for large images

### Image Processing

- **Max Width**: 1200px (images are resized if larger)
- **Quality**: 80% compression for JPEG images
- **Format Preservation**: Original format is maintained

### CSS Styling

Images in rich text content have the following styles:

```css
.rich-text-display img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin: 0.75rem 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
```

## Cloud Storage Setup (S3 Integration)

### ✅ **Current Implementation**

The application is now configured to use AWS S3 for image storage in the `/exercise-images` directory, reusing the same S3 bucket as audio files.

### Backend Setup

1. **Storage Service**: Extended `StorageService` with image upload methods
2. **Image Controller**: Created `/api/images/upload` endpoint with file validation
3. **Image Processing**: Includes Sharp.js for automatic image optimization
4. **S3 Integration**: Images stored in `exercise-images/` directory

### Frontend Integration

```typescript
// Automatic S3 upload with fallback
const handleImageUpload = async (file: File): Promise<string> => {
  try {
    return await uploadToS3(file); // Upload to S3
  } catch (error) {
    return await convertToBase64(file); // Fallback to base64
  }
};
```

### API Endpoints

- **POST** `/api/images/upload` - Upload image to S3
- **DELETE** `/api/images/:filename` - Delete image from S3

### Required Dependencies

```bash
# Backend dependencies
npm install multer sharp @types/multer

# Environment variables (already configured)
AWS_REGION=your-region
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=your-bucket-name
```

## Best Practices

### For Users

1. **Image Size**: Keep images under 2MB for best performance
2. **Dimensions**: Use images with reasonable dimensions (max 1200px width)
3. **Format**: Use JPEG for photos, PNG for graphics with transparency
4. **Alt Text**: Consider accessibility when using images

### For Developers

1. **Validation**: Always validate file size and type
2. **Compression**: Implement image compression for large files
3. **Storage**: Use cloud storage for production environments
4. **Fallback**: Provide fallback for failed uploads
5. **Security**: Validate file types on both client and server

## Troubleshooting

### Common Issues

1. **Large File Size**: Images over 5MB will be rejected
2. **Unsupported Format**: Only JPEG, PNG, GIF, WebP are supported
3. **Upload Failure**: Check network connection and server status

### Error Handling

The image upload includes automatic fallback to base64 if cloud upload fails:

```typescript
try {
  const imageUrl = await onImageUpload(file);
  // Use cloud URL
} catch (error) {
  // Fallback to base64
  const base64Url = await convertToBase64(file);
  // Use base64 URL
}
```

## Future Enhancements

### Planned Features

- [ ] Image resizing controls in editor
- [ ] Alt text input for accessibility
- [ ] Image alignment options (left, center, right)
- [ ] Image gallery/library for reusing images
- [ ] Drag and drop image upload
- [ ] Paste image from clipboard

### Performance Optimizations

- [ ] Lazy loading for images
- [ ] WebP format conversion
- [ ] CDN integration for faster loading
- [ ] Image optimization pipeline
