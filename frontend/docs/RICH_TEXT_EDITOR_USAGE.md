# RichTextEditor Usage Guide

## Overview

The RichTextEditor component provides rich text editing capabilities with optional image support. It's built on ReactQuill but designed to work seamlessly with React's virtual DOM.

## Basic Usage (No Images)

```tsx
import RichTextEditor from "../ui/input/RichTextEditor";

const MyComponent = () => {
  const [content, setContent] = useState("");

  return (
    <RichTextEditor
      label="Description"
      value={content}
      onChange={setContent}
      placeholder="Enter your text..."
      height="200px"
    />
  );
};
```

## Advanced Usage (With Images)

```tsx
import RichTextEditor from "../ui/input/RichTextEditor";
import { uploadImageWithFallback } from "../../utils/imageUpload";

const MyComponent = () => {
  const [content, setContent] = useState("");

  return (
    <RichTextEditor
      label="Description"
      value={content}
      onChange={setContent}
      placeholder="Enter your text..."
      height="200px"
      enableImages={true}
      onImageUpload={uploadImageWithFallback}
    />
  );
};
```

## Custom Image Upload Handler

```tsx
import { uploadToS3, convertToBase64 } from "../../utils/imageUpload";

const customImageUpload = async (file: File): Promise<string> => {
  try {
    // Try S3 upload first
    return await uploadToS3(file);
  } catch (error) {
    console.warn("S3 upload failed, using base64:", error);
    // Fallback to base64
    return await convertToBase64(file);
  }
};

// Use in component
<RichTextEditor
  enableImages={true}
  onImageUpload={customImageUpload}
  // ... other props
/>
```

## Props Reference

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Label for the editor |
| `placeholder` | `string` | - | Placeholder text |
| `value` | `string` | `""` | Current content |
| `onChange` | `(value: string) => void` | - | Change handler |
| `required` | `boolean` | `false` | Whether field is required |
| `disabled` | `boolean` | `false` | Whether editor is disabled |
| `error` | `string` | - | Error message to display |
| `className` | `string` | `""` | Additional CSS classes |
| `height` | `string` | `"200px"` | Editor height |
| `enableImages` | `boolean` | `false` | Enable image upload |
| `onImageUpload` | `(file: File) => Promise<string>` | - | Image upload handler |

## Available Formatting Options

- **Headers**: H1, H2, H3
- **Text Formatting**: Bold, Italic, Underline, Strikethrough
- **Lists**: Ordered and Unordered lists
- **Indentation**: Increase/Decrease indent
- **Links**: Insert hyperlinks
- **Images**: Upload and insert images (when enabled)
- **Clean**: Remove formatting

## Image Upload Strategies

### 1. S3 Upload with Base64 Fallback (Recommended)

```tsx
import { uploadImageWithFallback } from "../../utils/imageUpload";

<RichTextEditor
  enableImages={true}
  onImageUpload={uploadImageWithFallback}
/>
```

### 2. S3 Only

```tsx
import { uploadToS3 } from "../../utils/imageUpload";

<RichTextEditor
  enableImages={true}
  onImageUpload={uploadToS3}
/>
```

### 3. Base64 Only (For Testing)

```tsx
import { convertToBase64 } from "../../utils/imageUpload";

<RichTextEditor
  enableImages={true}
  onImageUpload={convertToBase64}
/>
```

### 4. Custom Upload Logic

```tsx
const customUpload = async (file: File): Promise<string> => {
  // Your custom upload logic here
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/custom-upload', {
    method: 'POST',
    body: formData,
  });
  
  const data = await response.json();
  return data.url;
};

<RichTextEditor
  enableImages={true}
  onImageUpload={customUpload}
/>
```

## Error Handling

The editor includes built-in error handling for image uploads:

- Shows "Uploading image..." text during upload
- Removes loading text on success/failure
- Shows alert on upload failure
- Gracefully falls back to base64 if S3 fails

## Best Practices

1. **Always provide fallback**: Use `uploadImageWithFallback` for production
2. **Validate file types**: The backend already validates image types
3. **Handle errors gracefully**: The component includes error handling
4. **Set appropriate height**: Adjust height based on expected content
5. **Use loading states**: The component shows upload progress automatically

## Troubleshooting

### Editor Not Displaying
- Check that ReactQuill CSS is imported
- Ensure no conflicting CSS styles
- Verify React version compatibility

### Images Not Working
- Ensure `enableImages={true}` is set
- Provide a valid `onImageUpload` handler
- Check backend image upload endpoint
- Verify authentication tokens

### Performance Issues
- Consider lazy loading for large content
- Optimize images before upload
- Use appropriate editor height

## Migration from Previous Version

If you were using the old version with image support:

```tsx
// Old (broken)
<RichTextEditor
  onImageUpload={handleImageUpload}
/>

// New (working)
<RichTextEditor
  enableImages={true}
  onImageUpload={uploadImageWithFallback}
/>
```
