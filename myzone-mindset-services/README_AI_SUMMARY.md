# AI Summary Feature for MyZone Mindset

This document explains how to set up and use the new AI summary feature that generates concise summaries of coachee exercise responses using OpenAI's GPT-4o mini model.

## Overview

The AI summary feature automatically generates a concise summary of a coachee's exercise responses when a coach views a completed exercise. This helps coaches quickly understand the key points from the coachee's responses without having to read through all the detailed answers.

## Setup Instructions

### 1. Install Required Dependencies

The backend now requires the OpenAI Python package. Install it by running:

```bash
pip install -r requirements.txt
```

### 2. Set Up Environment Variables

Create a `.env` file in the backend directory with your OpenAI API key:

```
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. Apply Database Migration

Run the following SQL migration to add the `ai_summary` column to the `exercise_assignments` table:

```bash
# Using psql or your preferred database tool
psql -U your_db_user -d your_db_name -f migrations/add_ai_summary_column.sql
```

## How It Works

1. When a coach views a completed exercise, the frontend checks if an AI summary already exists for that exercise.
2. If no summary exists, it automatically sends the coachee's responses to the backend API.
3. The backend formats the questions and responses and sends them to OpenAI's GPT-4o mini model.
4. The generated summary is returned to the frontend and stored in the database.
5. The summary is displayed to the coach in the exercise details page.

## API Endpoint

### Generate AI Summary

```
POST /api/generate-summary
```

Request body:

```json
{
  "questions": [
    {
      "id": "question_id",
      "question_text": "Question text",
      "question_type": "text|task|likert",
      "likert_scale_max": 5
    }
  ],
  "responses": [
    {
      "question_id": "question_id",
      "response_text": "Response text",
      "task_completed": true,
      "likert_response": 4
    }
  ]
}
```

Response:

```json
{
  "summary": "AI-generated summary of the responses"
}
```

## Troubleshooting

- If you encounter errors related to the OpenAI API, check that your API key is correctly set in the `.env` file.
- If the summary is not being generated, check the backend logs for any error messages.
- Ensure that the backend server is running and accessible from the frontend.
