from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import os
import io
import soundfile as sf
import numpy as np
import logging
from dotenv import load_dotenv
from pydub import AudioSegment
import openai
import replicate
import tempfile
import requests

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Initialize Replicate client
replicate_api = replicate.Client(api_token=os.environ["REPLICATE_API_KEY"])
# Get OpenAI API key from environment variable
openai_api_key = os.environ.get('OPENAI_API_KEY')
# Initialize OpenAI client
client = openai.OpenAI(api_key=openai_api_key)


def load_tts_model():
    """Load the Kokoro TTS model"""
    logger.info("Loading Kokoro TTS model...")
    # Replace local Kokoro model with Replicate API
    # tts_pipeline = KPipeline(lang_code='a')  # American English
    logger.info("Kokoro TTS model loaded successfully")
    return None

def generate_silence(duration, sample_rate=24000):
    """Generate silence for the specified duration in seconds."""
    num_samples = int(duration * sample_rate)
    return np.zeros(num_samples, dtype=np.float32)

def process_text_with_pauses(text):
    """Process text and replace pause instructions with silence."""
    segments = []
    remaining_text = text
    
    while True:
        # Find the next pause instruction
        pause_start = remaining_text.find('[Pause ')
        if pause_start == -1:
            break
            
        # Add text before pause
        segments.append(('text', remaining_text[:pause_start]))
        
        # Extract pause duration
        pause_end = remaining_text.find(']', pause_start)
        pause_str = remaining_text[pause_start+7:pause_end]
        
        # Remove any non-numeric characters (like 'seconds')
        pause_duration = float(''.join(filter(str.isdigit, pause_str)))
        
        segments.append(('silence', pause_duration))
        
        # Remove processed portion
        remaining_text = remaining_text[pause_end+1:]
    
    # Add remaining text
    if remaining_text:
        segments.append(('text', remaining_text))
    
    return segments

def process_text_with_replicate(text: str) -> dict:
    """Process text using Replicate's Kokoro model"""
    print("Processing text with Replicate...", text)
    try:
        output = replicate_api.run(
            "jaaari/kokoro-82m:f559560eb822dc509045f3921a1921234918b91739db4bf3daab2169b71c7a13",
            input={
                "text": text,
                "speed": 1.0,
                "voice": "af_nicole"
            }
        )
        return {
            'status': 'success',
            'result': output
        }
    except Exception as e:
        return {
            'status': 'error',
            'message': str(e)
        }

def generate_speech(text):
    """Generate speech using Replicate's Kokoro model"""
    print(f"Generating speech for text: {text}")
    result = process_text_with_replicate(text)
    if result['status'] != 'success':
        raise Exception(result['message'])
    
    # The Replicate API returns a URL to the generated audio
    audio_url = result['result']
    print(f"Audio URL from Replicate: {audio_url}")
    
    # Download the audio file
    response = requests.get(audio_url)
    if response.status_code != 200:
        raise Exception(f"Failed to download audio from {audio_url}, status code: {response.status_code}")
    
    # Create a temporary file to store the audio
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
    temp_file.write(response.content)
    temp_file.close()
    
    # Read the audio data
    audio_data, sample_rate = sf.read(temp_file.name)
    
    # Clean up the temporary file
    os.unlink(temp_file.name)
    
    return audio_data

@app.route('/api/tts', methods=['POST'])
def text_to_speech():
    """
    Convert text to speech using Replicate's Kokoro TTS model
    
    Request body:
    {
        "text": "Text to convert to speech",
        "voice": "optional voice ID"  # For future use
    }
    
    Returns:
    - Audio file in MP3 format
    """
    try:
        data = request.get_json()
        
        if not data or 'text' not in data:
            return jsonify({"error": "Missing 'text' parameter"}), 400
        
        text = data['text']
        
        if not text or not isinstance(text, str):
            return jsonify({"error": "Invalid 'text' parameter"}), 400
        
        logger.info(f"Processing TTS request: {text[:50]}{'...' if len(text) > 50 else ''}")
        
        segments = process_text_with_pauses(text)
        audio_segments = []
        
        for segment_type, content in segments:
            if segment_type == 'text':
                # Generate speech for text
                audio = generate_speech(content)
                audio_segments.append(audio)
            else:
                # Generate silence
                silence = generate_silence(content)
                audio_segments.append(silence)
        
        # Combine all audio segments
        final_audio = np.concatenate(audio_segments)
        
        # Create in-memory file-like object
        wav_io = io.BytesIO()
        sf.write(wav_io, final_audio, 24000, format='WAV')  # 24kHz sample rate
        wav_io.seek(0)
        
        # Convert WAV to MP3
        mp3_io = io.BytesIO()
        sound = AudioSegment.from_wav(wav_io)
        sound.export(mp3_io, format='mp3')
        mp3_io.seek(0)

        # Return audio file
        return send_file(
            mp3_io,
            mimetype='audio/mpeg',
            as_attachment=True,
            download_name='speech.mp3'
        )
        
    except Exception as e:
        logger.error(f"Error in text_to_speech: {str(e)}")
        return jsonify({"error": f"Failed to convert text to speech: {str(e)}"}), 500

@app.route('/api/process-text', methods=['POST'])
def process_text():
    data = request.get_json()
    text = data.get('text')
    if not text:
        return jsonify({'error': 'Text is required'}), 400

    result = process_text_with_replicate(text)
    return jsonify(result)

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy"})

@app.route('/api/generate-summary', methods=['POST'])
def generate_summary():
    """
    Generate an AI summary of exercise responses using OpenAI's GPT-4o mini model
    
    Request body:
    {
        "questions": [
            {
                "id": "question_id",
                "question_text": "Question text",
                "question_type": "text|task|likert",
                "likert_scale_max": 5  # Only for likert questions
            }
        ],
        "responses": [
            {
                "question_id": "question_id",
                "response_text": "Response text",  # For text questions
                "task_completed": true,  # For task questions
                "likert_response": 4  # For likert questions
            }
        ]
    }
    
    Returns:
    {
        "summary": "AI-generated summary of the responses"
    }
    """
    try:
        data = request.get_json()
        
        if not data or 'questions' not in data or 'responses' not in data:
            return jsonify({"error": "Missing required parameters"}), 400
        
        questions = data['questions']
        responses = data['responses']
        
        # Format the responses and questions for the AI
        formatted_data = []
        for question in questions:
            response = next((r for r in responses if r['question_id'] == question['id']), None)
            
            response_text = 'No response'
            if response:
                if question['question_type'] == 'text':
                    response_text = response.get('response_text', 'No text response')
                elif question['question_type'] == 'task':
                    response_text = 'Completed' if response.get('task_completed') else 'Not completed'
                elif question['question_type'] == 'likert':
                    response_text = f"Rating: {response.get('likert_response')} / {question.get('likert_scale_max', 5)}"
            
            formatted_data.append({
                'question': question['question_text'],
                'response': response_text
            })
        
        # Create the prompt for OpenAI
        prompt = f"""
        Please provide a concise summary of the following exercise responses:
        
        {chr(10).join([f"Question: {item['question']}{chr(10)}Response: {item['response']}" for item in formatted_data])}
        
        Provide a brief, insightful summary that captures the key points and insights from these responses.
        Keep your summary under 200 words and focus on the most important aspects.
        """
        
        # Call OpenAI API
        completion = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that summarizes exercise responses for coaches."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=300,
        )
        
        summary = completion.choices[0].message.content
        
        return jsonify({"summary": summary})
    
    except Exception as e:
        logger.error(f"Error in generate_summary: {str(e)}")
        return jsonify({"error": f"Failed to generate summary: {str(e)}"}), 500

if __name__ == '__main__':
    # Load the model on startup
    try:
        load_tts_model()
    except Exception as e:
        logger.warning(f"Failed to preload TTS model: {str(e)}")
        logger.warning("Will attempt to load model on first request")
    
    # Get port from environment variable or use default
    port = int(os.environ.get('PORT', 5001))
    
    app.run(host='0.0.0.0', port=port, debug=False)
