import requests
import argparse
import os
import tempfile
import subprocess

def text_to_speech(text, server_url="http://localhost:5000"):
    """
    Convert text to speech using the TTS API
    
    Args:
        text (str): Text to convert to speech
        server_url (str): URL of the TTS server
        
    Returns:
        str: Path to the saved audio file
    """
    endpoint = f"{server_url}/api/tts"
    
    try:
        # Make API request
        response = requests.post(
            endpoint,
            json={"text": text},
            headers={"Content-Type": "application/json"}
        )
        
        # Check for errors
        response.raise_for_status()
        
        # Save audio to temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        temp_file.write(response.content)
        temp_file.close()
        
        print(f"Audio saved to {temp_file.name}")
        return temp_file.name
        
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_data = e.response.json()
                print(f"Server error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"Server response: {e.response.text}")
        return None

def play_audio(file_path):
    """
    Play audio file using the default system player
    
    Args:
        file_path (str): Path to the audio file
    """
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist")
        return
    
    # Detect platform and use appropriate command
    if os.name == 'posix':  # macOS or Linux
        try:
            subprocess.run(['afplay', file_path], check=True)
        except FileNotFoundError:
            try:
                subprocess.run(['aplay', file_path], check=True)
            except FileNotFoundError:
                print("Could not find a suitable audio player. Please play the file manually.")
    elif os.name == 'nt':  # Windows
        try:
            os.startfile(file_path)
        except:
            print("Could not play the audio file automatically. Please play the file manually.")
    else:
        print("Unsupported platform. Please play the file manually.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test the TTS API")
    parser.add_argument("text", help="Text to convert to speech")
    parser.add_argument("--server", default="http://localhost:5000", help="TTS server URL")
    parser.add_argument("--play", action="store_true", help="Play the audio after conversion")
    
    args = parser.parse_args()
    
    audio_file = text_to_speech(args.text, args.server)
    
    if audio_file and args.play:
        play_audio(audio_file)
