# MyZone Mindset Text-to-Speech API

This backend service provides a text-to-speech API endpoint using the Kokoro TTS model from Hugging Face.

## Features

- Convert text to natural-sounding speech
- RESTful API endpoint
- Uses the [Kokoro-82M](https://huggingface.co/hexgrad/Kokoro-82M) model from Hugging Face

## Setup

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)

### Installation

1. Clone the repository
2. Install the required packages:

```bash
pip install -r requirements.txt
```

3. Start the server:

```bash
python app.py
```

The server will run on port 5000 by default. You can change this by setting the `PORT` environment variable.

## API Endpoints

### Convert Text to Speech

**Endpoint:** `POST /api/tts`

**Request Body:**

```json
{
  "text": "Text to convert to speech",
  "voice": "optional voice ID for future use"
}
```

**Response:**

- Audio file in WAV format

**Example Usage:**

```javascript
// Frontend example using fetch
async function convertTextToSpeech(text) {
  try {
    const response = await fetch("http://localhost:5001/api/tts", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ text }),
    });

    if (!response.ok) {
      throw new Error("Failed to convert text to speech");
    }

    // Create a blob from the audio data
    const audioBlob = await response.blob();

    // Create a URL for the blob
    const audioUrl = URL.createObjectURL(audioBlob);

    // Play the audio
    const audio = new Audio(audioUrl);
    audio.play();

    return audioUrl;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
}
```

### Health Check

**Endpoint:** `GET /api/health`

**Response:**

```json
{
  "status": "healthy",
  "model_loaded": true
}
```

## Deployment

For production deployment, it's recommended to use Gunicorn:

```bash
gunicorn app:app --bind 0.0.0.0:5000
```

## Notes

- The first request may take some time as the model needs to be loaded into memory
- The model requires approximately 1GB of RAM
